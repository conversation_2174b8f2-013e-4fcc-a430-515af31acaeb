#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息中心点位置测试工具
========================================
功能描述: 测试消息中心点判断法的可行性
创建时间: 2025-07-23
作者: AI Assistant

使用方法:
1. 确保模拟器运行并且在Instagram私信界面
2. 运行: python test_message_center_point.py
3. 程序会分析所有消息的中心点位置并给出判断

注意: 此程序只分析消息位置，不会进行任何操作
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.instagram_task import InstagramDMTask
from core.simple_config import get_config_manager
from core.logger_manager import log_info, log_error, log_warning


class MessageCenterPointTester:
    """消息中心点测试器"""

    def __init__(self, emulator_id: int = 2):
        """初始化测试器"""
        self.emulator_id = emulator_id
        self.config_manager = get_config_manager()
        self.instagram_task = None
        
    async def setup_tester(self):
        """设置测试器"""
        try:
            log_info(f"[消息中心点测试器] 设置测试器", component="MessageCenterPointTester")
            
            # 创建Instagram任务实例
            self.instagram_task = InstagramDMTask(self.emulator_id, self.config_manager)
            
            # 验证模拟器状态
            if not self.instagram_task.ld.is_running(self.emulator_id):
                log_error(f"[消息中心点测试器] 模拟器{self.emulator_id}未运行", component="MessageCenterPointTester")
                return False
            
            log_info(f"[消息中心点测试器] ✅ 测试器设置完成", component="MessageCenterPointTester")
            return True
            
        except Exception as e:
            log_error(f"[消息中心点测试器] 设置测试器异常: {e}", component="MessageCenterPointTester")
            return False

    def parse_bounds(self, bounds_str):
        """
        解析边界字符串
        
        Args:
            bounds_str: 边界字符串，格式如 "[35,323][128,361]"
            
        Returns:
            tuple: (left, top, right, bottom) 或 None
        """
        try:
            import re
            coords = re.findall(r'\d+', bounds_str)
            if len(coords) >= 4:
                left = int(coords[0])
                top = int(coords[1])
                right = int(coords[2])
                bottom = int(coords[3])
                return (left, top, right, bottom)
            return None
        except Exception as e:
            log_warning(f"[消息中心点测试器] 边界解析失败: {e}", component="MessageCenterPointTester")
            return None

    def calculate_center_point(self, left, top, right, bottom):
        """
        计算消息中心点

        Args:
            left, top, right, bottom: 消息边界坐标

        Returns:
            tuple: (center_x, center_y)
        """
        center_x = (left + right) / 2
        center_y = (top + bottom) / 2
        return (center_x, center_y)

    def calculate_long_press_position(self, left, top, right, bottom):
        """
        计算长按位置（模拟主程序的长按位置计算逻辑）

        Args:
            left, top, right, bottom: 消息边界坐标

        Returns:
            tuple: (long_press_x, long_press_y)
        """
        # 按照主程序的逻辑计算长按位置
        center_x = (left + right) / 2  # 水平居中

        # 动态计算消息高度的1/4位置
        height = bottom - top
        quarter_height = height // 4

        # 长按位置：水平居中，垂直在上方1/4处
        long_press_x = center_x  # 保持水平居中
        long_press_y = top + quarter_height  # 上方1/4处

        return (long_press_x, long_press_y)

    def judge_message_owner_by_center(self, center_x, screen_width):
        """
        基于中心点判断消息归属

        Args:
            center_x: 消息中心点X坐标
            screen_width: 屏幕宽度

        Returns:
            str: "自己的消息" 或 "对方的消息"
        """
        screen_center = screen_width / 2

        if center_x > screen_center:
            return "自己的消息"
        else:
            return "对方的消息"

    def judge_message_owner_by_long_press(self, long_press_x, screen_width):
        """
        基于长按位置判断消息归属（与主程序长按逻辑一致）

        Args:
            long_press_x: 长按位置X坐标
            screen_width: 屏幕宽度

        Returns:
            str: "自己的消息" 或 "对方的消息"
        """
        screen_center = screen_width / 2

        if long_press_x > screen_center:
            return "自己的消息"
        else:
            return "对方的消息"

    def analyze_message_position(self, message_element, screen_width, screen_height, index):
        """
        分析单个消息的位置信息
        
        Args:
            message_element: 消息元素
            screen_width: 屏幕宽度
            screen_height: 屏幕高度
            index: 消息索引
            
        Returns:
            dict: 分析结果
        """
        try:
            bounds = message_element.get('bounds', '')
            message_type = message_element.get('message_type', 'unknown')
            text = message_element.get('text', '')[:30] + '...' if len(message_element.get('text', '')) > 30 else message_element.get('text', '')
            
            if not bounds:
                return {
                    'index': index,
                    'type': message_type,
                    'text': text,
                    'error': '无边界信息'
                }
            
            # 解析边界
            coords = self.parse_bounds(bounds)
            if not coords:
                return {
                    'index': index,
                    'type': message_type,
                    'text': text,
                    'error': '边界解析失败'
                }
            
            left, top, right, bottom = coords
            
            # 计算中心点
            center_x, center_y = self.calculate_center_point(left, top, right, bottom)

            # 计算长按位置（模拟主程序逻辑）
            long_press_x, long_press_y = self.calculate_long_press_position(left, top, right, bottom)

            # 基于中心点判断消息归属
            owner_by_center = self.judge_message_owner_by_center(center_x, screen_width)

            # 基于长按位置判断消息归属（与主程序一致）
            owner_by_long_press = self.judge_message_owner_by_long_press(long_press_x, screen_width)
            
            # 计算相关比例
            screen_center = screen_width / 2
            center_offset = center_x - screen_center
            long_press_offset = long_press_x - screen_center
            center_ratio = center_x / screen_width
            width = right - left
            height = bottom - top
            width_ratio = width / screen_width
            quarter_height = height // 4

            return {
                'index': index,
                'type': message_type,
                'text': text,
                'bounds': bounds,
                'left': left,
                'top': top,
                'right': right,
                'bottom': bottom,
                'center_x': center_x,
                'center_y': center_y,
                'long_press_x': long_press_x,
                'long_press_y': long_press_y,
                'width': width,
                'height': height,
                'quarter_height': quarter_height,
                'screen_center': screen_center,
                'center_offset': center_offset,
                'long_press_offset': long_press_offset,
                'center_ratio': center_ratio,
                'width_ratio': width_ratio,
                'owner_by_center': owner_by_center,
                'owner_by_long_press': owner_by_long_press,
                'confidence': abs(long_press_offset) / screen_center  # 基于长按位置的置信度
            }
            
        except Exception as e:
            return {
                'index': index,
                'type': message_type,
                'text': text,
                'error': f'分析异常: {e}'
            }

    async def test_center_point_method(self):
        """测试中心点判断方法"""
        try:
            log_info(f"[消息中心点测试器] 开始测试中心点判断方法", component="MessageCenterPointTester")
            
            # 获取屏幕尺寸
            screen_width, screen_height = self.instagram_task.ld.get_screen_size(self.emulator_id)
            screen_center = screen_width / 2
            
            print(f"\n📱 屏幕信息:")
            print(f"  屏幕尺寸: {screen_width} × {screen_height}")
            print(f"  屏幕中心: X = {screen_center}")
            
            # 获取所有消息元素
            message_elements = await self.instagram_task._find_all_message_elements()
            
            if not message_elements:
                print("❌ 未找到任何消息元素")
                return False
            
            print(f"\n🔍 找到 {len(message_elements)} 条消息，开始分析:")
            print("=" * 80)
            
            # 分析每条消息
            results = []
            for i, element in enumerate(message_elements):
                result = self.analyze_message_position(element, screen_width, screen_height, i + 1)
                results.append(result)

            # 显示分析结果
            own_messages_center = 0
            other_messages_center = 0
            own_messages_long_press = 0
            other_messages_long_press = 0

            for result in results:
                if 'error' in result:
                    print(f"消息 {result['index']:2d}: ❌ {result['error']}")
                    continue

                # 统计消息归属（中心点方法）
                if result['owner_by_center'] == "自己的消息":
                    own_messages_center += 1
                    center_icon = "🟦"
                else:
                    other_messages_center += 1
                    center_icon = "🟨"

                # 统计消息归属（长按位置方法）
                if result['owner_by_long_press'] == "自己的消息":
                    own_messages_long_press += 1
                    long_press_icon = "🟦"
                else:
                    other_messages_long_press += 1
                    long_press_icon = "🟨"

                print(f"消息 {result['index']:2d}:")
                print(f"         类型: {result['type']}")
                if result['text']:
                    print(f"         文本: '{result['text']}'")
                print(f"         边界: {result['bounds']}")
                print(f"         尺寸: 宽度={result['width']}px, 高度={result['height']}px")
                print(f"         中心点: ({result['center_x']:.1f}, {result['center_y']:.1f})")
                print(f"         长按位置: ({result['long_press_x']:.1f}, {result['long_press_y']:.1f}) [水平居中, 上方1/4处]")
                print(f"         长按计算: 上边界={result['top']}, 1/4高度={result['quarter_height']}, 长按Y={result['top']}+{result['quarter_height']}={result['long_press_y']:.1f}")
                print(f"         中心点判断: {center_icon} {result['owner_by_center']} (偏移={result['center_offset']:+.1f})")
                print(f"         长按位置判断: {long_press_icon} {result['owner_by_long_press']} (偏移={result['long_press_offset']:+.1f})")
                print(f"         置信度: {result['confidence']:.2f} (基于长按位置)")
                print("-" * 80)
            
            # 显示统计结果
            print(f"\n📊 统计结果对比:")
            print(f"  📍 中心点判断法:")
            print(f"    🟦 自己的消息: {own_messages_center} 条")
            print(f"    🟨 对方的消息: {other_messages_center} 条")
            print(f"  📍 长按位置判断法 (与主程序一致):")
            print(f"    🟦 自己的消息: {own_messages_long_press} 条")
            print(f"    🟨 对方的消息: {other_messages_long_press} 条")
            print(f"  📱 屏幕中心线: X = {screen_center}")

            # 显示判断规律
            print(f"\n🎯 两种判断方法对比:")
            print(f"  📍 中心点判断法:")
            print(f"    规则: 如果消息中心点 > {screen_center}，则为自己的消息")
            print(f"    计算: center_x = (left + right) / 2")
            print(f"  📍 长按位置判断法 (主程序使用):")
            print(f"    规则: 如果长按X坐标 > {screen_center}，则为自己的消息")
            print(f"    计算: long_press_x = center_x (水平居中)")
            print(f"    计算: long_press_y = top + height/4 (上方1/4处)")

            # 验证判断准确性
            print(f"\n✅ 判断方法验证:")
            print(f"  🔍 关键发现:")
            print(f"    - 长按位置的X坐标 = 消息中心点的X坐标")
            print(f"    - 两种方法的水平判断逻辑完全相同")
            print(f"    - 长按位置判断法与主程序撤回逻辑一致")
            print(f"  📱 Instagram消息布局特点:")
            print(f"    - 自己的消息通常右对齐，长按位置在屏幕右侧")
            print(f"    - 对方的消息通常左对齐，长按位置在屏幕左侧")
            print(f"    - 屏幕中心线是天然的分界线")
            
            return True
                
        except Exception as e:
            log_error(f"[消息中心点测试器] 测试中心点判断方法异常: {e}", component="MessageCenterPointTester")
            return False

    async def run_test(self):
        """运行完整的测试流程"""
        try:
            print("🔍 Instagram消息中心点位置测试工具")
            print("=" * 60)
            print("此工具会分析所有消息的中心点位置并验证判断方法")
            print("=" * 60)
            
            # 1. 设置测试器
            if not await self.setup_tester():
                print("❌ 测试器设置失败")
                return
            
            # 2. 测试中心点判断方法
            success = await self.test_center_point_method()
            
            # 3. 显示测试结果
            print("\n" + "=" * 60)
            print("测试结果:")
            print("=" * 60)
            
            if success:
                print("✅ 长按位置判断方法测试成功!")
                print("✅ 消息位置分析完成")
                print("✅ 判断规律验证完成")
                print("✅ 与主程序长按逻辑一致性验证完成")
                print("\n💡 使用建议:")
                print("  1. 长按位置判断法与主程序完全一致")
                print("  2. 长按X坐标 = 消息中心点X坐标")
                print("  3. 适用于各种长度的消息")
                print("  4. 基于Instagram固定的布局规律")
                print("  5. 可以在撤回功能中使用此方法进行预判断")
            else:
                print("❌ 中心点判断方法测试失败")
                print("❌ 可能的问题:")
                print("  1. 界面不在正确的Instagram私信页面")
                print("  2. 没有足够的消息进行分析")
                print("  3. 消息元素获取失败")
            
        except Exception as e:
            log_error(f"[消息中心点测试器] 运行测试异常: {e}", component="MessageCenterPointTester")


async def main():
    """主函数"""
    try:
        print("Instagram消息中心点位置测试工具")
        print("此工具会分析消息的中心点位置并验证判断方法")
        print("⚠️  注意: 此程序只分析消息位置，不会进行任何操作")
        
        confirm = input("确认继续？(y/N): ").strip().lower()
        if confirm != 'y':
            print("测试已取消")
            return
        
        tester = MessageCenterPointTester(emulator_id=2)
        await tester.run_test()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
