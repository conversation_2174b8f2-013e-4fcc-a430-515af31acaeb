# _*_coding: utf_8*_
import logging
import os
import random
import re
import threading
from typing import Union, Tuple, List
import pyperclip
import win32gui
from PyQt6.QtWidgets import QWidget, QTextEdit, QVBoxLayout, QApplication, \
	QMessageBox

from LeiDian import Dnconsole
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt
import win32con
from logger_config import setup_logger

logger = setup_logger("Emulator", debug_mode=True)


class EmulatorThread(QThread):
	# 修改信号定义，添加一个字典类型的参数来传递额外信息
	task_finished = pyqtSignal(int, int, str,
							   dict)  # 用于发送任务完成信号，携带模拟器ID、行号、任务名称和额外参数（字典类型）
	thread_stopped = pyqtSignal()  # 新增线程停止信号
	task_log = pyqtSignal(str, int)  # 用于发送任务日志信号，携带模拟器ID、消息和行号
	emulator_info_updated = pyqtSignal(str, int, list, list)  # 发送模拟器详细信息
	save_last_device = pyqtSignal(int, int)  # 保存最后一个设备
	user_status_updated = pyqtSignal(int, int,
									 str)  # 发送用户状态更新信号，携带模拟器ID、行号和用户状态
	sortWdnd = pyqtSignal()  # 发送排序信号，携带模拟器ID、行号
	# 新增日志窗口信号
	log_window_created = pyqtSignal(int, object)  # 参数1: 模拟器句柄，参数2: 日志窗口对象
	
	def __init__(self, emulator_id, row, ui_window=None):
		super().__init__()
		self.stop_flag = False
		self.emulator_id = emulator_id
		self.row = row
		self.ui = ui_window
		# self.db = self.ui.db
		self.hwnd = self.ui.hwnd
		self.hwnd_top = None
		self.display_process = None  # 添加进程实例变量
		self.is_running = False
		self.emulator_is_running = False
		self.task_name = None
		self.task_args = {}
		self.ld_base_path = self.ui.ld_base_path or "D:/LDPlayer9.0.66"
		self.ld_share_path = self.ui.ld_share_path or "C:/Users/<USER>/Documents/leidian64"
		self.ld_exe_path = os.path.join(self.ld_base_path, "ld.exe")

		self.command_timeout = 15  # 增加全局超时设置
		self.last_activity = None
		self.xml_cache = None
		self.result = ''
		self.lock = threading.Lock()
		self.cmd_lock = threading.Lock()  # 添加一个用于执行 cmd 命令的锁
		
		self.订阅列表为空 = False
		self.窗口已排列 = False
		# 连接UI的任务停止信号
		self.ui.task_stop_signal.connect(self.stop_task)
		# 任务超时时间初始化
		self.timeout_total = self.ui.timeout_total or 900
		self.timeout_emulator = self.ui.timeout_v2ray or 300
		self.timeout_v2ray = self.ui.timeout_v2ray or 600
		self.timeout_v2ray_node = self.ui.timeout_v2ray_node or 300  # v2ray节点判断超时时间
		self.timeout_ins_shouye = self.ui.timeout_ins_shouye or 300
		self.timeout_ins_zhuangtai = self.ui.timeout_ins_zhuangtai or 30
		self.timeout_total_fb = 600
		self.timeout_other = 300  # 单位 秒
		self.v2ray_重启次数 = 0
		self.寻找节点列表最大次数 = 20
		self.检测节点ping最大次数 = 5
		self.uninstall_app = False  # 重装v2ray
		self.update_ins = False  # 更新ins版本
		self.is_update_node = None
		self.set_default_font_size = False
		# 初始化任务参数集合
		self.task_args_collection = {
			'com.v2ray.ang': {'package_name': 'com.v2ray.ang',
							  'filepath': 'D:/v2rayNG_1.1.12.apk'},
			'com.instagram.android': {'package_name': 'com.instagram.android',
									  'filepath': 'D:/Instagram.apk'},
		}
		# ================== 初始化参数 ==================
		self.v2ray_packageName = 'com.v2ray.ang'
		self.ins_packageName = 'com.instagram.android'
		self.fb_packageName = 'com.facebook.katana'
		
		self.swipe_actions = [
			{"direction": "up", "area": (200, 700, 400, 200)},
			{"direction": "down", "area": (200, 250, 400, 600)},  # 下滑
			# {"direction": "random", "area": (300, 600, 400, 100)}  # 随机抖动
		]
		self.swipe_duration = random.randint(500, 1000)  # 随机滑动速度
		# 预定义的模板路径
		self.templates = {
			"node_item": r'img/s.png',  # 节点列表项标识
			"connected": r'img/l1.png',  # 已连接状态标识
			"disconnected": r'img/l2.png',  # 未连接状态标识
			"加号按钮": r'img/jiahao.png',  # 右上方加号按钮
			"导入按钮": r'img/daoru.png',  # 导入连接按钮
			"右上角3个点": r'img/three_point.png',
			"更新订阅": r'img/update.png',
			# 请确认是否需要使用相同的图片，如果不需要请更换为正确的图片路径
			"confirm_connect": r'img/confirm_connect.png',
			"conn_sucess": r'img/chenggong.png',
			"conn_failed": r'img/shibai.png',
			"conn_connected": r'img/yilianjie.png',
			"INS_重试": r'img/chongshi.bmp',
			"INS_首页": r'img/shouye.png',
			"INS_身份连接": r'img/shenfenjixu.png',
			"雷电图标": r'img/leidian.png',
			"v2ray图标": r'img/v2ray.png',
			"INS图标": r"img/ins.png",
			"INS本人": r"img/benren.bmp",
			"FB_图标": r"img/facebook.png",
			"FB_会话过期确定": r"img/huihuaqueding.bmp",
			"FB_首页_继续": r"img/fbjixu.bmp.bmp",
			"FB_首页_继续1": r"img/jixu.bmp",
		}
		# 预定义的点击区域（根据实际界面调整）
		self.button_regions = {
			"node_list": (62, 69, 193, 449),  # 节点列表区域 (x1,y1,x2,y2)
			"connect_btn": (235, 445, 260, 461),  # 窗口视图坐标
			"connect_btn_2": (456, 844, 492, 875),  # 连接按钮区域 这个需要在原始分辨率下的坐标
			"ping_btn": (6, 464, 142, 497),  # 连接按钮区域
			"INS_APP_我的按钮区域": (475, 917, 497, 933),  # INSapp 我的按钮区域
		}
		
		self.已连接状态颜色HSV = (80, 255, 153)
		self.ld = Dnconsole(self.ld_base_path, self.ld_share_path,em=self)
		print("EmulatorThread 初始化完毕")
	
	def stop_task(self, _=None):  # 添加可选参数接收多余参数
		self.stop_flag = True
	
	def set_task(self, task_name, **task_args):
		"""设置任务并触发延迟执行"""
		self.stop_flag = False
		self.task_name = task_name
		self.task_args = task_args
		# 处理 package_name 和 filepath 参数
		package_names = task_args.get('package_name', '').split('|')
		filepaths = task_args.get('filepath', '').split('|')
		specific_package = task_args.get('specific_package', None)
		if specific_package:
			package_names = [specific_package]
			filepaths = [task_args.get('filepath', '')]
		
		if len(package_names) != len(filepaths) and task_name in [
			"apk_install"]:
			self.sendLog(
				f"模拟器 {self.emulator_id} 包名和文件路径数量不匹配，任务设置失败")
			return False
		
		# 智能创建任务参数集合
		for package_name, filepath in zip(package_names, filepaths):
			if package_name and filepath:
				self.task_args_collection[package_name] = {
					"package_name": package_name,
					"filepath": filepath
				}
		logger.info(self.task_args_collection)
		# 更新 task_args
		task_args['package_name'] = '|'.join(package_names)
		task_args['filepath'] = '|'.join(filepaths)
		self.node_client = task_args.get('node_client', '')
		self.v2ray_manager = V2rayManager(self, **task_args)
		self.emulatorStarter = EmulatorStarter(self, {
			'max_retries': 3,
			'retry_interval': 5,
			'desktop_validation_timeout': 50
		})
	
	def run(self):
		"""执行任务"""
		logger.info(f"线程 {self.emulator_id} 开始执行任务 {self.task_name}")
		if self.is_running:
			logger.info(f"任务 {self.task_name} 已经在执行，跳过...")
			return
		self.is_running = True
		try:
			# 根据任务类型执行任务
			if self.task_name == "start_emulator":
				self.result = self.start_emulator()
			elif self.task_name == "stop_emulator":
				self.stop_emulator()
			elif self.task_name == "pause_emulator":
				self.pause_emulator()
			elif self.task_name == "resume_emulator":
				self.resume_emulator()
			elif self.task_name == "restart_emulator":
				self.restart_emulator()
			elif self.task_name == "delete_emulator":
				self.delete_emulator()
			elif self.task_name == "apk_install":
				self.install_apk_file(**self.task_args)
			elif self.task_name == "apk_uninstall":
				self.uninstall_apk(**self.task_args)
			elif self.task_name == "app_run":
				self.start_app(**self.task_args)
			elif self.task_name == "app_kill":
				self.kill_app(**self.task_args)
			elif self.task_name == "root_open":
				self.root_emulator_open()
			elif self.task_name == "root_close":
				self.root_emulator_close()
			elif self.task_name == 'push_file':
				self.push_file(**self.task_args)
			elif self.task_name == 'pull_file':
				self.pull_file(**self.task_args)
			elif self.task_name == '修改桥接模式':
				self.modifyBridge_emulators(**self.task_args)
			elif self.task_name == 'check_app_task':
				self.check_app_task()
			elif self.task_name == 'check_app_task_2':
				self.check_app_task_2()
			elif self.task_name == '直接关注用户':
				self.INS_follow()
			elif self.task_name == '关注用户粉丝':
				self.INS_follow()
			elif self.task_name == '私信':
				self.INS_follow()
			
			# 其他任务...
			self.is_running = False
		
		finally:
			self.quit()  # 确保线程退出
			self.wait()  # 等待线程完全退出
		
		result = "任务完成"
		extra_info = "一些额外信息"
		task_args = {
			"result": result,
			"extra_info": extra_info
		}
		self.task_finished.emit(self.emulator_id, self.row, self.task_name,
								task_args)
	
	def stop_emulator(self):
		"""停止模拟器"""
		if not self.emulator_is_running:
			self.sendLog(f"模拟器 {self.emulator_id} 未启动，无需停止")
		# self.task_finished.emit(0, 0, self.task_name)
		else:
			logger.info(
				f"Stopping emulator {self.emulator_id}, is_running: {self.is_running}, emulator_is_running: {self.emulator_is_running}")
			self.ld.quit(self.emulator_id)
			self.emulator_info_updated.emit(str(self.emulator_id),
											self.row,
											['未启动'], [6])
			self.sendLog(f"模拟器 {self.emulator_id} 停止成功")
			self.emulator_is_running = False
			self.is_running = False
	
	# logger.info(f'准备发射任务完成信号')
	# time.sleep(1)
	# self.task_finished.emit(self.emulator_id, self.row, self.task_name)
	
	def pause_emulator(self):
		"""暂停模拟器"""
		logger.info(f"模拟器 {self.emulator_id} 暂停")
		self.safe_sleep(2)  # 模拟暂停延迟
	
	def resume_emulator(self):
		"""恢复模拟器"""
		logger.info(f"模拟器 {self.emulator_id} 恢复")
		self.safe_sleep(2)  # 模拟恢复延迟
	
	def start_emulator(self):
		"""启动模拟器"""
		self.窗口已排列 = False
		logger.info(f"模拟器 {self.emulator_id} 启动")
		
		# 初始化状态变量
		is_running, is_android, __ = self.ld.is_running(self.emulator_id)
		timeout_total = 100  # 总超时时间设置为5分钟
		retry_interval = 5  # 重试间隔5秒
		
		# 阶段1：检查已有运行实例
		if is_running and is_android:
			return self._handle_existing_instance()
		
		# 阶段2：启动新实例
		try:
			self.sendLog(f"模拟器 {self.emulator_id} 启动中")
			start_time = time.time()
			self.ld.launch(self.emulator_id)
			
			# 阶段3：等待启动完成
			while not self.stop_flag and (
					time.time() - start_time < timeout_total):
				try:
					# 获取最新状态
					is_running, is_android, result = self.ld.is_running(
						self.emulator_id)
					
					# 成功启动判断
					if is_running and is_android:
						return self._finalize_startup(result)
					
					# 超时判断（提前10秒预警）
					elapsed = time.time() - start_time
					if elapsed > (timeout_total - 10):
						self.sendLog(
							f"模拟器 {self.emulator_id} 即将超时，剩余时间：{timeout_total - elapsed:.1f}秒")
					
					self.safe_sleep(retry_interval)
				
				except Exception as e:
					logger.error(f"状态检查异常: {str(e)}")
					self.safe_sleep(1)
			
			# 处理超时
			if (time.time() - start_time) >= timeout_total:
				self.sendLog(
					f"模拟器 {self.emulator_id} 启动超时（{timeout_total}秒）")
				self.ld.quit(self.emulator_id)  # 强制关闭
				return False
		
		except Exception as e:
			self.sendLog(f"模拟器 {self.emulator_id} 启动失败，原因：{str(e)}")
			return False
	
	# 新增两个辅助方法
	def _handle_existing_instance(self):
		"""处理已存在的运行实例"""
		self.emulator_is_running = True
		self.sendLog(f"模拟器 {self.emulator_id} 已在运行")
		info = self.ld.get_hwnd(self.emulator_id)
		print(f'info: {info}')
		if info and info[1] > 0:
			self._setup_window(info)
			print(f'{self.hwnd}')
			return True
		return False
	
	def _finalize_startup(self, result):
		"""完成启动后处理"""
		self.emulator_is_running = True
		info_list = result.split(',')[2:5]  # 提取名称、状态、类型
		self._setup_window(self.ld.get_hwnd(self.emulator_id))
		self.emulator_info_updated.emit(str(self.emulator_id), self.row,
										['已启动'], [6])
		self.emulator_info_updated.emit(str(self.emulator_id), self.row,
										info_list, [3, 4, 5])
		return True
	
	def _setup_window(self, info):
		"""窗口初始化设置"""
		if info:
			self.hwnd = info[1]
			self.width = info[2]
			self.height = info[3]
			self.hwnd_top = info[0]
			if self.hwnd > 0 and not self.窗口已排列:
				self.ui.hwnd_list.append(self.hwnd_top)
				self.sortWdnd.emit()
				self.窗口已排列 = True
				
				# 在主线程安全创建日志窗口
				def create_window():
					log_win = LogWindow(self.hwnd_top)  # 必须传入主窗口作为parent
					log_win.move(log_win.pos().x(),
								 log_win.pos().y() + 30)  # 增加位置偏移
					log_win.show()
					log_win.raise_()  # 关键修复：强制窗口置顶
					log_win.activateWindow()
	
	def restart_emulator(self):
		"""重启模拟器"""
		if self.emulator_is_running:
			self.sendLog(f"模拟器 {self.emulator_id} 正在重启")
			self.ld.reboot(self.emulator_id)
			self.safe_sleep(5)
			self.start_emulator()  # 重启后重新启动模拟器
		else:
			self.sendLog(f"模拟器 {self.emulator_id} 未启动，无需重启")
	
	def delete_emulator(self):
		"""删除模拟器"""
		self.stop_emulator()
		self.safe_sleep(1)
		self.ld.remove(self.emulator_id)
		self.sendLog(f"模拟器 {self.emulator_id} 已删除")
	
	def install_apk_file22(self, **kwargs):
		"""通过文件名安装App"""
		if self.emulator_is_running:
			package_names = kwargs.get('package_name', '').split('|')
			filepaths = kwargs.get('filepath', '').split('|')
			
			logger.info(
				f'模拟器 {self.emulator_id} 开始安装App: {package_names}, 文件路径: {filepaths}')
			if len(package_names) != len(filepaths):
				self.sendLog(
					f"模拟器 {self.emulator_id} 包名和文件路径数量不匹配，安装失败")
				return False
			
			for package_name, filepath in zip(package_names, filepaths):
				self.sendLog(
					f"模拟器 {self.emulator_id} 开始安装App: {package_name}")
				self.ld.installappOfFile(self.emulator_id, filepath)
				start_time = time.time()
				while not self.stop_flag:
					logger.info(
						f"正在检查应用安装状态，index={self.emulator_id}, packagename={package_name},filepath={filepath}...")
					result = self.ld.appVersion(self.emulator_id,
												package_name)  # 模拟检查
					if result:
						self.sendLog(
							f"模拟器 {self.emulator_id} 安装App {package_name} 成功,版本号:{result}")
						break
					else:
						# logger.info(f"应用 {package_name} 未安装")
						self.safe_sleep(2)
						if self.check_timeout(start_time):
							self.sendLog(
								f"模拟器 {self.emulator_id} 安装App {package_name} 超时，安装失败")
							break
			
			return True
		else:
			self.sendLog(f"模拟器 {self.emulator_id} 未启动，无法安装App")
			return False
	
	def uninstall_apk22(self, **kwargs):
		"""通过包名卸载App"""
		if self.emulator_is_running:
			package_names = kwargs.get('package_name', '').split('|')
			for package_name in package_names:
				if not package_name:
					continue
				self.sendLog(
					f"模拟器 {self.emulator_id} 开始卸载App: {package_name}")
				self.ld.uninstallapp(self.emulator_id, package_name)
				start_time = time.time()
				while not self.stop_flag:
					logger.info(
						f"正在检查应用卸载状态，index={self.emulator_id}, packagename={package_name}...")
					result = self.ld.appVersion(self.emulator_id,
												package_name)  # 模拟检查
					# result = result.strip()
					if not result:
						self.sendLog(
							f"模拟器 {self.emulator_id} 已卸载App {package_name}")
						break
					else:
						# logger.info(f"应用 {package_name} 未安装")
						self.safe_sleep(2)
						if self.check_timeout(start_time):
							self.sendLog(
								f"模拟器 {self.emulator_id} 卸载App {package_name} 超时")
							break
		else:
			self.sendLog(f"模拟器 {self.emulator_id} 未启动，无法卸载App")
	
	# ... existing code ...
	
	def install_apk_file(self, **kwargs):
		"""通过文件名安装App"""
		if self.emulator_is_running:
			package_names = kwargs.get('package_name', '').split('|')
			filepaths = kwargs.get('filepath', '').split('|')
			specific_package = kwargs.get('specific_package', None)
			if specific_package:
				package_names = [specific_package]
				# 假设 specific_package 对应一个文件路径，如果没有提供，这里可以根据实际情况处理
				filepaths = [kwargs.get('filepath', '')]
			
			if len(package_names) != len(filepaths):
				self.sendLog(
					f"模拟器 {self.emulator_id} 包名和文件路径数量不匹配，安装失败")
				return False
			
			for package_name, filepath in zip(package_names, filepaths):
				if not package_name or not filepath:
					continue
				self._perform_app_action(package_name, filepath, "install")
			return True
		else:
			self.sendLog(f"模拟器 {self.emulator_id} 未启动，无法安装App")
			return False
	
	def uninstall_apk(self, **kwargs):
		"""通过包名卸载App"""
		if self.emulator_is_running:
			package_names = kwargs.get('package_name', '').split('|')
			specific_package = kwargs.get('specific_package', None)
			if specific_package:
				package_names = [specific_package]
			for package_name in package_names:
				if not package_name:
					continue
				self._perform_app_action(package_name, None, "uninstall")
		else:
			self.sendLog(f"模拟器 {self.emulator_id} 未启动，无法卸载App")
	
	# ... existing code ...
	
	def _perform_app_action(self, package_name, filepath, action):
		if action == "install":
			self.sendLog(
				f"模拟器 {self.emulator_id} 开始安装App: {package_name}")
			with self.cmd_lock:  # 获取锁
				self.ld.installappOfFile(self.emulator_id, filepath)
			success_msg = f"模拟器 {self.emulator_id} 安装App {package_name} 成功,版本号:{{version}}"
			check_func = lambda: self.ld.appVersion(self.emulator_id,
													package_name)
		elif action == "uninstall":
			self.sendLog(
				f"模拟器 {self.emulator_id} 开始卸载App: {package_name}")
			with self.cmd_lock:  # 获取锁
				self.ld.uninstallapp(self.emulator_id, package_name)
			success_msg = f"模拟器 {self.emulator_id} 已卸载App {package_name}"
			# 修改部分：检查返回值是否为字符串类型
			check_func = lambda: not (isinstance(
				self.ld.appVersion(self.emulator_id, package_name),
				str) and self.ld.appVersion(self.emulator_id,
											package_name).strip())
		
		start_time = time.time()
		while not self.stop_flag:
			logger.info(
				f"正在检查应用 {action} 状态，index={self.emulator_id}, packagename={package_name}...")
			result = check_func()
			logger.info(f"版本查询结果: {result}")
			if action == "install" and result:
				self.sendLog(success_msg.format(version=result))
				break
			elif action == "uninstall" and not result:
				self.sendLog(success_msg)
				break
			else:
				self.safe_sleep(2)
				if self.check_timeout(start_time):
					self.sendLog(
						f"模拟器 {self.emulator_id} {action} App {package_name} 超时")
					break
	
	def start_app(self, **kwargs):
		"""通过包名启动App"""
		"""通过包名启动App"""
		if self.emulator_is_running:
			package_name = kwargs.get('package_name')
			self.sendLog(f"模拟器 {self.emulator_id} 开始启动App")
			self.ld.runApp(self.emulator_id, package_name)
			self.safe_sleep(2)
			with self.cmd_lock:  # 获取锁
				print(f'开始启动app {package_name}')
				self.ld.runApp(self.emulator_id, package_name)
				self.safe_sleep(2)
				start_time = time.time()
				while not self.stop_flag:
					logger.info(
						f"正在检查应用状态，index={self.emulator_id}, packagename={package_name}")
					result1, result2 = self.ld.appIsrunning(
						self.emulator_id,
						package_name)
					if result2 and result2 != "":
						self.sendLog(
							f"模拟器 {self.emulator_id} 已启动App {package_name}")
						return True
					
					else:
						self.safe_sleep(2)
						if self.check_timeout(start_time):
							self.sendLog(
								f"模拟器 {self.emulator_id} 启动App {package_name} 超时")
							break
				return False
	
	def kill_app(self, **kwargs):
		"""通过包名杀死App"""
		if self.emulator_is_running:
			package_name = kwargs.get('package_name')
			self.sendLog(f"模拟器 {self.emulator_id} 开始终止App")
			self.ld.killApp(self.emulator_id, package_name)
			self.safe_sleep(2)
			start_time = time.time()
			while not self.stop_flag:
				logger.info(
					f"正在检查应用状态,{self.task_name}...")
				with self.cmd_lock:  # 获取锁
					result1, result2 = self.ld.appIsrunning(self.emulator_id,
															package_name)  # 模拟检查
				if result2 != "":
					# logger.info(f"应用 {package_name} 未关闭")
					self.safe_sleep(2)
				else:
					self.sendLog(
						f"模拟器 {self.emulator_id} 已终止App {package_name}")
					break
				if self.check_timeout(start_time):
					break
		
		else:
			self.sendLog(f"模拟器 {self.emulator_id} 未启动，无法终止App")
	
	def root_emulator_open(self):
		"""模拟器Root"""
		self.ld.modifyOthers(self.emulator_id, 1, 1, 1)
		self.sendLog(f"模拟器 {self.emulator_id} 已开启Root,请重启模拟器")
	
	def root_emulator_close(self):
		"""模拟器Root"""
		self.ld.modifyOthers(self.emulator_id, 1, 1, 0)
		self.sendLog(f"模拟器 {self.emulator_id} 已关闭Root,请重启模拟器")
	
	def push_file(self, **kwargs):
		"""推送文件到模拟器"""
		host_path = kwargs.get('host_path')
		emulator_path = kwargs.get('emulator_path')
		self.sendLog(f"模拟器 {self.emulator_id} 已推送文件,等待完成...")
		result = self.ld.push_file(self.emulator_id, host_path, emulator_path)
		start_time = time.time()
		while not self.stop_flag:
			if 'KB/s' in result:
				self.sendLog(f"模拟器 {self.emulator_id} 文件传输完成")
				break
			else:
				self.safe_sleep(2)
				if self.check_timeout(start_time):
					break
	
	def pull_file(self, **kwargs):
		host_path = kwargs.get('host_path')
		emulator_path = kwargs.get('emulator_path')
		self.sendLog(f"模拟器 {self.emulator_id} 已拉取文件,等待完成...")
		result = self.ld.pull_file(self.emulator_id, emulator_path, host_path)
		start_time = time.time()
		while not self.stop_flag:
			if 'KB/s' in result:
				self.sendLog(f"模拟器 {self.emulator_id} 文件传输完成")
				break
			else:
				self.safe_sleep(2)
				if self.check_timeout(start_time):
					break
	
	
	def modifyBridge_emulators(self, **kwargs):
		"""修改模拟器桥接"""
		
		try:
			index = self.emulator_id
			row = self.row
			model = kwargs.get('model')
			logger.info(f'模拟器 {index} 桥接修改中...row={row},model={model}')
			self.ld.enable_bridge_mode(index, model)
			self.sendLog(f'模拟器 {index} 桥接修改成功')
		# self.update_emulator_info(row, index, ['桥接模式'], [6])
		except Exception as e:
			self.sendLog(f'模拟器 {index} 桥接修改失败: {e}', row)
	
	def input_text(self, msg):
		"""输入文本"""
		if self.emulator_is_running:
			self.ld.input_text(self.emulator_id, msg)
	
	# self.sendLog(f"模拟器 {self.emulator_id} 输入文本: {msg}")
	
	def get_appVersion(self, package_name):
		"""获取App版本号"""
		version = self.ld.appVersion(self.emulator_id, package_name)
		return version
	
	def sendLog(self, msg):
		self.task_log.emit(msg, self.row)
	
	def check_timeout(self, start_time, timeout=60):
		"""检查超时"""
		with self.lock:
			if time.time() - start_time > timeout:
				self.sendLog(f"任务超时，请检查模拟器是否正常启动")
				return True
			else:
				return False
	
	
		
		
		
	# 在Emulator类中添加新方法
	def safe_sleep(self, seconds):
		"""可中断的短间隔等待"""
		interval = 0.1  # 100ms的间隔
		for _ in range(int(seconds / interval)):
			if self.stop_flag:  # 只需要检测这一个标志位
				return False
			time.sleep(interval)
		return True
	
	################### 定制功能函数区################
	
	def INS_follow(self):
		"""
		INS关注功能
		:return:
		"""
		ins = InstagramAutomation(self)
		ins.log_signal.connect(self.sendLog)
		ins.run(None, **self.task_args)
	
	def check_app_task_2(self):
		"""
		分为2个阶段:
		1, 切换节点
		2,检测insapp状态

		:return:
		"""
		
		def switch_and_verify_node():
			""" 完整的节点切换流程 """
			# ========== 步骤1：随机选择节点 ==========
			# 定义几个异常的变量
			节点测试失败次数 = 0
			ping检验失败次数 = 0
			start_time = time.time()
			while not self.stop_flag:
				self.sendLog(
					f"正在寻找可用节点...已耗时{time.time() - start_time:.2f}秒")
				if time.time() - start_time >= self.timeout_v2ray_node:
					self.sendLog(f"节点测试失败次数过多，任务超时")
					self.user_status_updated.emit(self.emulator_id, self.row,
												  "v2ray异常")
					return False
				node_positions = _find_nodes()
				if not node_positions:
					节点测试失败次数 += 1
					self.sendLog(f"导入节点列表失败,{节点测试失败次数}次")
					# if 节点测试失败次数 >= self.寻找节点列表最大次数:
					# 	self.sendLog(
					# 		f"导入节点列表失败 {self.寻找节点列表最大次数}，任务失败")
					# 	return False
					self.safe_sleep(1)
					continue
				
				logger.info("随机点击节点成功")
				self.safe_sleep(3)
				# ========== 步骤2：检测并点击连接按钮 ==========
				connect_status = _check_connection_status()
				logger.info(f'connect_status: {connect_status}')
				if connect_status == "disconnected":
					self.sendLog("检测到未连接状态，尝试连接...")
					_click_connect_button()
					self.sendLog("开始测ping")
				elif connect_status == "connected":
					self.sendLog("当前已连接")
				# _click_connect_button()  # 先断开
				# self.safe_sleep(2)
				# _click_connect_button()  # 重新连接
				# ========== 步骤3：验证连接是否成功 ==========
				ping检验结果 = _verify_connection(
					max_ping_retry=self.检测节点ping最大次数)
				if not ping检验结果:
					continue
				self.sendLog("节点ping成功")
				return True
		
		def _check_connection_status(max_retry=10) -> str | None:  # 修改返回类型
			""" 检测连接状态 """
			retry = 0
			start_time = time.time()
			未连接计次 = 0
			while not self.stop_flag and retry < max_retry:
				超时检测 = self.check_timeout(start_time, 20)
				if 超时检测:
					self.sendLog(f"节点连接超时")
					return "timeout"
				try:
					screenshot = self.ld.capture_window_back(
						hwnd=self.hwnd
					)
					if screenshot is None:
						self.sendLog(
							f"[第 {retry + 1} 次重试] 节点列表区域截图失败")
						continue
				except Exception as e:
					self.sendLog(f"[第 {retry + 1} 次重试] 截图异常: {str(e)}")
					continue
				connected = self.ld.find_color(screenshot,
											   self.已连接状态颜色HSV, 0.9, 10,
											   region=self.button_regions[
												   "connect_btn"])
				if connected:
					logger.info("已连接")
					return "connected"
				else:
					logger.info("未连接")
					未连接计次 += 1
					if 未连接计次 >= 3:
						return "disconnected"
					retry += 1
				self.safe_sleep(1)
			# 循环结束，没有匹配到任何状态，记录日志并返回 None
			self.sendLog(f"未能检测到连接状态，达到最大重试次数 {max_retry}")
			return "disconnected"
		
		def _click_connect_button():
			""" 点击连接/断开按钮 """
			# 计算按钮中心点（基于预定义区域）
			x1, y1, x2, y2 = self.button_regions["connect_btn_2"]
			btn_x = x1 + (x2 - x1) // 2
			btn_y = y1 + (y2 - y1) // 2
			self.sendLog(f"点击连接按钮")
			self.ld.touch(self.emulator_id, x=btn_x, y=btn_y)  # 点击后等待1.5秒
			self.safe_sleep(random.uniform(1, 3))
			# 首次使用会弹出一个确定连接的按钮,这里直接用超时检测吧,顺便延迟了
			self.sendLog(f"已连接,等待确认")
			self.ld.wait_and_click(self.templates["confirm_connect"], timeout=5)
			self.safe_sleep(2)
		
		def _verify_connection(max_ping_retry: int = 3):
			"""
			完整连接验证流程（包含Ping测试和智能重试）
			:param max_ping_retry: 单节点最大Ping测试重试次数
			:param timeout: 整体验证超时时间（秒）
			:return: 是否验证成功
			"""
			for current_retry in range(max_ping_retry):
				if self.stop_flag:
					self.thread_stopped.emit()
					return False
				# ========== 步骤2：执行Ping测试 ==========
				logger.info("开始Ping测试...")
				""" 点击Ping测试按钮（区域需预先定义） """
				try:
					# 使用封装的函数
					if self.ld.wait_and_click(self.templates["conn_connected"],
										   timeout=1):
						logger.info("已连接找到,点击测试ping")
					if self.ld.wait_and_click(self.templates["conn_sucess"],
										   timeout=1):
						self.sendLog("Ping测试成功")
						return True
					if self.ld.wait_and_click(self.templates["conn_failed"],
										   timeout=1):
						current_retry += 1
						self.sendLog(f"Ping测试失败 {current_retry} 次,重试")
					self.safe_sleep(1)
				except Exception as e:
					self.sendLog(f"节点验证失败,异常:{e}")
					return False
			self.sendLog(
				f"节点验证失败（已重试 {max_ping_retry} 次），准备切换节点...")
			return False
		
		def _find_nodes(max_retry: int = 5) -> bool:
			"""
			智能节点选择算法（支持列表探测+随机滑动+分层点击）
			新增功能：
			1. 先探测节点存在性不点击
			2. 随机滑动浏览更多节点
			3. 分层概率选择（优先新滑动到的区域）
			"""
			导入节点失败次数 = 0
			# ================== 核心流程 ==================
			for retry in range(max_retry):
				if self.stop_flag:
					self.thread_stopped.emit()
					return False
				# 最好导入节点之前确认下是否打开v2ray
				再次检测v2ray = self.ld.wait_for_image(index=self.emulator_id,
													hwnd=self.hwnd,
													templates=self.templates[
														"connected"], timeout=5)
				if not 再次检测v2ray:
					self.ld.runApp(self.emulator_id, self.v2ray_packageName)
					# self.sendLog(f"v2ray已重启, 5秒后继续")
					self.safe_sleep(5)
				# 阶段1：检查订阅状态
				if self.订阅列表为空:
					# 点击 导入订阅按钮, 等待导入完成
					node_client = self.task_args.get('node_client')
					self.sendLog(f"准备导入节点 {node_client}")
					导入节点结果 = self.import_node(node_client)
					if not 导入节点结果:
						导入节点失败次数 += 1
						self.sendLog(f"导入节点失败 {导入节点失败次数} 次,重试")
						if 导入节点失败次数 >= 3:
							self.sendLog(f"导入节点失败次数过多,任务失败")
							return False
					# continue
					导入节点失败次数 = 0
				# 阶段2：智能滑动（前两次重试不滑动增加首屏点击概率）
				if not self.订阅列表为空 and retry > 1:
					logger.info("开始随机滑动..")
					try:
						self.ld.random_swipe(self.swipe_actions,
										  self.swipe_duration)
					except Exception as e:
						logger.info(f"滑动异常: {e}")
				# 阶段3：分层检测（先全屏快速检测，失败再精确检测）
				if self.首次探测:
					self.sendLog("首次探测节点是否存在")
					found = self.ld.wait_for_image(
						hwnd=self.hwnd,
						index=self.emulator_id,
						templates=r'img/s.png',
						threshold=0.9,
						multi_match=True,
						timeout=5
					)
					logger.info(f"首次探测结果: {found}")
					if not found:
						retry += 1
						self.sendLog("节点列表为空,开始导入节点")
						self.订阅列表为空 = True
						self.订阅列表为空次数 += 1
						if self.订阅列表为空次数 >= 2:
							self.sendLog(
								f"节点列表为空 {self.订阅列表为空次数} 次,重启v2ray")
							self.订阅列表为空次数 = 0
							# 重启v2ray APP 先杀掉进程 再启动
							self.ld.killApp(self.emulator_id,
											self.v2ray_packageName)
							self.safe_sleep(5)
							self.ld.runApp(self.emulator_id,
										   self.v2ray_packageName)
							self.v2ray_重启次数 += 1
							if self.v2ray_重启次数 >= 3:
								self.sendLog(
									f"v2ray已重启 {self.v2ray_重启次数}次,退出")
								self.v2ray_重启次数 = 0
								return False
							self.sendLog(
								f"v2ray已重启 {self.v2ray_重启次数}次, 5秒后继续")
							self.safe_sleep(5)
						continue
					self.sendLog("节点列表已刷新,准备选择节点")
					self.首次探测 = False
					self.订阅列表为空 = False
					# 这里可以加入是否更新订阅的逻辑
					if not self.节点已更新 and self.is_update_node:
						self.sendLog(f"开始更新节点订阅")
						self.节点更新订阅()
						self.节点已更新 = True
						continue
				else:
					if not self.订阅列表为空:
						# 这个地方使用一个随机最好了, 是遍历点击还是继续随机滑动
						actions = ['continue_sliding', 'clicking']
						selected_action = random.choice(actions)
						if selected_action == 'continue_sliding':
							logger.info("选择继续滑动")
							self.safe_sleep(2)
							continue
						self.sendLog("检测节点并且点击1")
						found = self.ld.wait_and_click(template=r'img/s.png',
													multi_match=True,
													threshold=0.95, )
						if found:
							return True
			
			return False
		
		def INS_启动(INS安装结果: bool = False):
			self.sendLog("开始启动INS")
			isn启动失败次数 = 0
			安装重试 = 0
			INS是否安装检测 = False
			INS启动开始时间 = time.time()
			while not self.stop_flag:
				current_time = time.time()
				self.sendLog(
					f"INS启动任务,已用时 {current_time - INS启动开始时间:.2f}秒")
				if current_time - total_start_time > self.timeout_total:
					return False
				# 先启动app
				self.safe_sleep(3)
				INS启动结果 = self.start_app(
					**self.task_args_collection[self.ins_packageName])
				# 如果启动成功,防止启动失败的情况,再做一个检测
				# 如何检测呢?通过找图的话,是判断页面是否还停留在v2ray的页面
				# self.ld.wait_for_image(hwnd=self.hwnd,templates=self.templates["connected"], timeout=10)
				if INS启动结果:
					return True
				else:
					isn启动失败次数 += 1
					self.sendLog(f"INS启动失败,重试")
					if isn启动失败次数 >= 3:
						isn启动失败次数 = 0
						self.sendLog(f"INS启动失败次数过多,任务失败")
						return False
					# kill 进程 再启动
					self.ld.killApp(self.emulator_id, self.ins_packageName)
					self.safe_sleep(3)
					continue
		
		def INS_检测是否到主页():
			self.sendLog("检测INS是否到达主页")
			start_time2 = time.time()
			start_time3 = time.time()
			进入主页检测超时次数 = 0
			重试出现次数 = 0
			申诉出现次数 = 0
			身份认证出现次数 = 0
			主页activity次数 = 0
			计次1 = 0
			出现重试 = False
			在首页 = False
			while not self.stop_flag:
				current_time = time.time()
				self.sendLog(
					f"INS主页检测中,已用时 {current_time - start_time2:.2f}秒,超时{self.timeout_ins_shouye}秒")
				# if current_time - total_start_time > self.timeout_total:
				# 	break
				主页检测超时 = self.check_timeout(start_time2,
												  self.timeout_ins_shouye)
				if 主页检测超时:
					self.sendLog(f"异常-INS主页检测超时")
					进入主页检测超时次数 += 1
					# 超时的话,杀进程重启一次
					if 进入主页检测超时次数 >= 3:
						self.sendLog(f"异常-INS主页检测超时次数过多,任务失败")
						return "异常-INS主页检测超时"
					self.kill_app(
						**self.task_args_collection[self.ins_packageName])
					self.sendLog("INS进程已kill, 10秒后重启")
					self.safe_sleep(10)
					INS启动结果 = self.start_app(
						**self.task_args_collection[self.ins_packageName])
					if not INS启动结果:
						continue
					self.sendLog("INS进程已启动, 5秒后继续检测")
					start_time2 = time.time()
				
				current_activity = self.ld.get_activity_name()
				logger.info(f'当前activity: {current_activity}')
				if "ModalActivity" in current_activity or "BloksSignedOut" in current_activity:
					主页activity次数 += 1
					if 主页activity次数 >= 3:
						主页activity次数 = 0
						在首页 = True
						self.sendLog("INS已到达主页")
					# 这里会有提示身份认证的,记录一下
					
					if self.ld.wait_and_click(r'img/INS_shenfenjixu.png',
										   timeout=2):
						print("出现身份继续,并点击")
						身份认证出现次数 += 1
						if 身份认证出现次数 >= 2:
							身份认证出现次数 = 0
							在首页 = False
							self.sendLog("异常-INS身份认证超时")
							return True
						# # 点击之后需要等待下一步图片出现并点击 INS_xingming_xiayibu.bmp
						# 姓名下一步找图结果 = self.ld.wait_and_click(r'img/INS_xingming_xiayibu.png',timeout=15)
						# 创建账号下一步找图结果 = self.ld.wait_and_click(r'img/INS_chuangjian_xiayibu.png',timeout=15)
						# 同意并继续找图结果 = self.ld.wait_and_click(r'img/INS_tongyibingjixu1.png',timeout=15)
						# # 这里会主线一个 我同意 页面, INS_wotongyi.bmp
						# 我同意找图结果 = self.ld.wait_and_click(r'img/INS_wotongyi.png',timeout=15)
						# # 点击我同意之后,等待的时间可能会长一些,这里需要加一个超时判断
						# # 首先,应该会出现一个提示:  确认或更换你的头像 INS_touxiangwancheng.bmp
						# 更换头像完成找图结果 = self.ld.wait_and_click(r'img/INS_touxiangwancheng.png',timeout=20)
						
						self.ld.wait_for_images(index=self.emulator_id,
											 hwnd=self.hwnd,
											 templates=[
												 r'img/INS_xingming_xiayibu.png',
												 r'img/INS_chuangjian_xiayibu.png',
												 r'img/INS_tongyibingjixu1.png',
												 r'img/INS_wotongyi.png',
												 r'img/INS_touxiangwancheng.png'
											 ],
											 threshold=0.85,
											 click_mode=1,
											 timeout=60,
											 click_delay=3000,
											 )
						
						# 其实到这里就完全可以kill进程,然后重启了.
						self.kill_app(
							**self.task_args_collection[self.ins_packageName])
						self.sendLog("INS进程已kill, 10秒后重启")
						self.safe_sleep(10)
						self.start_app(
							**self.task_args_collection[self.ins_packageName])
						self.sendLog("INS进程已启动, 10秒后继续检测")
						start_time2 = time.time()
						continue
					
					计次1 += 1
					if 计次1 >= 10:
						self.sendLog("账号未登")
						result = "异常-账号未登"
						计次1 = 0
						return result
				
				elif "MainActivity" in current_activity and "instagram" in current_activity:
					if self.ld.wait_and_click(r'img/INS_shouyebiaozhi.png',
										   click_mode=0, timeout=2):
						self.sendLog("在INS主页面")
						result = "在INS主页面"
						在首页 = True
						return result
					
					主页activity次数 += 1
					if 主页activity次数 >= 5:
						self.sendLog("在INS主页面")
						result = "在INS主页面"
						在首页 = True
						主页activity次数 = 0
						return result
				
				if self.ld.wait_for_images(index=self.emulator_id,
										hwnd=self.hwnd,
										templates=[r'img/INS_shouyedenglu.png',
												   r'img/INS_shouyeshensu.png'
												   ],
										timeout=2):
					result = "异常-INS账户未登录"
					return result
				self.safe_sleep(1)
			
			return "异常_INS主页超时"
		
		def INS_修改头像():
			# 先确定在首页
			检测是否在首页 = self.ld.wait_for_image(
				hwnd=self.hwnd,
				index=self.emulator_id,
				templates=[r'img/INS_shouyebiaozhi.png'],
				threshold=0.85,
				timeout=10
			)
			if 检测是否在首页:
				self.sendLog("10秒后开始修改头像")
			self.safe_sleep(10)
			修改头像开始时间 = time.time()
			修改头像失败次数 = 0
			# 先坐标点击我的头像按钮
			self.ld.touch(self.emulator_id, 485, 920)
			self.sendLog("点击 我的")
			self.ld.wait_and_click(template=f'img/INS_bianjizhuye.png', timeout=15)
			self.sendLog("点击 编辑主页")
			# 点击编辑主页之后,可能会弹出创建虚拟形象,这里给他哥10秒等待,点击以后再说
			以后再说找图结果 = self.ld.wait_and_click(
				template=r'img/INS_yihouzaishuo.png', timeout=15)
			if 以后再说找图结果:
				self.sendLog("点击 以后再说")
				self.safe_sleep(5)
			
			# 等待INS_bianjitouxiang.png 出现,即可
			self.ld.wait_and_click(template=r'img/INS_bianjitouxiang.png',
								timeout=15)
			self.sendLog("点击 编辑头像")
			self.safe_sleep(5)
			# 然后点击 从Facebook导入
			self.ld.wait_and_click(template=r'img/facebookdaoru.png')
			self.sendLog("点击 从facebook导入,进入各种点击,60秒超时")
			# # 点击后会有一个提示:  如需从Facebook导入,请把这些账户假如同一个账户中心,点击继续
			# self.ld.wait_and_click(template=f'img/INS_daorujixu.png',timeout=20)
			# self.sendLog("点击 继续")
			# 提示: 如需完成账户添加,请同意下列内容      同意并完成添加
			self.ld.wait_for_images(index=self.emulator_id,
								 hwnd=self.hwnd,
								 templates=[r'img/INS_daorujixu.png',
											r'img/INS_tongyibingwanchengtianjia.png',
											r'img/INS_touxiang_yunxu.png',
											r'img/INS_touxiang_yihouzaiushuo.png'],
								 click_mode=1,
								 click_delay=3000,
								 timeout=60)
			# 等待10秒 完工
			self.sendLog("INS头像修改完成,10秒后结束任务")
			self.safe_sleep(10)
			return True
		
		def INS_账号状态检测():
			self.sendLog("检测INS账号状态")
			start_time3 = time.time()
			账号状态检测失败次数 = 0
			我的按钮区域 = self.button_regions["INS_APP_我的按钮区域"]
			NOT首页actitity次数 = 0
			首页actitity次数 = 0
			在首页 = False
			# 取按钮区域中心点
			x1, y1, x2, y2 = 我的按钮区域
			btn_x = x1 + (x2 - x1) // 2
			btn_y = y1 + (y2 - y1) // 2
			# 点击按钮
			self.sendLog("点击我的按钮,等待5-10秒")
			self.ld.touch(self.emulator_id, btn_x, btn_y)
			self.safe_sleep(random.uniform(5, 10))
			
			# 这里暂时用actitity检测,
			while not self.stop_flag:
				current_time = time.time()
				self.sendLog(
					f"INS账号状态检测中,已用时 {current_time - start_time3:.2f}秒,超时{self.timeout_ins_zhuangtai}秒")
				账号状态检测超时 = self.check_timeout(start_time3,
													  self.timeout_ins_zhuangtai)
				if 账号状态检测超时 and not 在首页:
					self.sendLog(f"INS账号状态检测超时")
					账号状态检测失败次数 += 1
					# 超时的话,杀进程重启一次
					result = "异常-账号状态检测超时"
					break
				current_activity = self.ld.get_activity_name()
				logger.info(f'当前activity: {current_activity}')
				if "Main" in current_activity:
					首页actitity次数 += 1
					在首页 = True
					if 首页actitity次数 >= 10:
						self.sendLog("账号正常")
						result = "正常"
						break
				elif "ChallengeActivity" in current_activity:
					# logger.info("申诉")
					result = "异常-申诉"
					在首页 = False
					break
				else:
					在首页 = False
				self.safe_sleep(2)
			# 成功之后,需要对模拟器进行备注
			return result
		
		def FB_检测是否到主页():
			self.sendLog("检测FB是否到达主页")
			start_time5 = time.time()
			FB主页检测超时 = False
			进入主页检测超时次数 = 0
			主页activity次数 = 0
			FB启动结果 = True
			在首页 = False
			计次1 = 0
			while not self.stop_flag:
				current_time = time.time()
				self.sendLog(
					f"FB主页检测中,已用时 {current_time - start_time5:.2f}秒,超时{self.timeout_ins_shouye}秒")
				# if current_time - total_start_time > self.timeout_total:
				# 	break
				FB主页检测超时 = self.check_timeout(start_time5,
													self.timeout_ins_shouye)
				if FB主页检测超时:
					self.sendLog(f"异常-FB主页检测超时")
					进入主页检测超时次数 += 1
					# 超时的话,杀进程重启一次
					if 进入主页检测超时次数 >= 3:
						self.sendLog(f"异常-FB主页检测超时次数过多,任务失败")
						是否继续运行任务 = False
						return False
					self.kill_app(
						**self.task_args_collection[self.fb_packageName])
					self.sendLog("FB进程已kill, 10秒后重启")
					self.safe_sleep(10)
					FB启动结果 = self.start_app(
						**self.task_args_collection[self.fb_packageName])
					if not FB启动结果:
						continue
					self.sendLog("FB进程已启动, 5秒后继续检测")
					start_time5 = time.time()
				
				current_activity = self.ld.get_activity_name()
				logger.info(f'当前activity: {current_activity}')
				
				if "login.activity" in current_activity:
					# 应该是掉线
					计次1 += 1
					if 计次1 >= 10:
						self.sendLog("账号未登")
						result = "异常-FB账号未登"
						计次1 = 0
						return result
					logger.info("异常-FB账号未登")
				if "FbExperimentalLoggedOut" in current_activity:
					# 如果出现此页面,检测 jixu.bmp 并点击
					self.safe_sleep(1)
				# 继续检测 = self.ld.wait_and_click([
				# 	self.templates["FB_首页_继续1"]],
				# 		timeout=5)
				# if "FbMain" in current_activity:
				# 	主页activity次数 += 1
				# 	if 主页activity次数 >= 5:
				# 		self.sendLog("在FB主页面1")
				# 		在首页 = True
				# 		主页activity次数 = 0
				# 		return True
				if self.ld.wait_for_image(index=self.emulator_id,
									   hwnd=self.hwnd,
									   templates=r'img/fb_shouyebiaoshi1.png',
									   timeout=1):
					# 这里需要添加一个首页的图片标志,用来确定是否正确进入到首页
					self.sendLog("在FB主页面2")
					在首页 = True
					是否继续运行任务 = False
					return True
				if self.ld.wait_for_images(index=self.emulator_id,
										hwnd=self.hwnd,
										templates=[r'img/fb_huihuaguoqing.png',
												   r'img/fb_suoding.png',
												   r'img/fb_shouye_jixu.png',
												   r'img/INS_shouyeshensu.png',
												   ],
										timeout=5):
					result = "异常-FB账户已锁定"
					是否继续运行任务 = False
					self.user_status_updated.emit(self.emulator_id, self.row,
												  result)
					return False
				
				self.safe_sleep(1)
		
		模拟器启动失败次数 = 0
		找图判断模拟器启动失败次数 = 0
		v2ray启动失败次数 = 0
		v2ray已重装 = False
		self.uninstall_app = self.task_args.get('uninstall_app')
		self.is_update_node = self.task_args.get('update_node')
		self.update_ins = self.task_args.get('update_ins')
		self.set_default_font_size = self.task_args.get('set_default_font_size')
		self.首次探测 = True  # 首次探测不点击
		self.订阅列表为空次数 = 0  # 订阅列表为空次数
		self.节点已更新 = False  # 节点已更新
		v2ray是否安装检测 = False  # v2ray是否安装检测
		v2ray开始启动时间 = None  # v2ray开始启动时间
		# 记录最后一个运行的设备行号,写到UI
		self.save_last_device.emit(self.emulator_id, self.row)
		total_start_time = time.time()
		模拟器启动成功 = False
		v2ray初次安装成功 = False
		v2ray_启动成功 = False
		模拟器已重启 = False
		v2ray已安装 = False
		INS已安装 = False
		FB已安装 = False
		FB启动成功 = False
		FB是否安装检测 = False
		是否继续运行任务 = True
		result = ""
		
		# 第一步 启动模拟器
		while not self.stop_flag:
			if time.time() - total_start_time >= self.timeout_emulator:
				self.sendLog(f"模拟器启动超时")
				result = "异常-模拟器启动超时"
				是否继续运行任务 = False
				break
			# 把模拟器启动单独拿出来,因为有时候模拟器启动失败,会导致后续的任务无法执行
			if not 模拟器启动成功:
				self.sendLog(f"开始启动模拟器")
				模拟器启动成功 = self.start_emulator()
				模拟器启动失败次数 += 1
				if 模拟器启动失败次数 >= 3:
					self.sendLog(f"模拟器启动失败")
					result = "异常-模拟器启动失败"
					break
				continue
			
			# 这里就算是判断成功了,再次用找图判断一次
			判断模拟器是否启动成功 = self.ld.wait_for_image(index=self.emulator_id,
														 hwnd=self.hwnd,
														 templates=r'img/leidian.png',
														 threshold=0.9,
														 timeout=50)
			if not 判断模拟器是否启动成功:
				# 有可能是启动卡主了,得重启一下
				找图判断模拟器启动失败次数 += 1
				if 找图判断模拟器启动失败次数 >= 3:
					self.sendLog(f"模拟器启动失败")
					result = "异常-模拟器启动失败"
					break
				self.sendLog(f"模拟器启动卡主,重启模拟器")
				self.ld.quit(self.emulator_id)
				self.sendLog(f"5秒后重启模拟器")
				self.safe_sleep(5)
				continue
			模拟器启动成功 = True
			self.sendLog("找到雷电图标,模拟器启动成功")
			break
		#
		if 模拟器启动成功:
			# 如果有需要,先设置默认字体大小
		
			
			try:
				print(f'set_default_font_size: {self.set_default_font_size}')
				if self.set_default_font_size:
					print("设置默认字体大小")
					self.ld.set_default_font_size(self.emulator_id)
				print('//////////2222')
				# 这里模拟器启动成功后,是处于纯净桌面状态,通过找图判断INS是否安装,V2ray是否安装
				v2ray是否安装检测 = self.ld.wait_for_image(
					index=self.emulator_id,
					hwnd=self.hwnd,
					templates=r'img/v2ray.png',
					threshold=0.9,
					timeout=5)
				print('//////2222')
				if v2ray是否安装检测:
					v2ray已安装 = True
				INS是否安装检测 = self.ld.wait_for_image(index=self.emulator_id,
				                                         hwnd=self.hwnd,
				                                         templates=r'img/ins.png',
				                                         threshold=0.9,
				                                         timeout=5)
			except Exception as e:
				self.sendLog(f"模拟器启动成功,但是找图失败,错误信息: {e}")
		
			if INS是否安装检测:
				INS已安装 = True
			#  这里需要卸载并且重装INS, 如果INS未安装,就不用卸载了
			if INS已安装:
				self.sendLog(f"检测到INS已安装,开始卸载")
				self.ld.uninstallapp(self.emulator_id, self.ins_packageName)
				self.sendLog(f"5秒后重装INS")
				self.safe_sleep(5)
				INS已安装 = False
			INS已安装 = self.install_apk_file(
				**self.task_args_collection[self.ins_packageName])
			self.safe_sleep(5)
			self.sendLog(f"INS已重装,10秒后继续")
			FB是否安装检测 = self.ld.wait_for_image(index=self.emulator_id,
												 hwnd=self.hwnd,
												 templates=r"img/facebook.png",
												 timeout=5)
			if not FB是否安装检测:
				self.sendLog(f"检测到FB未安装,任务退出")
				result = "异常-FB未安装"
				是否继续运行任务 = False
			self.sendLog(f"FB已安装,10秒后继续")
		# 第二部 启动v2ray 并且检测节点
		while 是否继续运行任务 and not self.stop_flag:
			# 总任务超时检测
			if self.check_timeout(total_start_time, self.timeout_v2ray):
				self.sendLog(f"v2ray启动超时")
				self.user_status_updated.emit(self.emulator_id, self.row,
											  "v2ray启动超时")
				result = "异常-v2ray启动超时"
				是否继续运行任务 = False
				break
			self.sendLog(f"模拟器状态:成功, 5秒后启动v2ray")
			self.safe_sleep(5)
			##########  第二步:检测安装v2ray  ######################
			# 应该是先检测APP是否安装
			if not v2ray是否安装检测:
				self.sendLog(f"检测v2ray是否安装")
				v2ray是否安装检测 = self.ld.appVersion(self.emulator_id,
													   self.v2ray_packageName)
			self.sendLog(f"v2ray是否安装检测: {v2ray是否安装检测}")
			if not v2ray是否安装检测:
				self.sendLog(f"v2ray未安装,开始安装v2ray")
				v2ray初次安装成功 = True
				# logger.info(self.task_args_collection[self.v2ray_packageName])
				install_apk_result = self.install_apk_file(
					**self.task_args_collection[self.v2ray_packageName])
				if not install_apk_result:
					continue
				self.sendLog(f"v2ray安装完毕")
			self.sendLog(f"v2ray已安装")
			v2ray已安装 = True
			
			if not v2ray初次安装成功 and self.uninstall_app and not v2ray已重装:
				self.sendLog(f"5秒后重装v2ray")
				self.safe_sleep(5)
				self.ld.uninstallapp(self.emulator_id, self.v2ray_packageName)
				self.safe_sleep(5)
				v2ray是否安装检测 = False
				v2ray已重装 = True
				# v2ray是否安装检测 = True
				self.safe_sleep(5)
				continue
			
			self.safe_sleep(10)
			# 这里需要判断一下APP是否启动成功 改为找图判断
			确认v2ray是否安装成功 = self.ld.wait_for_image(index=self.emulator_id,
														hwnd=self.hwnd,
														templates=r'img/v2ray.png',
														threshold=0.9,
														timeout=20)
			if not 确认v2ray是否安装成功:
				self.sendLog(f"v2ray安装失败")
				continue
			v2ray是否安装检测 = True
			##########  第三步:启动v2ray  ######################
			if v2ray开始启动时间 is None:
				v2ray开始启动时间 = time.time()
				self.sendLog(
					f"启动v2ray超时检测中,已用时 {time.time() - v2ray开始启动时间:.2f}秒,超时300秒")
			if time.time() - v2ray开始启动时间 >= self.timeout_v2ray_node:
				self.sendLog(f"模拟器启动成功,但是v2ray启动失败,任务失败")
				result = "异常-v2ray启动失败"
				是否继续运行任务 = False
				break
			start_app_result = self.start_app(
				**self.task_args_collection[self.v2ray_packageName])
			if not start_app_result:
				continue
			v2ray启动失败次数 = 0
			self.sendLog(f"v2ray启动完毕,再次确认")
			self.safe_sleep(2)
			# 这里需要判断一下APP是否启动成功 改为找图判断
			self.sendLog("再次判断APP是否启动成功,找图判断")
			# 这里找图带有超时检测,如果超时,就杀进程重启一次
			start_app_result = self.ld.wait_for_image(index=self.emulator_id,
												   hwnd=self.hwnd,
												   templates=r'img/connected.png',
												   timeout=10)
			if not start_app_result:
				self.sendLog(f"v2ray启动失败")
				v2ray启动失败次数 += 1
				# 杀进程重启一次
				self.ld.killApp(self.emulator_id, self.v2ray_packageName)
				self.sendLog("APP进程已kill, 5秒后重启")
				self.safe_sleep(5)
				continue
			self.sendLog("进入V2ray首页,准备检测节点")
			
			##########  第四步:检测节点  ######################
			检测节点开始时间 = time.time()
			if time.time() - 检测节点开始时间 >= self.timeout_v2ray_node:
				self.sendLog(f"模拟器启动成功,但是v2ray启动失败,任务失败")
				result = "异常-v2ray启动失败"
				是否继续运行任务 = False
				break
			
			节点选择最后结果 = switch_and_verify_node()
			if not 节点选择最后结果:
				self.user_status_updated.emit(self.emulator_id, self.row,
											  f"v2ray节点异常{self.timeout_v2ray}未连接成功")
				result = "异常-v2ray节点未连接成功"
				是否继续运行任务 = False
				break
			self.sendLog(f"节点选择成功,任务成功")
			v2ray_启动成功 = True
			self.sendLog(f"10秒后启动INS")
			break  # 这里需要用break,退出循环,然后开始检测app
		
		##########  第三步:启动FB  ######################
		self.sendLog(f"模拟器已启动,V2rat已启动, 10秒后启动FB")
		self.safe_sleep(10)
		FB首页检测继续执行 = True
		
		while 是否继续运行任务 and not self.stop_flag:
			# 总任务超时检测
			if self.check_timeout(total_start_time, self.timeout_total_fb):
				self.sendLog(f"FB启动超时")
				self.user_status_updated.emit(self.emulator_id, self.row,
											  "FB启动超时")
				result = "异常-FB启动超时"
				是否继续运行任务 = False
				break
			
			FB启动成功 = self.start_app(
				**self.task_args_collection[self.fb_packageName])
			# 接下来检测FB是否确实启动成功
			if not FB_检测是否到主页():
				self.sendLog(f"FB账户异常")
				result = "异常-FB账户异常"
				是否继续运行任务 = False
				break
			self.sendLog(f"FB进入首页成功")
			# 进入首页后  右上角3个杠 ,点击 3gegang.bmp
			三道杠找图结果 = self.ld.wait_and_click(r"img/3gegang.png",
												 timeout=5)
			self.sendLog(f"点击右上角3个杠,5秒后点击设置按钮")
			if not 三道杠找图结果:
				continue
			self.safe_sleep(5)
			等待设置按钮出现 = self.ld.wait_and_click(r"img/shezhi.png",
												   timeout=5)
			if 等待设置按钮出现:
				self.sendLog(f"点击设置按钮")
				self.safe_sleep(5)
			# 在设置与隐私页面,  这里可以直接点击    在账户中心查看详情
			self.sendLog(f"等待点击 在账户中心查看详情")
			# 在账户中心查看详情找图结果 = self.ld.wait_and_click(r"img/zhanghuzhongxinchakanxiangqing.png", method=6,threshold=0.2,show_result=True,timeout=15)
			# 这个地方貌似找图不太好找,直接点击吧
			# 不过最好是先用找图判断一下是否进入设置页面
			# 设置与隐私找图结果 = self.ld.wait_for_image(index=self.emulator_id,
			# 									  hwnd=self.hwnd,
			# 									  templates=r"img/shezhiyuyinsi.png",
			# 									  timeout=15)
			# if 设置与隐私找图结果:
			# 	self.safe_sleep(3)
			# 	self.ld.background_mouse_click(self.hwnd, 70, 301)
			
			# 在进入账户中心之前,可能会出现一个确定按钮,  欢迎使用账户中心
			# 账户中心确定按钮找图结果 = self.ld.wait_and_click(template=r"img/zhanghuzhongxinqueding.png")
			
			# 这个地方可能会出现一个确定,然后把 "在账户中心查看详情" 再次修改为找图, 把这2个图合并
			
			在账户中心查看详情找图结果 = self.ld.wait_for_images(
				index=self.emulator_id,
				hwnd=self.hwnd,
				templates=[r"img/zhanghuzhongxinchakanxiangqing.png",
						   r"img/zhanghuzhongxinqueding.png"],
				click_mode=1,
				click_delay=3000,
				timeout=15
			)
			
			meta找图结果 = self.ld.wait_for_image(index=self.emulator_id,
											   hwnd=self.hwnd,
											   templates=r"img/FB_zhanghuzhognxin.png",
											   timeout=15)
			if not meta找图结果:
				self.sendLog(f"kill进程,重启FB")
				self.kill_app(**self.task_args_collection[self.fb_packageName])
				self.sendLog(f"5秒后重启FB")
				self.safe_sleep(5)
				continue
			self.sendLog(f"进入到账户中心")
			FB首页检测继续执行 = True
			self.safe_sleep(5)
			# 这里等待是否成功进入管理账户页面, 可以使用 meta.bmp 作为判断
			
			计时7 = time.time()
			zhanghu找图结果 = False
			tianjiazhanghu找图结果 = False
			while FB首页检测继续执行 and not self.stop_flag:
				# 这里需要滑动屏幕,然后找图,找到就退出
				if time.time() - 计时7 >= 20:
					FB首页检测继续执行 = False
					break
				# 滑动屏幕
				self.ld.swipe(self.emulator_id, (300, 700), (400, 300),
							  1000)
				self.safe_sleep(2)
				# 找图 zhanghu.bmp 并点击
				zhanghu找图结果 = self.ld.wait_and_click(r"img/zhanghu.png",
													  timeout=5)
				logger.info(f"账户图标结果: {zhanghu找图结果}")
				if zhanghu找图结果:
					self.sendLog(f"点击 账户按钮")
					self.safe_sleep(5)
					tianjiazhanghu找图结果 = self.ld.wait_for_image(
						index=self.emulator_id,
						hwnd=self.hwnd,
						templates=r"img/tianjiazhanghu.png",
						timeout=10)
					if tianjiazhanghu找图结果:
						break
			if tianjiazhanghu找图结果:
				self.sendLog(f"到达添加账户页面")
				FB首页检测继续执行 = True
				self.safe_sleep(5)
				
				# 最后在本页面 找图   yichu.bmp  这个有可能存在2个,如果是2个就删除下面的一个,如果是1个返回
				yichu_matches = self.ld.wait_for_image(
					hwnd=self.hwnd,
					index=self.emulator_id,
					templates=r"img/yichu.png",
					multi_match=True)
				
				if not yichu_matches:
					result = "异常-FB没有多余账户"
					# 是否继续运行任务 = False
					self.sendLog(f"FB没有多余账户")
					break
				logger.info(f"找到移除坐标:  {yichu_matches}")
				# 按y坐标降序排序（取屏幕下方更大的y值）
				sorted_matches = sorted(yichu_matches, key=lambda m: m[1],
										reverse=True)
				# 获取匹配结果的原始坐标
				target_x = sorted_matches[0][0]
				target_y = sorted_matches[0][1]
				
				# 添加调试信息
				self.sendLog(f"原始匹配坐标: ({target_x}, {target_y})")
				
				# 转换为模拟器屏幕坐标（使用雷电adb的坐标转换）
				screen_x, screen_y = self.ld.window_to_screen(
					(target_x, target_y))
				self.sendLog(f"转换后屏幕坐标: ({screen_x}, {screen_y})")
				
				# 方案1：使用雷电ADB点击（推荐）
				self.ld.touch(self.emulator_id, screen_x, screen_y)
				self.safe_sleep(10)
				
				# 等待 yichuzhanghu.bmp 并点击
				移除账户点击结果 = self.ld.wait_and_click(
					r"img/yichuzhanghu.png", timeout=15)
				if 移除账户点击结果:
					self.sendLog(f"点击 移除账户按钮")
					# 等待 yichu_jixu.bmp 并点击
					移除账户继续点击结果 = self.ld.wait_and_click(
						r"img/yichu_jixu.png", timeout=15)
					self.sendLog(f"点击 移除继续按钮")
					# 等待 wanchengyichu.bmp 并点击
					移除账户确认结果 = self.ld.wait_and_click(
						r"img/wanchengyichu.png", timeout=15)
					self.sendLog(f"点击 移除确认按钮")
					if 移除账户确认结果:
						# # 等待时间可能比较长, 最后是弹出一个确定按钮,也有可能不弹出,这里做一个超时,只要点击了移除就没问题.等待 yichuqueding.bmp  找图并点击
						# yichuqueding_result = self.ld.wait_and_click(
						# 	r"img/yichuqueding.png",timeout=20)
						# # 致辞,facebook操作完毕
						# 是否继续运行任务 = True
						# self.sendLog(f"移除账户成功")
						# self.ld.killApp(self.emulator_id, self.fb_packageName)
						# self.sendLog("FB进程已kill")
						self.ld.wait_for_images(
							hwnd=self.hwnd,
							index=self.emulator_id,
							templates=[r"img/yichu_jixu.png",
									   r"img/yichuqueding.png",
									   r"img/wanchengyichu.png",
									   ],
							threshold=0.9,
							timeout=40
						)
						是否继续运行任务 = True
						self.sendLog(f"移除账户成功")
						self.ld.killApp(self.emulator_id, self.fb_packageName)
						self.sendLog("FB进程已kill")
					
					break
		
		########### 第四部 : 启动INS#######
		while 是否继续运行任务 and not self.stop_flag:
			print("开始启动INS")
			# 总任务超时检测
			if self.check_timeout(total_start_time, 500):
				self.sendLog(f"INS启动超时")
				self.user_status_updated.emit(self.emulator_id, self.row,
											  "INS启动超时")
				result = "异常-INS启动超时"
				是否继续运行任务 = False
				break
			if not INS_启动():
				result = "异常-INS启动异常"
				是否继续运行任务 = False
				break
			# 检测是否进入到INS主页面
			if INS_检测是否到主页() != "在INS主页面":
				result = "异常-INS进入主页失败"
				是否继续运行任务 = False
				break
			print("INS已成功进入主页")
			
			# 接下来修改头像
			if INS_修改头像():
				result = "正常"
				break
		
		logger.info("///////////进入停止任务阶段/////////////")
		try:
			if not self.stop_flag:
				self.safe_sleep(2)
				if result and result != "正常":
					# 修改模拟器备注
					# 截图保存
					# 以时间戳为文件名
					timestamp = int(time.time())
					# 使用相对路径
					img_folder = "abnormal_Img"
					# 检查文件夹是否存在，不存在则创建
					if not os.path.exists(img_folder):
						os.makedirs(img_folder)
					filename = f"{img_folder}/{self.emulator_id}_{timestamp}.png"
					logger.info(
						f"账号异常,开始截图. 文件路径: {filename}, handle: {self.hwnd}")
					self.ld.capture_and_save(filename, self.hwnd)
					self.sendLog(f"异常截图保存成功: {filename}")
				self.safe_sleep(2)
			# 任务完成,关闭模拟器
			self.ld.quit(self.emulator_id)
			self.sendLog("任务完成,关闭模拟器")
			self.safe_sleep(2)
			# 并且吧句柄从列表里移除吧
			if self.hwnd_top and self.ui.hwnd_list:
				self.ui.hwnd_list.remove(self.hwnd_top)
			self.user_status_updated.emit(self.emulator_id, self.row,
										  result)
			self.thread_stopped.emit()  # 发出线程停止信号
		except Exception as e:
			self.sendLog(f"关闭模拟器时出错: {e}")
	
	def check_app_task(self):
		"""
		分为2个阶段:
		1, 切换节点
		2,检测insapp状态

		:return:
		"""
		
		def _check_connection_status(max_retry=10) -> str | None:  # 修改返回类型
			""" 检测连接状态 """
			retry = 0
			start_time = time.time()
			未连接计次 = 0
			while not self.stop_flag and retry < max_retry:
				超时检测 = self.check_timeout(start_time, 20)
				if 超时检测:
					self.sendLog(f"节点连接超时")
					return "timeout"
				try:
					screenshot = self.ld.capture_window_back(
						hwnd=self.hwnd
					)
					if screenshot is None:
						self.sendLog(
							f"[第 {retry + 1} 次重试] 节点列表区域截图失败")
						continue
				except Exception as e:
					self.sendLog(
						f"[第 {retry + 1} 次重试] 截图异常: {str(e)}")
					continue
				connected = self.ld.find_color(screenshot,
											   self.已连接状态颜色HSV, 0.9,
											   10,
											   region=self.button_regions[
												   "connect_btn"])
				if connected:
					logger.info("已连接")
					return "connected"
				else:
					logger.info("未连接")
					未连接计次 += 1
					if 未连接计次 >= 3:
						return "disconnected"
					retry += 1
				self.safe_sleep(1)
			# 循环结束，没有匹配到任何状态，记录日志并返回 None
			self.sendLog(f"未能检测到连接状态，达到最大重试次数 {max_retry}")
			return "disconnected"
		
		def _click_connect_button():
			""" 点击连接/断开按钮 """
			# 计算按钮中心点（基于预定义区域）
			x1, y1, x2, y2 = self.button_regions["connect_btn_2"]
			btn_x = x1 + (x2 - x1) // 2
			btn_y = y1 + (y2 - y1) // 2
			self.sendLog(f"点击连接按钮")
			self.ld.touch(self.emulator_id, x=btn_x, y=btn_y)  # 点击后等待1.5秒
			self.safe_sleep(random.uniform(1, 3))
			# 首次使用会弹出一个确定连接的按钮,这里直接用超时检测吧,顺便延迟了
			self.sendLog(f"已连接,等待确认")
			self.ld.wait_and_click(self.templates["confirm_connect"],
								timeout=5)
			self.safe_sleep(2)
		
		def _verify_connection(max_ping_retry: int = 3):
			"""
			完整连接验证流程（包含Ping测试和智能重试）
			:param max_ping_retry: 单节点最大Ping测试重试次数
			:param timeout: 整体验证超时时间（秒）
			:return: 是否验证成功
			"""
			for current_retry in range(max_ping_retry):
				if self.stop_flag:
					self.thread_stopped.emit()
					return False
				# ========== 步骤2：执行Ping测试 ==========
				logger.info("开始Ping测试...")
				""" 点击Ping测试按钮（区域需预先定义） """
				try:
					# 使用封装的函数
					if self.ld.wait_and_click(self.templates["conn_connected"],
										   timeout=1):
						logger.info("已连接找到,点击测试ping")
					if self.ld.wait_and_click(self.templates["conn_sucess"],
										   timeout=1):
						self.sendLog("Ping测试成功")
						return True
					if self.ld.wait_and_click(self.templates["conn_failed"],
										   timeout=1):
						current_retry += 1
						self.sendLog(
							f"Ping测试失败 {current_retry} 次,重试")
					self.safe_sleep(1)
				except Exception as e:
					self.sendLog(f"节点验证失败,异常:{e}")
					return False
			self.sendLog(
				f"节点验证失败（已重试 {max_ping_retry} 次），准备切换节点...")
			return False
		
		def _find_nodes(max_retry: int = 5) -> bool:
			"""
			智能节点选择算法（支持列表探测+随机滑动+分层点击）
			新增功能：
			1. 先探测节点存在性不点击
			2. 随机滑动浏览更多节点
			3. 分层概率选择（优先新滑动到的区域）
			"""
			导入节点失败次数 = 0
			# ================== 核心流程 ==================
			for retry in range(max_retry):
				if self.stop_flag:
					self.thread_stopped.emit()
					return False
				# 最好导入节点之前确认下是否打开v2ray
				再次检测v2ray = self.ld.wait_for_image(index=self.emulator_id,
													hwnd=self.hwnd,
													templates=
													self.templates[
														"connected"],
													timeout=5)
				if not 再次检测v2ray:
					self.ld.runApp(self.emulator_id, self.v2ray_packageName)
					# self.sendLog(f"v2ray已重启, 5秒后继续")
					self.safe_sleep(5)
				# 阶段1：检查订阅状态
				if self.订阅列表为空:
					# 点击 导入订阅按钮, 等待导入完成
					node_client = self.task_args.get('node_client')
					self.sendLog(f"正在导入 节点 {node_client}")
					导入节点结果 = self.import_node(node_client)
					if not 导入节点结果:
						self.sendLog(
							f"导入节点失败 {导入节点失败次数} 次,重试")
						导入节点失败次数 += 1
						if 导入节点失败次数 >= 3:
							self.sendLog(f"导入节点失败次数过多,任务失败")
							return False
					# continue
					导入节点失败次数 = 0
				# 阶段2：智能滑动（前两次重试不滑动增加首屏点击概率）
				if not self.订阅列表为空 and retry > 1:
					logger.info("开始随机滑动..")
					try:
						self.ld.random_swipe(self.swipe_actions,
										  self.swipe_duration)
					except Exception as e:
						logger.info(f"滑动异常: {e}")
				# 阶段3：分层检测（先全屏快速检测，失败再精确检测）
				if self.首次探测:
					self.sendLog("首次探测节点是否存在")
					found = self.ld.wait_for_image(
						hwnd=self.hwnd,
						index=self.emulator_id,
						templates=r'img/s.png',
						multi_match=True,
						timeout=5
					)
					logger.info(f"首次探测结果: {found}")
					if not found:
						retry += 1
						self.sendLog("节点列表为空,开始导入节点")
						self.订阅列表为空 = True
						self.订阅列表为空次数 += 1
						if self.订阅列表为空次数 >= 2:
							self.sendLog(
								f"节点列表为空 {self.订阅列表为空次数} 次,重启v2ray")
							self.订阅列表为空次数 = 0
							# 重启v2ray APP 先杀掉进程 再启动
							self.ld.killApp(self.emulator_id,
											self.v2ray_packageName)
							self.safe_sleep(5)
							self.ld.runApp(self.emulator_id,
										   self.v2ray_packageName)
							self.v2ray_重启次数 += 1
							if self.v2ray_重启次数 >= 3:
								self.sendLog(
									f"v2ray已重启 {self.v2ray_重启次数}次,退出")
								self.v2ray_重启次数 = 0
								return False
							self.sendLog(
								f"v2ray已重启 {self.v2ray_重启次数}次, 5秒后继续")
							self.safe_sleep(5)
						continue
					self.sendLog("节点列表已刷新,准备选择节点")
					self.首次探测 = False
					self.订阅列表为空 = False
					# 这里可以加入是否更新订阅的逻辑
					if not self.节点已更新 and self.is_update_node:
						self.sendLog(f"开始更新节点订阅")
						self.节点更新订阅()
						self.节点已更新 = True
						continue
				else:
					if not self.订阅列表为空:
						# 这个地方使用一个随机最好了, 是遍历点击还是继续随机滑动
						actions = ['continue_sliding', 'clicking']
						selected_action = random.choice(actions)
						if selected_action == 'continue_sliding':
							logger.info("选择继续滑动")
							self.safe_sleep(2)
							continue
						self.sendLog("检测节点并且点击2")
						found = self.ld.wait_and_click(template=r'img/s.png',
													multi_match=True,
													threshold=0.95, )
						if found:
							return True
			
			return False
		
		def random_swipe(actions: list, duration: int):
			""" 执行智能随机滑动 """
			logger.info(f'开始随机滑动,动作: {actions}, 持续时间: {duration}')
			action = random.choice(actions)
			for action in actions:
				direction = action['direction']
				x1, y1, x2, y2 = action['area']
				# 确保 x1 < x2, y1 < y2
				if x1 > x2:
					x1, x2 = x2, x1
				if y1 > y2:
					y1, y2 = y2, y1
				
				if direction == 'up':
					# 向上滑动，起始点 y 坐标大，结束点 y 坐标小
					start_x = random.randint(x1, x2)
					start_y = random.randint(int(y1 * 0.8),
											 y1)  # 适当缩小范围，避免太边缘
					end_x = random.randint(x1, x2)
					end_y = random.randint(y2,
										   int(y2 * 1.2))  # 适当扩大范围，避免太边缘
				elif direction == 'down':
					# 向下滑动，起始点 y 坐标小，结束点 y 坐标大
					start_x = random.randint(x1, x2)
					start_y = random.randint(y2,
											 int(y2 * 1.2))  # 适当扩大范围，避免太边缘
					end_x = random.randint(x1, x2)
					end_y = random.randint(int(y1 * 0.8),
										   y1)  # 适当缩小范围，避免太边缘
				
				start = (start_x, start_y)
				end = (end_x, end_y)
			
			logger.info(f'开始随机滑动1: {start} -> {end}')
			# 添加随机抖动
			start = (start[0] + random.randint(-10, 10),
					 start[1] + random.randint(-5, 5))
			end = (
				end[0] + random.randint(-10, 10),
				end[1] + random.randint(-5, 5))
			logger.info(f'开始随机滑动2: {start} -> {end}')
			self.ld.swipe(
				index=self.emulator_id,
				start=start,
				end=end,
				duration=duration
			)
			logger.info("滑动完毕1")
			# 滑动后稳定等待
			self.safe_sleep(0.5 + random.random())
		
		def INS_启动(INS安装结果: bool = False):
			self.sendLog("开始启动INS")
			isn启动失败次数 = 0
			安装重试 = 0
			while not self.stop_flag:
				current_time = time.time()
				self.sendLog(
					f"INS启动任务,已用时 {current_time - total_start_time:.2f}秒")
				if current_time - total_start_time > self.timeout_total:
					break
				# 首先检测是否安装了APP
				if not INS安装结果:
					self.sendLog(f"INS未安装,开始安装")
					安装重试 += 1
					self.sendLog(f"尝试安装INS（第{安装重试 + 1}次）")
					if 安装重试 >= 4:
						return "INS安装失败"
					INS安装结果 = self.install_apk_file(
						**self.task_args_collection[self.ins_packageName])
					self.sendLog(f"INS安装成功,5秒后启动")
					self.safe_sleep(5)
					continue
				# 先启动app
				self.safe_sleep(3)
				INS启动结果 = self.start_app(
					**self.task_args_collection[self.ins_packageName])
				if not INS启动结果:
					isn启动失败次数 += 1
					self.sendLog(f"INS启动失败,重试")
					# kill 进程 再启动
					self.ld.killApp(self.emulator_id, self.ins_packageName)
					continue
				# 如果启动成功,防止启动失败的情况,再做一个检测
				# 如何检测呢?通过找图的话,是判断页面是否还停留在v2ray的页面
				# self.ld.wait_for_image(hwnd=self.hwnd,templates=self.templates["connected"], timeout=10)
				if INS启动结果:
					return "INS启动成功"
				else:
					isn启动失败次数 += 1
					if isn启动失败次数 >= 3:
						isn启动失败次数 = 0
						self.sendLog(f"INS启动失败次数过多,任务失败")
						return "异常-INS启动失败"
					# 启动失败的话,啥掉进程重新启动
					self.safe_sleep(2)
					self.ld.killApp(self.emulator_id, self.ins_packageName)
				self.safe_sleep(1)
		
		def INS_检测是否到主页():
			"""基于控件树的智能首页检测（使用find_node函数）"""
			超时时间 = self.timeout_ins_shouye  # 总超时时间（秒）
			检测间隔 = 2  # 检测频率（秒）
			最大连续失败 = 5  # 最大允许连续失败次数
			首页超时次数 = 0  # 新增超时计数器
			循环检测次数 = 0
			start_time = time.time()
			连续失败计数 = 0
			首页特征 = [
				# 修改点1：增加更多特征判断条件
				{"resource_id": "com.instagram.android:id/tab_icon"},
			]
			# 尝试查找多个首页特征控件
			找到特征数 = 0
			while not self.stop_flag:
				# 修改点2：每次检测前强制刷新XML缓存
				self.ld._refresh_xml_cache()  # 确保获取最新界面数据
				循环检测次数 += 1
				self.sendLog(f"检测INS主页,第{循环检测次数}次")
				# 超时检测
				if time.time() - start_time > 超时时间:
					self.sendLog(f"INS主页检测超时（第{首页超时次数 + 1}次）")
					首页超时次数 += 1
					if 首页超时次数 >= 2:
						self.sendLog("INS主页检测超时")
						return "异常-INS主页检测超时"
					# 第一次超时处理流程
					self.kill_app(
						**self.task_args_collection[self.ins_packageName])
					self.sendLog("INS进程已kill, 10秒后重启")
					self.safe_sleep(10)
					self.start_app(
						**self.task_args_collection[self.ins_packageName])
					self.sendLog("INS进程已重启，重新开始检测")
					start_time = time.time()  # 重置计时器
					continue  # 跳过当前循环
				
				try:
					# 修改点3：增加等待时间确保界面加载完成
					self.safe_sleep(2)  # 增加等待时间
					
					# 修改点4：使用更精确的匹配条件
					if self.ld.find_node(
							resource_id="com.instagram.android:id/feed_tab"):
						print("找到首页按钮")
						找到特征数 += 1
					if self.ld.find_node(text__contains="快拍"):
						找到特征数 += 1
						print("找到你的快拍")
					# 找到足够多的特征控件则认为在首页
					if 找到特征数 >= 2:  # 调整匹配阈值
						self.sendLog("INS已到达主页")
						return "在INS主页面"
					# 处理可能出现的中间状态
					
					if self.ld.find_node(text__contains="忘记密码"):
						self.sendLog("账号未登录")
						return "异常-账号未登录"
					if self.ld.find_node(text__contains="申诉"):
						self.sendLog("账号申诉")
						return "异常-账号申诉"
					if self.ld.find_node(text__contains="继续"):
						self.sendLog("账号未登录")
						return "异常-身份验证"
					是我本人尝试登录 = self.ld.find_node(
						text__contains="是我本人尝试登录")
					if 是我本人尝试登录:
						self.sendLog("是我本人尝试登录")
						self.ld.click_node(是我本人尝试登录)
					# 点击 node
					自动化行为 = self.ld.find_node(text__contains="自动化行为")
					if 自动化行为:
						self.sendLog("自动化行为")
						self.ld.click_node(自动化行为)
					if self.ld.find_node(text__contains="立即开始"):
						self.sendLog("立即开始")
						return "异常-立即开始"
					
					连续失败计数 = 0  # 重置计数器
				
				except Exception as e:
					连续失败计数 += 1
					self.sendLog(f"控件检测异常：{str(e)}")
					if 连续失败计数 >= 最大连续失败:
						self.sendLog("连续检测失败次数过多，终止检测")
						return "异常-控件检测失败"
				
				self.safe_sleep(检测间隔)
			
			return "异常-用户中止检测"
		
		def INS_账号状态检测():
			self.sendLog("检测INS账号状态")
			start_time3 = time.time()
			账号状态检测失败次数 = 0
			我的按钮区域 = self.button_regions["INS_APP_我的按钮区域"]
			NOT首页actitity次数 = 0
			首页actitity次数 = 0
			在首页 = False
			# 取按钮区域中心点
			x1, y1, x2, y2 = 我的按钮区域
			btn_x = x1 + (x2 - x1) // 2
			btn_y = y1 + (y2 - y1) // 2
			# 点击按钮
			self.sendLog("点击我的按钮,等待5-10秒")
			self.ld.touch(self.emulator_id, btn_x, btn_y)
			self.safe_sleep(random.uniform(5, 10))
			
			# 这里暂时用actitity检测,
			while not self.stop_flag:
				current_time = time.time()
				self.sendLog(
					f"INS账号状态检测中,已用时 {current_time - start_time3:.2f}秒,超时{self.timeout_ins_zhuangtai}秒")
				账号状态检测超时 = self.check_timeout(start_time3,
													  self.timeout_ins_zhuangtai)
				if 账号状态检测超时 and not 在首页:
					self.sendLog(f"INS账号状态检测超时")
					账号状态检测失败次数 += 1
					# 超时的话,杀进程重启一次
					result = "异常-账号状态检测超时"
					break
				current_activity = self.ld.get_activity_name()
				logger.info(f'当前activity: {current_activity}')
				if "Main" in current_activity:
					首页actitity次数 += 1
					在首页 = True
					if 首页actitity次数 >= 10:
						self.sendLog("账号正常")
						result = "正常"
						break
				elif "ChallengeActivity" in current_activity:
					# logger.info("申诉")
					result = "异常-申诉"
					在首页 = False
					break
				else:
					在首页 = False
				self.safe_sleep(2)
			# 成功之后,需要对模拟器进行备注
			return result
		
		def INS_处理重试(重试次数, 每次等待时间):
			重试点击次数 = 0
			while not self.stop_flag:
				self.sendLog("开始处理重试")
				重试找图结果 = self.ld.wait_and_click(
					self.templates["INS_重试"],
					threshold=0.9,
					click_mode=1,
					timeout=每次等待时间)
				重试点击次数 += 1
				if 重试点击次数 >= 重试次数:
					self.sendLog("重试次数过多,任务失败")
					return "异常-INS点击重试次数过多"
				if 重试找图结果:
					self.sendLog("点击重试成功")
		
		模拟器启动失败次数 = 0
		找图判断模拟器启动失败次数 = 0
		v2ray启动失败次数 = 0
		v2ray已重装 = False
		self.uninstall_app = self.task_args.get('uninstall_app')
		self.is_update_node = self.task_args.get('update_node')
		self.update_ins = self.task_args.get('update_ins')
		self.set_default_font_size = self.task_args.get('set_default_font_size')
		self.首次探测 = True  # 首次探测不点击
		self.订阅列表为空次数 = 0  # 订阅列表为空次数
		self.节点已更新 = False  # 节点已更新
		v2ray是否安装检测 = False  # v2ray是否安装检测
		v2ray开始启动时间 = None  # v2ray开始启动时间
		# 记录最后一个运行的设备行号,写到UI
		self.save_last_device.emit(self.emulator_id, self.row)
		total_start_time = time.time()
		模拟器启动成功 = False
		v2ray初次安装成功 = False
		v2ray_启动成功 = False
		模拟器已重启 = False
		v2ray已安装 = False
		INS已安装 = False
		是否继续进行 = False
		result = ""
		
		# 第一步 启动模拟器
		# while not self.stop_flag:
		# 	if time.time() - total_start_time >= self.timeout_emulator:
		# 		self.sendLog(f"模拟器启动超时")
		# 		result = "异常-模拟器启动超时"
		# 		break
		# 	# 把模拟器启动单独拿出来,因为有时候模拟器启动失败,会导致后续的任务无法执行
		# 	if not 模拟器启动成功:
		# 		self.sendLog(f"开始启动模拟器")
		# 		模拟器启动成功 = self.start_emulator()
		# 		模拟器启动失败次数 += 1
		# 		if 模拟器启动失败次数 >= 3:
		# 			self.sendLog(f"模拟器启动失败")
		# 			result = "异常-模拟器启动失败"
		# 			break
		# 		continue
		#
		# 	# 这里就算是判断成功了,再次用找图判断一次
		# 	判断模拟器是否启动成功 = self.ld.wait_for_image(
		# 		index=self.emulator_id,
		# 		hwnd=self.hwnd, templates=[
		# 			self.templates["雷电图标"]], threshold=0.9, timeout=50)
		# 	if not 判断模拟器是否启动成功:
		# 		找图判断模拟器启动失败次数 += 1
		# 		if 找图判断模拟器启动失败次数 >= 3:
		# 			self.sendLog(f"模拟器启动失败")
		# 			result = "异常-模拟器启动失败"
		# 			break
		# 		# 有可能是启动卡主了,得重启一下
		# 		self.sendLog(f"模拟器启动卡主,重启模拟器")
		# 		self.ld.quit(self.emulator_id)
		# 		self.sendLog(f"5秒后重启模拟器")
		# 		self.safe_sleep(5)
		# 		continue
		# 	模拟器启动成功 = True
		# 	self.sendLog("找到雷电图标,模拟器启动成功")
		# 	break
		模拟器启动成功 = self.emulatorStarter.start()
		if not 模拟器启动成功:
			self.sendLog(f"模拟器启动失败")
			result = "异常-模拟器启动失败"
			是否继续进行 = True
		if 模拟器启动成功:
			# print(f'set_default_font_size: {self.set_default_font_size}')
			# if self.set_default_font_size:
			# 	print("设置默认字体大小")
			# 	self.ld.set_default_font_size(self.emulator_id)
			# 这里模拟器启动成功后,是处于纯净桌面状态,通过找图判断INS是否安装,V2ray是否安装
			v2ray是否安装检测 = self.ld.wait_for_image(index=self.emulator_id,
													hwnd=self.hwnd,
													templates=r'img/v2ray.png',
													threshold=0.8,
													timeout=5)
			if v2ray是否安装检测:
				v2ray已安装 = True
				if self.uninstall_app:
					# 需要重装v2ray
					self.sendLog(f"需要重装v2ray")
					if not v2ray初次安装成功 and not v2ray已重装:
						self.sendLog(f"5秒后重装v2ray")
						self.safe_sleep(5)
						self.ld.uninstallapp(self.emulator_id,
											 self.v2ray_packageName)
						self.safe_sleep(5)
						v2ray是否安装检测 = False
						self.sendLog("开始安装v2ray")
						self.install_apk_file(
							**self.task_args_collection[self.v2ray_packageName])
						v2ray已重装 = True
						# v2ray是否安装检测 = True
						self.safe_sleep(5)
			
			else:
				self.sendLog("开始安装v2ray")
				self.install_apk_file(
					**self.task_args_collection[self.v2ray_packageName])
				self.sendLog("v2ray安装成功,10秒后继续")
				self.safe_sleep(10)
			
			INS是否安装检测 = self.ld.wait_for_image(index=self.emulator_id,
												  hwnd=self.hwnd,
												  templates=r'img/ins.png',
												  threshold=0.8,
												  timeout=5)
			if INS是否安装检测:
				INS已安装 = True
			# 如果需要更新ins版本,就在这里操作,千万不要卸载!直接更新即可
			if self.update_ins:
				# 直接安装
				INS安装结果 = self.install_apk_file(
					**self.task_args_collection[self.ins_packageName])
				if INS安装结果:
					INS已安装 = True
					self.sendLog(f"INS更新成功,10庙后启动ray")
				self.safe_sleep(10)
			print(
				f"APP是否安装检测结果: v2ray: {v2ray已安装}, INS: {v2ray是否安装检测}")
			是否继续进行 = True
		# 第二部 启动v2ray 并且检测节点
		
		if 模拟器启动成功 and 是否继续进行:
			v2ray_启动成功 = self.v2ray_manager.start_with_retry()
			if v2ray_启动成功:
				self.sendLog(f"v2ray启动成功,10秒后启动ins")
				self.safe_sleep(10)
			else:
				self.sendLog(f"v2ray启动失败")
				result = "异常-v2ray启动失败"
		
		# 接下来检测insAPP
		
		##########  第步:启动INS  ######################
		
		while 是否继续进行 and v2ray_启动成功 and not self.stop_flag:
			# 总任务超时检测
			if self.check_timeout(total_start_time, self.timeout_total):
				self.sendLog(f"总任务超时")
				self.user_status_updated.emit(self.emulator_id, self.row,
											  "总任务超时")
				result = "异常-总任务超时"
				break
			INS_启动结果 = INS_启动(INS已安装)
			if INS_启动结果 != "INS启动成功":
				result = INS_启动结果
				break
			首页检测结果 = INS_检测是否到主页()
			if 首页检测结果 == "在INS主页面":
				账号检测结果 = INS_账号状态检测()
				result = 账号检测结果
				break
			else:
				# self.user_status_updated.emit(self.emulator_id, self.row,
				#                               首页检测结果)
				result = 首页检测结果
				break
		
		logger.info("///////////进入停止任务阶段/////////////")
		try:
			if not self.stop_flag:
				self.safe_sleep(2)
				if result and result != "正常":
					# 修改模拟器备注
					# 截图保存
					# 以时间戳为文件名
					timestamp = int(time.time())
					# 使用相对路径
					img_folder = "abnormal_Img"
					# 检查文件夹是否存在，不存在则创建
					if not os.path.exists(img_folder):
						os.makedirs(img_folder)
					filename = f"{img_folder}/{self.emulator_id}_{timestamp}.png"
					logger.info(
						f"账号异常,开始截图. 文件路径: {filename}, handle: {self.hwnd}")
					self.ld.capture_and_save(filename, self.hwnd)
					self.sendLog(f"异常截图保存成功: {filename}")
				self.safe_sleep(2)
			# 任务完成,关闭模拟器
			self.ld.quit(self.emulator_id)
			self.sendLog("任务完成,关闭模拟器")
			self.safe_sleep(2)
			# 并且吧句柄从列表里移除吧
			if self.hwnd_top and self.ui.hwnd_list:
				self.ui.hwnd_list.remove(self.hwnd_top)
			self.user_status_updated.emit(self.emulator_id, self.row,
										  result)
			self.thread_stopped.emit()  # 发出线程停止信号
		except Exception as e:
			self.sendLog(f"关闭模拟器时出错: {e}")
	
	# 更新订阅
	def 节点更新订阅(self):
		右上角3个点找图结果 = self.ld.wait_and_click(
			template=r'img/three_point.png')
		if 右上角3个点找图结果:
			# 更新订阅
			更新订阅按钮找图结果 = self.ld.wait_and_click(
				template=r'img/update.png',
			)
			if 更新订阅按钮找图结果:
				self.sendLog(f"模拟器 {self.emulator_id} 更新订阅,等待刷新")
				self.safe_sleep(10)
	
	# 导入节点
	def import_node(self, node_client):
		"""导入节点"""
		# 接下来需要检测是否刷新节点列表成功
		if self.emulator_is_running:
			# 导入节点
			self.sendLog(f"模拟器 {self.emulator_id} 开始导入节点1")
			start_time = time.time()
			while not self.stop_flag:
				导入节点超时检测 = self.check_timeout(start_time, timeout=30)
				if 导入节点超时检测:
					self.sendLog(f"模拟器 {self.emulator_id} 导入节点超时")
					return False
				# 导入节点
				加号按钮找图结果 = self.ld.wait_for_image(hwnd=self.hwnd,
													   index=self.emulator_id,
													   templates=r'img/jiahao.png',
													   threshold=0.9,
													   click_mode=1,
													   timeout=10)
				if not 加号按钮找图结果:
					self.sendLog(
						f"模拟器 {self.emulator_id} 导入节点失败,未找到加号按钮")
					continue
				self.safe_sleep(2)
				# 把node_client 复制到剪切板
				if node_client:
					
					try:
						# --- 数据清洁阶段 ---
						# 移除首尾空白及非常规字符
						node_client = node_client.strip().replace('\x00', '')
						logger.info(f"[DEBUG] 清洁后内容: {repr(node_client)}")
						pyperclip.copy(node_client)
						self.safe_sleep(0.5)
					
					except Exception as e:
						logger.info(f"⚠️ 致命错误: {e}")
				# return False
				
				导入按钮找图结果 = self.ld.wait_for_image(self.hwnd,
													   index=self.emulator_id,
													   templates=r'img/daoru.png',
													   threshold=0.9,
													   timeout=10)
				logger.info(
					f"导入按钮找图结果: {导入按钮找图结果}")  # {0: [(np.int64(135), np.int64(67), 0.999988853931427)]}
				if 导入按钮找图结果:
					
					click_x, click_y = 导入按钮找图结果
					# 将 numpy.int64 类型转换为 Python 内置的 int 类型
					click_x = int(click_x)
					click_y = int(click_y)
					logger.info(
						f"模拟器 {self.emulator_id} 导入节点,点击坐标: 句柄 {self.hwnd}, ({click_x}, {type(click_x)} {click_y})")
					# 检查窗口是否可见
					is_visible = win32gui.IsWindowVisible(self.hwnd)
					if is_visible:
						self.ld.background_mouse_click(hwnd=self.hwnd, x=click_x,
													y=click_y)
					else:
						self.sendLog(
							f"模拟器 {self.emulator_id} 窗口不可见，无法点击")
					
					self.sendLog(f"模拟器 {self.emulator_id} 导入节点成功")
					self.safe_sleep(2)
					# 然后刷新订阅
					self.节点更新订阅()
					# 更新之后延延迟10秒
					# self.sendLog(f"模拟器 {self.emulator_id} 刷新订阅成功,延迟10秒")
					self.safe_sleep(10)
					return True
				
				self.safe_sleep(1)


class InstagramAutomation(QThread):
	log_signal = pyqtSignal(str, int)
	
	def __init__(self, em):
		# 显式调用父类初始化
		super().__init__()  # 添加这行解决父类初始化问题
		self.stop_flag = em.stop_flag
		self.INS版本 = "278.0.0.21.117"
		# 初始化配置参数
		self.config = {
			'target_follows': 50,  # 关注目标任务数量
			'min_followers': 100,  # 最少粉丝数
			'scroll_timeout': 100,  # 滑动屏幕超时
			'page_load_timeout': 15,  # 加载页面超时
			'delay_every_user': 20,  # 每一个用户之间的随机延迟
			'delay_every_follow': 5,  # 每一个关注之间的随机延迟
			# 增加一个通用延迟
			'delay_general': 5,  # 通用延迟
			'target_country': 'jp',  # 目标国家/地区代码
			'target_regions': ['日本']  # 支持多地区配置
		}
		# 如果传入UI参数则覆盖默认值
		if em is not None:
			# 动态读取UI配置值，保留未设置项的默认值
			for key in self.config:
				if hasattr(em, key):
					self.config[key] = getattr(em, key)
		# 运行状态跟踪
		self.stats = {
			'total_followed': 0,  # 关注用户粉丝列表的数量统计
			'total_user_followed': 0,  # 直接关注用户的数量统计
			'skipped_private': 0,
			'skipped_blue': 0,
			'skipped_low_followers': 0
		}
		# 创建一个用户资料卡吧
		self.user_profile = {
			'user_id': "",
			'nickname': "",
			'followers': 0,
			'following': 0,
		}
		self.em = em  # 暂时先用em这个名字，后面再改
		if self.em:
			self.row = em.row
			# 连接日志信号到主窗口
			self.log_signal.connect(self.em.ui.append_log)  # 假设主窗口有append_log方法
		self.xml_cache = self.em.xml_cache
		self.list_node = self.em.find_node(
			resource_id="android:id/list")  # 粉丝列表滑动list
		self.为你推荐 = self.em.find_node(text="为你推荐")  # 为你推荐
		self.查看更多 = self.em.find_node(text="查看更多")  # 查看更多
		print("InstagramAutomation 初始化完毕")
	
	# 初始化日志
	# logger.basicConfig(level=logger.INFO)
	
	def run(self, target_users: List[str] = None, **kwargs):
		"""主运行流程"""
		# 将参数转换为实例变量
		self.直接用户关注数量 = int(kwargs.get('直接用户关注数量'))
		self.用户粉丝关注数量 = int(kwargs.get('用户粉丝关注数量'))
		self.私信任务数量 = int(kwargs.get('私信任务数量'))
		self.最小粉丝数量 = int(kwargs.get('最小粉丝数量'))
		self.切换用户延迟1 = int(kwargs.get('切换用户延迟1'))
		self.切换用户延迟2 = int(kwargs.get('切换用户延迟2'))
		self.关注延迟1 = int(kwargs.get('关注延迟1'))
		self.关注延迟2 = int(kwargs.get('关注延迟2'))
		self.用户资料页加载超时 = int(kwargs.get('用户资料页加载超时') or 60)
		self.用户资料页加载超时次数 = 0
		self.粉丝列表页滑动超时 = int(kwargs.get('粉丝列表页滑动超时') or 1000)
		self.私密用户跳过 = kwargs.get('私密用户跳过')
		self.蓝v用户跳过 = kwargs.get('蓝v用户跳过')
		self.通用延迟1 = int(kwargs.get('通用延迟1'))
		self.通用延迟2 = int(kwargs.get('通用延迟2'))
		self.关注该数量后休息 = int(kwargs.get('关注该数量后休息'))
		self.关注休息1 = int(kwargs.get('关注休息1'))
		self.关注休息2 = int(kwargs.get('关注休息2'))
		self.node_client = kwargs.get('node_client')
		self.timeout_total = int(kwargs.get('timeout_total'))
		self.目标用户文本路径 = kwargs.get('目标用户文本路径')
		self.地区列表 = kwargs.get('地区列表')
		self.已私信数量 = 0
		current_task = kwargs.get('current_task')
		self.current_user_info = None
		self.current_user = None
		self.current_user_id = None
		result = ""
		是否继续任务 = False
		print(f'self.通用延迟1: {self.通用延迟1}')
		
		if self.INS版本 == "278.0.0.21.117":
			self.用户资料页标识 = "com.instagram.android:id/profile_tab"
			self.粉丝数标识 = "com.instagram.android:id/row_profile_header_textview_followers_count"
			self.发送按钮标识 = "com.instagram.android:id/row_thread_composer_button_send"
		
		############# 下面开始任务#################
		if not self.start_emulator_with_retry():
			self.log("无法启动模拟器，终止任务")
			result = "无法启动模拟器，终止任务"
			self.task_completed(result)
			return
		if not self.start_v2ray_with_retry():
			self.log("无法启动v2ray，终止任务")
			result = "无法启动v2ray，终止任务"
			self.task_completed(result)
			return
		
		self.log(f'v2ray启动并连接成功,10秒后启动INS')
		self.safe_sleep(10)
		if not self.start_ins_app_with_retry():
			self.log("无法启动INS应用，终止任务")
			result = "无法启动INS应用，终止任务"
			self.task_completed(result)
			return
		task_start_time = time.time()
		self.task_start_time = task_start_time
		try:
			if current_task == "私信":
				# 直接关注用户
				self.msg_data = kwargs.get('msg_data')
				self.send_mass_dm_to_followers()
			
			#  这里把关注的2个任务区分开执行...
			if current_task == "关注用户粉丝":
				while self.stats['total_followed'] <= self.用户粉丝关注数量:
					
					if time.time() - task_start_time >= self.timeout_total:
						self.log(
							f"总任务超时.任务进度: {self.stats['total_followed']} / {self.用户粉丝关注数量} 耗时: {time.time() - task_start_time:.2f}秒 ")
						result = f"异常-总任务超时. 任务进度: {self.stats['total_followed']} / {self.用户粉丝关注数量} 耗时: {time.time() - task_start_time:.2f}秒"
						break
					# 从数据库获取账号
					self.current_user = self.em.ui.get_username()
					if not self.current_user:
						logger.warning("目标用户列表已耗尽")
						self.log(
							f'目标用户列表已耗尽. 任务进度: {self.stats['total_followed']} / {self.用户粉丝关注数量} 耗时: {time.time() - task_start_time:.2f}秒')
						break
					self.user_profile['user_id'] = self.current_user
					print(f'current_user: {self.current_user}')
					### 主任务
					result = self.enter_followers_list(self.current_user)
					if result and '频繁' in result:
						self.log(
							f"操作频繁,结束任务. 任务进度: {self.stats['total_followed']} / {self.用户粉丝关注数量} 耗时: {time.time() - task_start_time:.2f}秒")
						break
					###  主任务
					if self.用户资料页加载超时次数 >= 4:
						self.用户资料页加载超时次数 = 0
						self.log(
							f"大概率模拟器无响应了,退出任务. 任务进度: {self.stats['total_followed']} / {self.用户粉丝关注数量} 耗时: {time.time() - task_start_time:.2f}秒")
						break
					# 更新数据库,账号完成状态
					if self.stats['total_followed'] >= self.用户粉丝关注数量:
						self.log(
							f"任务完成,退出循环. 任务进度: {self.stats['total_followed']} / {self.用户粉丝关注数量} 耗时: {time.time() - task_start_time:.2f}秒")
						break
					if self.stats[
						'total_followed'] % self.关注该数量后休息 == 0:
						# self.log(f'已关注 {self.关注该数量后休息} 个用户, 随机休息')
						# 随机休息一下
						随机休息时间 = random.randint(self.关注休息1,
													  self.关注休息2)
						self.safe_sleep(随机休息时间)
					# 没处理完一个用户, 保存一下任务进度,并且做一个随机延迟
					切换用户随机延迟 = random.randint(self.切换用户延迟1,
													  self.切换用户延迟2)
					self.log(
						f"{切换用户随机延迟} 秒后开始下一个用户任务")
					self.safe_sleep(切换用户随机延迟)
				self.log(
					f"任务完成。任务进度: {self.stats['total_followed']} / {self.用户粉丝关注数量},耗时: {time.time() - task_start_time:.2f}秒")
			if current_task == "直接关注用户":
				while self.stats[
					'total_user_followed'] < self.直接用户关注数量 and not self.em.stop_flag:
					if time.time() - task_start_time >= self.timeout_total:
						self.log(
							f"总任务超时.任务进度: {self.stats['total_followed']} / {self.用户粉丝关注数量} 耗时: {time.time() - task_start_time:.2f}秒 ")
						result = f"异常-总任务超时. 任务进度: {self.stats['total_followed']} / {self.用户粉丝关注数量} 耗时: {time.time() - task_start_time:.2f}秒"
						break
					self.current_user = self.em.ui.get_username()
					if not self.current_user:
						logger.warning(
							f"目标用户列表已耗尽. --任务进度 :{self.stats['total_user_followed']} / {self.直接用户关注数量} 耗时: {time.time() - task_start_time:.2f}秒")
						break
					self.user_profile['user_id'] = self.current_user
					self.follow_user(self.current_user)
					# 更新数据库 完成标识
					if self.stats[
						'total_user_followed'] >= self.直接用户关注数量:
						self.log(
							f"任务完成,退出循环.--任务进度 :{self.stats['total_user_followed']} / {self.直接用户关注数量} 耗时: {time.time() - task_start_time:.2f}秒")
						break
		# 没处理完一个用户, 保存一下任务进度,并且做一个随机延迟
		# 切换用户随机延迟 = random.randint(self.切换用户延迟1,
		#                                  self.切换用户延迟2)
		# self.safe_sleep(切换用户随机延迟)
		except Exception as e:
			self.log(f"主流程异常: {str(e)}")
		finally:
			self.task_completed(result)
	
	def task_completed(self, result):
		"""任务完成信号"""
		logger.info("///////////进入停止任务阶段/////////////")
		try:
			# 任务完成,关闭模拟器
			self.em.ld.quit(self.em.emulator_id)
			# self.log("任务完成,关闭模拟器")
			self.safe_sleep(2)
			# 并且吧句柄从列表里移除吧
			if self.em.hwnd_top and self.em.ui.hwnd_list:
				self.em.ui.hwnd_list.remove(self.em.hwnd_top)
			self.em.user_status_updated.emit(self.em.emulator_id, self.em.row,
											 result)
			self.em.thread_stopped.emit()  # 发出线程停止信号
		except Exception as e:
			self.log(f"关闭模拟器时出错: {e}")
	
	def log(self, message):
		"""日志记录方法"""
		# logger.info(message)
		self.log_signal.emit(message, self.row)
	
	def start_emulator_with_retry(self, max_retries=3, retry_interval=5):
		"""带重试机制的模拟器启动"""
		retry_count = 0
		while not self.em.stop_flag and retry_count < max_retries:
			self.log(f"尝试启动模拟器（第 {retry_count + 1}/{max_retries} 次）")
			
			# 调用模拟器线程的启动方法
			if not self.em.start_emulator():
				self.log("模拟器启动失败")
				retry_count += 1
				if retry_count >= 3:
					self.log("模拟器启动失败")
					return False
				self.safe_sleep(retry_interval)
				continue
			
			# 验证是否真正进入桌面
			if self.em.wait_for(
					resource_id="com.android.launcher3:id/folder_icon_name",
					timeout=20):
				self.log("模拟器启动验证通过")
				return True
			else:
				self.log("模拟器界面验证失败")
				self.em.stop_emulator()
				retry_count += 1
				if retry_count >= 3:
					self.log("模拟器启动失败")
					return False
				self.safe_sleep(retry_interval)
		
		self.log(f"达到最大重试次数 {max_retries} 次")
		return False
	
	def start_v2ray_with_retry(self, max_retries=3, retry_interval=10):
		"""带重试机制的v2ray启动流程"""
		retry_count = 0
		package_name = 'com.v2ray.ang'
		
		while not self.em.stop_flag and retry_count < max_retries:
			self.log(f"尝试启动v2ray（第 {retry_count + 1}/{max_retries} 次）")
			
			# 启动应用
			if not self.em.start_app(package_name=package_name):
				self.log("v2ray启动失败")
				retry_count += 1
				self.safe_sleep(retry_interval)
				continue
			
			# 验证连接状态
			if not self._v2ray_node_management():
				self.log("v2ray连接失败")
				return False
			# 节点验证成功, v2ray流程完毕,返回
			return True
		
		self.log(f"达到最大重试次数 {max_retries} 次")
		return False
	
	def _v2ray_node_management(self):
		"""完整的v2ray节点管理流程"""
		# 阶段1：检查节点列表
		if not self._check_existing_nodes():
			self.log("未找到可用节点，开始导入订阅")
			if not self._import_and_update_subscription():
				self.log("节点订阅失败")
				return False
		
		# 阶段2：随机选择节点
		start_time = time.time()
		已选择节点 = True
		while not self.em.stop_flag and time.time() - start_time < 300:
			if not 已选择节点:
				self.log(f'开始随机切换节点')
				selected_node = self._select_random_node()
				if selected_node:
					self.log(f"成功选择节点: {selected_node}")
					已选择节点 = True
				if not selected_node:
					self.log("无法选择有效节点")
					已选择节点 = False
					continue
			
			# 阶段3：连接v2ray
			连接状态 = self._node_connection()
			if not 连接状态:
				self.log("节点连接失败")
				return False
			# 阶段4 :测试ping
			ping测试结果 = self._node_ping()
			if not ping测试结果:
				self.log("节点ping测试失败5次,更换节点重试")
				已选择节点 = False
				self.safe_sleep(1)
				# 接下来 更换节点
				continue
			return True
		return False
	
	def _check_existing_nodes(self, timeout=10):
		"""检查列表是否存在节点"""
		start_time = time.time()
		while not self.em.stop_flag and time.time() - start_time < timeout:
			# 通过界面元素判断节点列表是否为空
			if self.em.find_node(resource_id="com.v2ray.ang:id/tv_name"):
				self.log('节点列表存在')
				return True
			self.safe_sleep(2)
		return False
	
	def _import_and_update_subscription(self, max_retries=3):
		"""导入并更新订阅链接"""
		for retry in range(max_retries):
			# 调用模拟器线程的导入方法
			result = self.em.wait_for(desc="添加配置", timeout=10, click=True)
			if result:
				# ## 订阅连接 https://ss.suyunti.cc/link/HiuoC9QzQrng43Ro?sub=3
				# 订阅连接 = 'https://ss.suyunti.cc/link/HiuoC9QzQrng43Ro?sub=3'
				# 先复制链接
				pyperclip.copy(self.node_client)
				# 等待粘贴
				点击导入 = self.em.wait_for(text="从剪贴板导入", timeout=15,
											click=True)
				if 点击导入:
					self.log("节点导入成功,更新订阅")
					self.em.wait_for(desc="更多选项", timeout=10, click=True)
					self.em.wait_for(text="更新订阅", timeout=10, click=True)
					self.log("更新订阅,等待刷新")
					self.safe_sleep(5)
					# 检查节点列表是否为空
					if not self._check_existing_nodes():
						self.log("节点列表刷新成功")
						return True
		
		return False
	
	def _select_random_node(self):
		"""智能随机选择节点算法"""
		list_node = self.em.find_node(
			resource_id="com.v2ray.ang:id/recycler_view")
		
		# 阶段1：随机滑动浏览更多节点
		for _ in range(random.randint(1, 5)):  # 随机滑动3-5次
			# 生成随机滑动参数
			direction = random.choice(['up', 'down'])
			distance_ratio = random.uniform(0.3, 0.7)  # 滑动距离比例
			
			# 执行随机滑动
			if list_node and self.em.scroll_list_enhanced(list_node,
														  direction=direction,
														  ratio=distance_ratio):
				self.safe_sleep(random.uniform(1, 3))  # 滑动后随机等待
		
		# 阶段2：获取所有可见节点
		nodes = self.em.find_nodes(resource_id="com.v2ray.ang:id/tv_name")
		
		if not nodes:
			self.log("未找到任何可用节点")
			return None
		
		# 随机选择并直接点击
		try:
			selected = random.choice(nodes)
			success, _ = self.em.click_node(selected)
			if success:
				self.log(f"成功点击节点: {selected.get('text', '')}")
				return True
			return False
		except Exception as e:
			self.log(f"节点点击异常: {str(e)}")
			return False
	
	def _node_connection(self):
		"""带智能重试的节点连接逻辑"""
		max_retry = 5  # 增加最大重试次数
		retry_count = 0
		
		while not self.em.stop_flag and retry_count < max_retry:
			# 获取连接状态
			state_node = self.em.find_node(
				resource_id="com.v2ray.ang:id/tv_test_state"
			)
			
			# 状态判断
			if state_node and '已连接' in state_node.get('text'):
				return True
			
			# 未连接时执行点击操作
			if state_node and state_node.get('text') == '未连接':
				self.log("v2ray未连接,等待连接")
				# 点击连接按钮
				connect_btn = self.em.find_node(
					resource_id="com.v2ray.ang:id/fab"
				)
				if connect_btn:
					self.em.click_node(connect_btn)
					# 增加等待时间让连接操作完成
					self.safe_sleep(3)
					
					# 重新检查连接状态
					if self._check_connection():
						self.log("v2ray连接成功,接下来测ping")
						return True
			
			retry_count += 1
			self.log(f"连接失败，正在进行第{retry_count}次重试...")
			self.safe_sleep(3)
		
		self.log("无法启动v2ray，终止任务")
		return False
	
	def _check_connection(self):
		"""独立封装连接状态检测"""
		check_node = self.em.find_node(
			resource_id="com.v2ray.ang:id/tv_test_state"
		)
		if check_node and '已连接' in check_node.get('text',
													 '') or '失败' in check_node.get(
			'text', ''):
			return True
		else:
			return False
	
	# 接下来测试ping
	def _node_ping(self, timeout=30):
		"""测试节点ping延迟"""
		start_time = time.time()
		ping测试失败次数 = 0
		while not self.em.stop_flag and time.time() - start_time < timeout:
			# 点击测试按钮
			ping状态 = self.em.find_node(
				resource_id="com.v2ray.ang:id/tv_test_state")
			if ping状态 and '成功' not in ping状态.get('text', ''):
				# 需要点击一下
				ping测试失败次数 += 1
				self.log(f'测ping失败 {ping测试失败次数} 次')
				if ping测试失败次数 >= 5:
					self.log("ping测试失败次数过多,跳过")
					return False
				
				self.em.click_node(ping状态)
				# 点击后,continue
				self.safe_sleep(2)
				continue
			
			elif ping状态 and '成功' in ping状态.get('text', ''):
				self.log("ping测试成功")
				return True
			self.safe_sleep(2)
	
	def start_ins_app_with_retry(self, max_retries=3, retry_interval=5):
		"""带重试机制的INS应用启动"""
		retry_count = 0
		package_name = 'com.instagram.android'
		
		# # 先检测一下ins的版本
		# ins_version = self.em.get_app_version(package_name)
		
		while not self.em.stop_flag and retry_count < max_retries:
			self.log(f"尝试启动INS应用（第 {retry_count + 1}/{max_retries} 次）")
			
			# 调用模拟器线程的启动方法
			if not self.em.start_app(package_name=package_name):
				self.log("应用启动失败")
				retry_count += 1
				self.safe_sleep(retry_interval)
				continue
			
			# 验证是否进入INS主界面
			if self.em.wait_for(
					resource_id="com.instagram.android:id/feed_tab",
					timeout=20):
				self.log("INS应用启动验证通过")
				return True
			else:
				self.log("INS界面验证失败")
				self.em.kill_app(
					**self.em.task_args_collection[self.em.ins_packageName])
				retry_count += 1
				self.safe_sleep(retry_interval)
		
		self.log(f"达到最大重试次数 {max_retries} 次")
		return False
	
	# 直接关注目标用户
	def follow_user(self, user_id: str):
		"""处理单个用户的主流程"""
		self.log(f"开始处理用户：{user_id}")
		# 先跳转到用户资料页
		if not self.navigate_to_profile(user_id):
			return
		print("已跳转至用户资料页,检测是否私密账户以及蓝V")
		# 检查是否为私密账户或蓝V认证账户
		if self.skip_special_account():
			return
		print("普通用户,检查粉丝数")
		# 检查粉丝数
		if not self.check_followers_count():
			return
		# print(self.user_profile)
		# 接下来直接关注就行..
		关注状态 = self.em.find_node(
			resource_id="com.instagram.android:id/profile_header_follow_button"
		)
		if 关注状态 and 关注状态.get('text', '') == "已关注":
			self.log("该用户已关注,跳过")
			return False
		elif 关注状态 and 关注状态.get('text', '') == "关注" or 关注状态.get(
				'text', '') == "回粉":
			self.log("该用户未关注,开始关注")
			# 点击关注按钮
			self.em.click_node(关注状态)
			self.stats['total_user_followed'] += 1
			self.log(
				f"关注完成,已关注 {self.stats['total_user_followed']} / {self.直接用户关注数量}")
			if self.stats['total_user_followed'] >= self.直接用户关注数量:
				print("任务完成,退出循环")
				return True
			return True
	
	# 关注用户粉丝列表
	def enter_followers_list(self, user_id: str):
		"""处理单个用户的主流程"""
		self.log(f"开始处理用户：{user_id}")
		
		# 先跳转到用户资料页
		if not self.navigate_to_profile(user_id):
			return
		print("已跳转至用户资料页,检测是否私密账户以及蓝V")
		# 检查是否为私密账户或蓝V认证账户
		if self.skip_special_account():
			return
		print("普通用户,检查粉丝数")
		# 检查粉丝数
		if not self.check_followers_count():
			return
		print(self.user_profile)
		
		# 接下来需要点击粉丝列表进行继续操作
		return self.process_followers_list()
	
	# 私信
	def send_mass_dm_to_followers(self, max_messages=100):
		"""批量给粉丝发送私信"""
		self.log("开始执行批量私信任务")
		
		# 新增去重文件路径（建议使用绝对路径）
		SENT_RECORD_FILE = "sent_users.txt"
		
		# 初始化已发送记录
		sent_users = set()
		try:
			# 读取历史记录
			with open(SENT_RECORD_FILE, 'r') as f:
				sent_users = set(line.strip() for line in f if line.strip())
		except FileNotFoundError:
			pass
		
		# 跳转到个人主页
		if not self.navigate_to_self_profile():
			self.log("无法进入个人主页")
			return False
		# 打开粉丝列表后等待一会吧
		followers_count = self.get_followers_count()
		if followers_count == 0:
			self.log("粉丝列表为空,终止任务")
			return False
		self.log(f"粉丝数量: {followers_count}")
		# 打开粉丝列表
		if not self.open_mine_followers_list():
			self.log("无法打开粉丝列表")
			return False
		
		sent_count = 0
		last_known_count = 0
		retry_count = 0
		self.发送按钮node = None
		self.返回按钮node = None
		while not self.em.stop_flag and sent_count < max_messages and not self.stop_flag:
			# 获取当前可见粉丝
			followers = self.get_mine_followers()
			if not followers:
				if retry_count >= 3:
					self.log("连续3次未找到粉丝，停止任务")
					break
				retry_count += 1
				self.em.scroll_list_enhanced(self.list_node, 'down')
				continue
			self.log(f"粉丝列表获取完成,接下来进行私信")
			retry_count = 0
			
			for follower in followers:
				if sent_count >= max_messages:
					break
				# 确定在粉丝列表页
				# 是否在粉丝列表页 = self.em.find_node(
				# 	resource_id="com.instagram.android:id/title"
				# )
				
				# 需要用户名去重,已经发过的就不再发了,
				# 新增去重检查
				username = follower.get('username', '')
				if not username or username in sent_users:
					self.log(f"用户 {username} 已发送过，跳过")
					continue
				
				# 进入粉丝主页
				if self.open_follower_profile(follower['position']):
					self.log("等待进入粉丝主页")
					# 判断是否正确进入粉丝主页
					self.em.wait_for(text="发消息", timeout=10, click=True)
					self.log("点击发消息")
					self.em.wait_for(
						resource_id="com.instagram.android:id/row_thread_composer_edittext",
						timeout=10,
						click=True
					)
					self.log("开始输入私信内容")
					# 发送私信
					message = self.get_random_message()
					self.em.input_text(message)
					if self.发送按钮node is None:
						self.em.wait_for(
							resource_id=self.发送按钮标识,
							click=True, timeout=5)
					else:
						self.em.click_node(self.发送按钮node)
					sent_count += 1
					self.已私信数量 += 1
					self.log(f"成功发送第 {sent_count} 条私信")
					
					# 记录已发送用户
					sent_users.add(username)
					with open(SENT_RECORD_FILE, 'a') as f:
						f.write(f"{username}\n")
					
					sent_count += 1
					self.已私信数量 += 1
					
					self.safe_sleep(1)
					# 然后返回
					if self.返回按钮node is None:
						返回按钮node = self.em.find_node(
							resource_id="com.instagram.android:id/action_bar_button_back"
						)
					self.em.ld.touch(self.em.emulator_id, 39, 78)
					self.safe_sleep(1)
					self.em.ld.touch(self.em.emulator_id, 39, 78)
					self.safe_sleep(1)
					if sent_count >= self.私信任务数量:
						self.log(
							f"任务完成. 任务进度: {sent_count} / {self.私信任务数量}")
						return True
			查看更多 = self.em.wait_for(text="查看更多", click=True, timeout=5)
			if 查看更多:
				self.log("点击查看更多")
				self.safe_sleep(1)
				continue
			if self.em.find_node(text="为你推荐"):
				self.log("已到达底部,停止任务")
				break
			# 第一次遍历完成之后 需要滑动屏幕
			# 滚动加载更多
			self.em.scroll_list_enhanced(self.list_node, 'down')
		
		self.log(f"任务完成，共发送 {sent_count} 条私信")
		return True
	
	def get_random_message(self):
		"""从竖线分隔的话术中随机取一个"""
		if not self.msg_data:
			return "Hello!"  # 默认话术
		
		# 分割话术并过滤空值
		messages = [m.strip() for m in self.msg_data.split('|') if m.strip()]
		
		# 添加常见默认话术
		defaults = ['Nice to meet you!', 'How are you?']
		return random.choice(messages) if messages else random.choice(defaults)
	
	def navigate_to_profile(self, user_id: str) -> bool:
		"""跳转到指定用户资料页"""
		self.log(f"跳转到用户主页：{user_id}")
		start_time = time.time()
		
		# 构造Instagram deep link命令
		cmd = f'am start -a android.intent.action.VIEW -d "instagram://user?username={user_id}"'
		self.em._adb_command(cmd)
		# 调用您封装的ADB跳转逻辑
		timeout_threshold = self.用户资料页加载超时  # 使用局部变量存储超时阈值
		while not self.em.stop_flag and (
				time.time() - start_time) < timeout_threshold:
			# 优先检查用户资料页标题
			if self.check_current_profile(user_id):
				self.log(f"当前已在用户主页：{user_id}")
				# 二次验证关键元素
				if self.em.find_node(
						resource_id=self.粉丝数标识):
					return True
			
			# 直接检测关键元素（应对快速加载的情况）
			if self.em.find_node(
					resource_id=self.粉丝数标识):
				self.log(f"成功进入用户主页：{user_id}")
				self.用户资料页加载超时次数 = 0
				return True
			
			self.safe_sleep(2)
		
		logger.warning("用户资料页加载超时")
		self.用户资料页加载超时次数 += 1
		return False
	
	def skip_special_account(self) -> bool:
		"""综合检测账户状态（私密/蓝V/粉丝数）"""
		# 检测私密账户和蓝V
		is_private = self.is_private_account()
		is_blue = self.is_blue_verified()
		# 检测粉丝数是否达标
		count = self.get_followers_count()
		self.user_profile['followers'] = count
		
		if is_private:
			self.log("检测到私密账户")
			self.stats['skipped_private'] += 1
		if is_blue:
			self.log("检测到蓝V认证账户")
			self.stats['skipped_blue'] += 1
		if is_private or is_blue:
			return True  # 任意特殊账户状态则返回True
		
		if count < self.最小粉丝数量:
			self.log(f"粉丝数不足{self.最小粉丝数量}，当前：{count}")
			self.stats['skipped_low_followers'] += 1
			return True  # 粉丝数不足也返回True
		
		return False  # 所有条件都通过返回False
	
	def check_followers_count(self) -> bool:
		"""检查粉丝数量是否达标"""
		count = self.get_followers_count()
		self.user_profile['followers'] = count
		
		if count < self.最小粉丝数量:
			self.log(
				f"粉丝数不足{self.最小粉丝数量}，当前：{count}")
			self.stats['skipped_low_followers'] += 1
			return False
		return True
	
	def process_followers_list(self):
		"""处理粉丝列表"""
		if not self.open_followers_list():
			return '没有打开粉丝列表'
		
		last_position = None
		start_time = time.time()
		
		while not self.em.stop_flag and self.stats[
			'total_followed'] < self.用户粉丝关注数量:
			
			if time.time() - self.task_start_time > self.timeout_total:
				self.log("任务超时，退出")
				return "任务超时，退出"
			# 需要检测几个关注频繁的提示
			if self.em.find_node(text__contains="请稍后重试"):
				self.log("出现稍后重试,关注频繁,终止任务")
				return "出现稍后重试,关注频繁,终止任务"
			if self.em.find_node(self.xml_cache, text__contains="请求待审核"):
				self.log("出现请求审核,关注频繁,终止任务")
				return "出现请求审核,关注频繁,终止任务"
			# 获取当前屏幕粉丝信息
			followers = self.get_visible_followers()
			if not followers:
				return ''
			
			for follower in followers:
				if self.should_follow(follower['nickname'],
									  self.地区列表):
					self.log(f"用户: {follower['nickname']} 符合地区筛选")
					if follower['is_followed']:
						self.log(f"用户: {follower['nickname']} 已关注")
						continue
					if self.execute_follow(follower['button_pos']):
						self.stats['total_followed'] += 1
						关注后随机延迟 = random.randint(self.关注延迟1,
														self.关注延迟2)
						# 这里也增加一个判断任务完成数
						if self.stats[
							'total_followed'] >= self.用户粉丝关注数量:
							return f"已完成任务，共关注 {self.stats['total_followed']} 人"
						self.log(
							f"已关注数：{self.stats['total_followed']}/{self.用户粉丝关注数量}, {关注后随机延迟} 秒后下一个, 耗时: {time.time() - self.timeout_total:.2f} 秒")
						self.safe_sleep(关注后随机延迟)
				else:
					self.log(
						f"用户: {follower['nickname']} 不符合地区筛选,跳过")
			查看更多 = self.em.wait_for(text="查看更多", click=True, timeout=2)
			if 查看更多:
				self.log("点击查看更多")
				self.safe_sleep(1)
				continue
			
			if self.em.find_node(
					resource_id="com.instagram.android:id/row_header_textview"):
				self.log("已到达底部,停止任务")
				return "已到达底部,停止任务"
			# 滑动屏幕逻辑
			self.em.scroll_list_enhanced(self.list_node, 'down')
			
			# 超时判断
			if time.time() - start_time > self.粉丝列表页滑动超时:
				logger.warning("滚动超时")
				return "滚动超时"
			
			self.safe_sleep(2)
		return ""
	
	def should_follow(self, nickname: str, target_regions: list) -> bool:
		"""根据昵称和地区列表判断是否关注"""
		nickname = nickname.lower().strip()
		if not nickname:
			return False
		# 语言特征正则表达式
		han_re = re.compile(r'[\u4e00-\u9fff]')  # 中文
		jp_re = re.compile(r'[\u3040-\u309f\u30a0-\u30ff]')  # 日文假名
		kr_re = re.compile(r'[\uac00-\ud7af]')  # 韩文
		th_re = re.compile(r'[\u0e00-\u0e7f]')  # 泰文
		en_re = re.compile(r'^[a-z0-9_]+$')  # 英文/数字
		
		# 检测语言特征
		if jp_re.search(nickname):
			return any(r in ['通用', '日本'] for r in target_regions)
		elif kr_re.search(nickname):
			return any(r in ['通用', '韩国'] for r in target_regions)
		elif th_re.search(nickname):
			return any(r in ['通用', '泰国'] for r in target_regions)
		elif han_re.search(nickname):
			return any(r in ['通用', '中国'] for r in target_regions)
		elif en_re.match(nickname):
			return '通用' in target_regions
		
		return False
	
	# 以下为需要您实现的ADB操作接口（示例）
	def check_current_profile(self, user_id: str) -> bool:
		"""验证当前是否在指定用户的资料页"""
		result = self.em.find_node(
			resource_id="com.instagram.android:id/action_bar_title")
		if result and result['text'] == user_id:
			return True
		else:
			return False
	
	def is_private_account(self) -> bool:
		"""检查是否为私密账户"""
		# com.instagram.android:id/igds_headline_emphasized_headline
		# com.instagram.android:id/row_profile_header_empty_profile_notice_title
		check_conditions = [
			# 优先检查仅需节点存在的条件
			({
				 'resource_id': 'com.instagram.android:id/row_profile_header_empty_profile_notice_title'},
			 "这是私密帐户"),
			# 修正文本匹配为实际出现的"这是私密帐户"
			({
				 'resource_id': 'com.instagram.android:id/igds_headline_emphasized_headline'},
			 "这是私密帐户")
		]
		check_result = self.em.check_any_node(check_conditions)
		# 更新状态到数据库
		return check_result
	
	def is_blue_verified(self) -> bool:
		"""检查蓝V认证状态"""
		check_result = self.em.find_node(
			resource_id="com.instagram.android:id/action_bar_title_verified_badge")
		
		if check_result:
			return True
		else:
			return False
	
	def get_followers_count(self) -> int:
		"""获取粉丝数量"""
		result = self.em.find_node(
			resource_id=self.粉丝数标识)
		
		if not result or not result.get('text'):
			return 0
		
		count_str = result['text'].lower().strip()  # 新增strip()处理空格
		
		try:
			# 修改点1：正则表达式增加逗号匹配
			num_match = re.search(r'[\d,]+\.?\d*',
								  count_str.replace(',', ''))  # 先移除逗号
			if not num_match:
				logger.warning(f"未找到数字内容: {count_str}")
				return 0
			
			# 修改点2：处理带逗号的数字字符串
			num_str = num_match.group().replace(',', '')  # 移除所有逗号
			num = float(num_str)  # 先转换为浮点数处理
			
			# 处理单位
			if '万' in count_str:
				num *= 10000
			elif 'k' in count_str:
				num *= 1000
			
			# 调试日志
			logger.debug(f"原始文本: {count_str} -> 解析结果: {int(num)}")
			
			# 更新数据库（保持原有逻辑）
			
			return int(num)
		except Exception as e:
			logger.error(
				f"粉丝数解析失败 | 原始文本: {count_str} | 错误: {str(e)}")
			return 0
	
	def open_followers_list(self) -> bool:
		"""进入粉丝列表页面"""
		# 查找粉丝数控件时强制刷新XML
		followers_node = self.em.find_node(
			resource_id=self.粉丝数标识
		)
		
		if not followers_node or not followers_node.get('bounds'):
			logger.warning("未找到粉丝数控件")
			return False
		
		# 点击后添加等待逻辑
		success, _ = self.em.click_node(followers_node)
		if not success:
			return False
		
		# 等待最多10秒，每0.5秒检查一次标题
		try:
			result = self.em.wait_for(
				resource_id="com.instagram.android:id/title",
				timeout=20,
				interval=1
			)
			# 这里还要进行一个继续判断. com.instagram.android:id/row_no_results_textview "找不到任何用户。"
			if result:
				if not self.em.find_node(
						resource_id="com.instagram.android:id/row_no_results_textview"):
					return True
			else:
				return False
		except TimeoutError:
			return False
	
	def get_visible_followers(self) -> List[dict]:
		"""获取当前屏幕可见的粉丝信息列表"""
		followers = []
		
		# 获取所有粉丝容器节点
		containers = self.em.find_nodes(
			resource_id="com.instagram.android:id/follow_list_container"
		)
		
		for container in containers:
			# 在容器内查找用户名和按钮
			username_node = self.em.find_children(
				container,
				resource_id="com.instagram.android:id/follow_list_subtitle"
			)
			
			button_node = self.em.find_children(
				container,
				resource_id="com.instagram.android:id/follow_list_row_large_follow_button"
			)
			
			if username_node and button_node:
				# 获取按钮状态文本
				button_text = button_node[0].get("text", "")
				# 判断关注状态：关注=未关注状态，已关注=已关注状态
				is_followed = button_text.strip() == "已关注" or button_text.strip() == "已请求"
				followers.append({
					"nickname": username_node[0].get("text", ""),
					"button_pos": self.em.get_node_bounds(button_node[0]),
					"is_followed": is_followed  # 新增关注状态字段
				})
		
		self.log(f"当前屏幕发现 {len(followers)} 个可操作粉丝")
		# print(followers)
		return followers
	
	def scroll_followers_list(self) -> tuple:
		"""滚动粉丝列表并返回滚动位置标记"""
		raise NotImplementedError
	
	def execute_follow(self, button_pos: tuple) -> bool:
		"""通过按钮坐标执行关注操作"""
		if not button_pos:
			logger.warning("未找到关注按钮坐标")
			return False
		
		try:
			x, y = button_pos
			success, cost_time = self.em.click(x, y)
			if success:
				self.log(f"关注成功，坐标：({x}, {y})，耗时{cost_time:.2f}秒")
				self.safe_sleep(1.5)  # 等待关注状态更新
				return True
			return False
		except Exception as e:
			logger.error(f"关注操作异常：{str(e)}")
			return False
	
	def safe_sleep(self, seconds):
		"""可中断的短间隔等待"""
		interval = 0.1  # 100ms的间隔
		for _ in range(int(seconds / interval)):
			if self.stop_flag:  # 只需要检测这一个标志位
				return False
			time.sleep(interval)
		return True
	
	# --------- 辅助函数 ---------
	def navigate_to_self_profile(self):
		"""导航到当前账号的个人主页"""
		个人主页 = self.em.find_node(
			resource_id=self.用户资料页标识
		)
		if 个人主页:
			self.em.click_node(个人主页)
			self.log("点击个人主页")
			等待粉丝列表 = self.em.wait_for(
				resource_id=self.粉丝数标识,
				timeout=15
			)
			if 等待粉丝列表:
				self.log("成功进入个人主页")
				return True
		return False
	
	def open_mine_followers_list(self):
		"""打开粉丝列表"""
		followers_btn = self.em.find_node(
			resource_id=self.粉丝数标识
		)
		return self.em.click_node(followers_btn) if followers_btn else False
	
	def get_mine_followers(self):
		"""
		获取当前屏幕可见的粉丝列表
		自有粉丝的标识是 后面是个移除按钮,而不是关注
		"""
		followers = []
		items = self.em.find_nodes(
			resource_id="com.instagram.android:id/follow_list_container"
		)
		
		for item in items:
			# 获取用户名和右侧按钮
			user_node = self.em.find_children(
				item,
				resource_id="com.instagram.android:id/follow_list_username"
			)
			button_node = self.em.find_children(
				item,
				resource_id="com.instagram.android:id/follow_list_right_follow_button"
			)
			
			# 只有当按钮显示"移除"时才认为是自己的粉丝
			if user_node and button_node and button_node[0].get(
					'text') == '移除':
				followers.append({
					'username': user_node[0].get('text', ''),
					'position': self.em.get_node_bounds(user_node[0])
				})
		return followers
	
	def open_follower_profile(self, position):
		"""打开指定位置的粉丝主页"""
		return self.em.click(position[0], position[1])


class EmulatorStarter:
	def __init__(self, em, config=None):
		self.em = em  # 接收Emulator实例
		self.config = config or {
			'max_retries': 3,
			'retry_interval': 5,
			'desktop_validation_timeout': 20
		}
	
	def start(self):
		"""执行带重试的模拟器启动流程"""
		retry_count = 0
		while not self.em.stop_flag and retry_count < self.config[
			'max_retries']:
			self.em.sendLog(
				f"尝试启动模拟器（第 {retry_count + 1}/{self.config['max_retries']} 次）")
			
			if not self._launch_emulator():
				return self._handle_launch_failure(retry_count)
			
			if self._validate_desktop():
				return True
			
			retry_count = self._handle_validation_failure(retry_count)
		
		self.em.sendLog(f"达到最大重试次数 {self.config['max_retries']} 次")
		return False
	
	def _launch_emulator(self):
		"""调用底层启动方法"""
		return self.em.start_emulator()
	
	def _validate_desktop(self):
		"""验证是否进入桌面"""
		# 弹窗等待10秒
		self._handle_app_crash()
		# 等待桌面元素出现
		self.em.sendLog("等待进入桌面")
		try:
			print('///////////////ffff')
			return self.em.ld.wait_for(
				resource_id="com.android.launcher3:id/folder_icon_name",
				timeout=self.config['desktop_validation_timeout']
			)
		except Exception as e:
			self.em.sendLog(f"桌面验证失败: {str(e)}")
			return False
	def _handle_launch_failure(self, retry_count):
		"""处理启动失败"""
		retry_count += 1
		if retry_count >= self.config['max_retries']:
			self.em.sendLog("模拟器启动失败")
			return False
		self.safe_sleep(10)
		return retry_count
	
	def _handle_validation_failure(self, retry_count):
		"""处理桌面验证失败"""
		self.em.sendLog("模拟器界面验证失败")
		self.em.stop_emulator()
		retry_count += 1
		if retry_count >= self.config['max_retries']:
			self.em.sendLog("模拟器启动失败")
			return retry_count
		self.safe_sleep(self.config['retry_interval'])
		return retry_count
	
	# 处理等待应用无响应的弹窗
	def _handle_app_crash(self):
		"""处理应用无响应弹窗"""
		self.em.sendLog(f"尝试关闭应用")
		try:
			self.em.ld.wait_for_image(self.em.hwnd,
			                          index=self.em.emulator_id,
			                          templates=r'img/dengdai.png',
			                          threshold=0.9,
			                          timeout=10,
			                          click_mode=1)
		except Exception as e:
			print(e)
			self.em.sendLog(f"应用无响应弹窗未出现")
			return
	
	def safe_sleep(self, seconds, timeout=None):
		"""可中断的短间隔等待（增加超时参数）
		:param timeout: 最大超时时间（秒），优先级高于seconds
		"""
		start_time = time.time()
		max_wait = timeout if timeout is not None else seconds
		interval = 0.1  # 100ms的间隔
		
		while (time.time() - start_time) < max_wait:
			if self.em.stop_flag:  # 只需要检测这一个标志位
				return False
			if (
					time.time() - start_time) + interval > max_wait:  # 剩余时间不足一个interval
				time.sleep(max_wait - (time.time() - start_time))
				break
			time.sleep(interval)
		return True


class V2rayManager:
	def __init__(self, emulator, **kwargs):
		self.em = emulator  # 模拟器实例
		# self.config = config  # 配置参数
		self.kwargs = kwargs
		self.package_name = 'com.v2ray.ang'
		self.start_time = None
		self.retry_count = 0
		self.node_client = kwargs.get('node_client', None)
		self.swipe_actions = [
			{"direction": "up", "area": (200, 700, 400, 200)},
			{"direction": "down", "area": (200, 250, 400, 600)},  # 下滑
			# {"direction": "random", "area": (300, 600, 400, 100)}  # 随机抖动
		]
		self.swipe_duration = random.randint(500, 1000)  # 随机滑动速度
	
	def start_with_retry(self, max_retries=3, retry_interval=10):
		"""带重试机制的v2ray启动流程"""
		v2ray节点失败次数 = 0
		v2ray已启动 = False
		while not self.em.stop_flag and self.retry_count < max_retries:
			self.em.sendLog(
				f"尝试启动v2ray（第 {self.retry_count + 1}/{max_retries} 次）")
			
			if not v2ray已启动 and not self.em.start_app(
					package_name=self.package_name):
				self.retry_count += 1
				self.safe_sleep(retry_interval)
				continue
			
			if not self._node_management():
				v2ray节点失败次数 += 1
				if v2ray节点失败次数 >= 2:
					self.em.sendLog("连续2次节点管理失败")
					result = "异常-v2ray启动异常"
					return False
				
				continue
			return True
		return False
	
	def _node_management(self):
		"""完整的节点管理流程"""
		self.start_time = time.time()
		selected = False
		是否更换随机节点 = False
		是否更新订阅 = self.kwargs.get('update_node', False)
		ping失败次数 = 0
		节点超时 = False
		节点是否选择成功 = False
		节点超时次数 = 0
		# 先判断列表是否为空
		已订阅 = False
		
		节点为空次数 = 0
		while not self.em.stop_flag and time.time() - self.start_time < 100:
			self.em.sendLog(
				f'v2ray循环检测中,用时 {time.time() - self.start_time} /100 秒')
			if not selected:
				if not self._check_nodes():
					self.em.sendLog("节点列表空,尝试导入订阅")
					节点为空次数 += 1
					if 节点为空次数 >= 2:
						self.em.sendLog("连续2次节点列表为空,重新启动v2ray")
						self.em.kill_app(
							**self.em.task_args_collection[
								self.em.v2ray_packageName])
						self.em.sendLog("等待5秒后重新启动v2ray")
						self.safe_sleep(5)
						self.em.start_app(
							**self.em.task_args_collection[
								self.em.v2ray_packageName])
						self.em.sendLog("重启成功等待5秒后继续")
						self.safe_sleep(5)
						节点为空次数 = 0
					self._import_subscription()
			
			if 是否更新订阅 and not 已订阅:
				self.em.sendLog("开始更新订阅")
				self._update_subscription()
				是否更新订阅 = False
				已订阅 = True
			
			if 是否更换随机节点:
				self.em.sendLog("开始随机选择节点")
				节点是否选择成功 = self._select_node()
			
			if not 节点是否选择成功:
				是否更换随机节点 = True
				是否更新订阅 = False
				continue
			if self._connect_node():
				self.em.sendLog("节点连接成功,开始测ping")
				ping_result = self._test_ping()
				if not ping_result:
					self.em.sendLog("ping测试失败,重新选择节点")
					selected = False
					是否更换随机节点 = True
					ping失败次数 += 1
					if ping失败次数 >= 2:
						self.em.sendLog("连续2次ping测试失败,重新启动v2ray")
						self.em.kill_app(
							**self.em.task_args_collection[
								self.em.v2ray_packageName])
						self.em.sendLog("等待5秒后重新启动v2ray")
						self.safe_sleep(5)
						self.em.start_app(
							**self.em.task_args_collection[
								self.em.v2ray_packageName])
						self.em.sendLog("重启成功等待5秒后继续")
						已订阅 = False
						是否更新订阅 = True
					continue
				self.em.sendLog("ping测试通过,任务完成")
				return True
		return False
	
	def _check_nodes(self, timeout=10):
		"""检查节点列表是否存在"""
		start_time = time.time()
		while not self.em.stop_flag and time.time() - start_time < timeout:
			if self.em.ld.find_node(resource_id="com.v2ray.ang:id/tv_name"):
				return True
			self.safe_sleep(2)
		return False
	
	def _import_subscription(self, max_retries=3):
		"""导入订阅链接"""
		self.em.sendLog(f'开始导入订阅')
		for _ in range(max_retries):
			if self.em.ld.wait_for(desc="添加配置", timeout=10, click=True):
				pyperclip.copy(self.node_client)
				if self.em.ld.wait_for(text="从剪贴板导入", timeout=15,
									click=True):
					self._update_subscription()
					return True
		return False
	
	def _select_node(self):
		"""智能随机选择节点"""
		timeout = 30  # 最大超时时间30秒
		start_time = time.time()
		成功点击节点 = False
		# self.em.sendLog(f'开始选择节点')
		for _ in range(random.randint(1, 5)):
			if 成功点击节点:
				return True
			# 超时检查
			# if time.time() - start_time > timeout:
			# 	self.em.sendLog("节点选择超时")
			# 	return False
			
			try:
				self.em.ld.random_swipe(self.swipe_actions, self.swipe_duration)
			except Exception as e:
				logger.info(f"滑动异常: {e}")
		
		# # 带超时的等待
		# if not self.safe_sleep(1, timeout - (time.time() - start_time)):
		# 	break
		#
		# 带超时的节点查找
		while time.time() - start_time < timeout:
			nodes = self.em.ld.find_nodes(resource_id="com.v2ray.ang:id/tv_name")
			
			if not nodes:
				self.em.sendLog("未找到任何可用节点")
				continue
			
			# 随机选择并直接点击
			try:
				selected = random.choice(nodes)
				success, _ = self.em.ld.click_node(selected)
				if success:
					成功点击节点 = True
					self.em.sendLog(f"成功点击节点: {selected.get('text', '')}")
					return True
				continue
			except Exception as e:
				self.em.sendLog(f"节点点击异常: {str(e)}")
				return False
		
		self.em.sendLog("未找到可用节点")
		return False
	
	def _connect_node(self, max_retries=5):
		"""节点连接逻辑"""
		try:
			for i in range(max_retries):
				node = self.em.ld.find_node(
					resource_id="com.v2ray.ang:id/tv_test_state")
				if node and '已连接' in node.get('text',
												 '') or '失败' in node.get(
					'text', ''):
					self.em.sendLog(f'节点已连接')
					return True
				if node and node.get('text') == '未连接':
					self.em.sendLog(f'节点未连接,尝试连接 {i + 1} 次')
					btn = self.em.ld.find_node(resource_id="com.v2ray.ang:id/fab")
					if btn and self.em.click_node(btn):
						self.em.sendLog("已点击连接按钮")
						self.safe_sleep(3)
						# 这里需要等待一个弹出的提示
						self.em.ld.wait_for(text__contains="确定", timeout=5,
										 click=True)
						self.safe_sleep(3)
				# return self._check_connection()
				self.safe_sleep(2)
			return False
		except Exception as e:
			self.em.sendLog(f"节点连接异常: {str(e)}")
			return False
	
	def _check_connection(self):
		"""验证连接状态"""
		node = self.em.ld.find_node(resource_id="com.v2ray.ang:id/tv_test_state")
		return bool(node and (
				'已连接' in node.get('text') or '失败' in node.get('text')))
	
	def _test_ping(self, timeout=30):
		"""测试节点延迟"""
		self.em.sendLog(f'开始测试延迟')
		start_time = time.time()
		retry_count = 0
		max_retry_count = 5
		ping失败次数 = 0
		while not self.em.stop_flag and time.time() - start_time < timeout:
			node = self.em.ld.find_node(
				resource_id="com.v2ray.ang:id/tv_test_state")
			if node and '成功' in node.get('text', ''):
				self.em.sendLog(f'延迟测试通过')
				return True
			retry_count += 1
			ping失败次数 += 1
			if retry_count >= max_retry_count:
				self.em.sendLog(f'连续 {max_retry_count} 次测试失败,更换节点')
				ping失败次数 = 0
				break
			self.em.sendLog(
				f'测ping失败 {ping失败次数} ,重试中 {retry_count}/{max_retry_count}次 ')
			self.em.click_node(node)
			self.safe_sleep(2)
		return False
	
	def _update_subscription(self):
		"""更新订阅"""
		self.em.ld.wait_for(desc="更多选项", timeout=10, click=True)
		self.em.ld.wait_for(text="更新订阅", timeout=10, click=True)
		self.em.sendLog("更新订阅成功,10秒后继续")
		self.safe_sleep(10)
	
	def random_swipe(self, actions: list, duration: int):
		""" 执行智能随机滑动 """
		action = random.choice(actions)  # 改为随机选择单个动作
		direction = action['direction']
		x1, y1, x2, y2 = action['area']
		logger.info(f'开始随机滑动,动作: {actions}, 持续时间: {duration}')
		# 确保 x1 < x2, y1 < y2
		if x1 > x2:
			x1, x2 = x2, x1
		if y1 > y2:
			y1, y2 = y2, y1
		
		if direction == 'up':
			# 向上滑动，起始点 y 坐标大，结束点 y 坐标小
			start_x = random.randint(x1, x2)
			start_y = random.randint(int(y1 * 0.8), y1)
			end_x = random.randint(x1, x2)
			end_y = random.randint(y2, int(y2 * 1.2))
		elif direction == 'down':
			# 向下滑动，起始点 y 坐标小，结束点 y 坐标大
			start_x = random.randint(x1, x2)
			start_y = random.randint(y2, int(y2 * 1.2))
			end_x = random.randint(x1, x2)
			end_y = random.randint(int(y1 * 0.8), y1)
		
		start = (start_x, start_y)
		end = (end_x, end_y)
		
		# 添加随机抖动（保留原有逻辑）
		start = (start[0] + random.randint(-10, 10),
				 start[1] + random.randint(-5, 5))
		end = (
			end[0] + random.randint(-10, 10),
			end[1] + random.randint(-5, 5))
		
		logger.info(f'开始随机滑动1: {start} -> {end}')
		# 添加随机抖动
		start = (start[0] + random.randint(-10, 10),
				 start[1] + random.randint(-5, 5))
		end = (
			end[0] + random.randint(-10, 10),
			end[1] + random.randint(-5, 5))
		logger.info(f'开始随机滑动2: {start} -> {end}')
		self.em.ld.swipe(
			index=self.em.emulator_id,
			start=start,
			end=end,
			duration=duration
		)
		logger.info("滑动完毕")
		# 滑动后稳定等待
		self.safe_sleep(0.5 + random.random())
	
	def safe_sleep(self, seconds, timeout=None):
		"""可中断的短间隔等待（增加超时参数）
		:param timeout: 最大超时时间（秒），优先级高于seconds
		"""
		start_time = time.time()
		max_wait = timeout if timeout is not None else seconds
		interval = 0.1  # 100ms的间隔
		
		while (time.time() - start_time) < max_wait:
			if self.em.stop_flag:  # 只需要检测这一个标志位
				return False
			if (
					time.time() - start_time) + interval > max_wait:  # 剩余时间不足一个interval
				time.sleep(max_wait - (time.time() - start_time))
				break
			time.sleep(interval)
		return True


class LogWindow(QWidget):
	def __init__(self, emulator_hwnd):  # 添加 parent 参数
		super().__init__()  # 指定父对象
		self.emulator_hwnd = emulator_hwnd
		self.log_window_height = 20  # 固定日志窗口高度
		self.init_ui()
		self.init_position_tracker()
	
	def init_ui(self):
		self.setWindowTitle("模拟器日志")
		# 添加置顶属性
		self.setWindowFlags(
			Qt.WindowType.Tool |
			Qt.WindowType.FramelessWindowHint |
			Qt.WindowType.WindowStaysOnTopHint
		)
		
		# 日志显示区域调整为单行
		self.log_area = QTextEdit()
		self.log_area.setReadOnly(True)
		self.log_area.setVerticalScrollBarPolicy(
			Qt.ScrollBarPolicy.ScrollBarAlwaysOff)  # 隐藏垂直滚动条
		self.log_area.setHorizontalScrollBarPolicy(
			Qt.ScrollBarPolicy.ScrollBarAlwaysOff)  # 隐藏水平滚动条
		self.log_area.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)  # 禁用自动换行
		
		# 调整样式表（删除滚动条相关样式）
		self.log_area.setStyleSheet("""
					QTextEdit {
						background-color: black;
						color: #00FF00;
						font-family: Consolas;
						font-size: 10pt;
						border: 1px solid #444444;
						padding: 2px;
					}
				""")
		
		# 布局设置
		layout = QVBoxLayout()
		layout.addWidget(self.log_area)
		self.setLayout(layout)
	
	def init_position_tracker(self):
		self.timer = QTimer(self)
		self.timer.timeout.connect(self.update_position)
		self.timer.start(200)
	
	def update_position(self):
		try:
			rect = win32gui.GetWindowRect(self.emulator_hwnd)
			emulator_width = rect[2] - rect[0]
			emulator_height = rect[3] - rect[1]
			
			# 调整到模拟器窗口下方
			self.resize(emulator_width, self.log_window_height)
			self.move(rect[0], rect[3])  # 使用模拟器底部坐标
		
		except Exception as e:
			print(f"窗口定位失败: {str(e)}")
	
	def append_log(self, message):
		self.log_area.append(message)
		self.log_area.verticalScrollBar().setValue(
			self.log_area.verticalScrollBar().maximum()
		)


import pymysql
from pymysql import cursors
from typing import List, Dict, Tuple, Union
import logging
import time

import pymysql
import json
from typing import List, Dict, Any


class AdvancedDatabaseManager:
	def __init__(self, config: Dict[str, Any]):
		self.config = config
		self.pool = []
		self.connection = None
		self._create_database()  # 新增数据库创建检查
	
	# self._init_pool()
	
	def _init_pool(self):
		"""初始化连接池"""
		for _ in range(self.config['pool_size']):
			connection = pymysql.connect(
				host=self.config['host'],
				user=self.config['user'],
				password=self.config['password'],
				db=self.config['database'],
				port=self.config['port'],
				charset='utf8mb4',
				cursorclass=pymysql.cursors.DictCursor
			)
			self.pool.append(connection)
		if self.pool and self.pool[0]:
			self.connection = self.pool[0]
			# self.connected_signal.emit("连接成功")  # 连接成功信号发射
			print("数据库连接成功")
		
		# 添加连接池监控日志
		logger.info(f"成功创建连接池，当前连接数：{len(self.pool)}")
	
	def close(self):
		"""关闭数据库连接池"""
		# 遍历连接池关闭所有连接
		if hasattr(self, 'pool') and self.pool:
			for conn in self.pool:
				try:
					if conn.open:  # 仅关闭活跃连接
						conn.close()
						print(f"已关闭数据库连接：{id(conn)}")
				except Exception as e:
					print(f"关闭连接时发生错误：{str(e)}")
			self.pool.clear()  # 清空连接池
			print("数据库连接池已清空")
		self.connection = None
	
	def _create_database(self):
		"""在连接前检查并创建数据库"""
		temp_conn = pymysql.connect(
			host=self.config['host'],
			user=self.config['user'],
			password=self.config['password'],
			port=self.config['port'],
			charset='utf8mb4',
			cursorclass=pymysql.cursors.DictCursor
		)
		
		try:
			with temp_conn.cursor() as cursor:
				# 创建数据库（如果不存在）
				cursor.execute(f"""
					CREATE DATABASE IF NOT EXISTS {self.config['database']}
					CHARACTER SET utf8mb4
					COLLATE utf8mb4_unicode_ci
				""")
			temp_conn.commit()
		finally:
			temp_conn.close()
	
	def create_target_users_table(self):
		"""创建目标用户表（带注释和索引）"""
		columns = [
			('id', 'INT AUTO_INCREMENT PRIMARY KEY'),  # 主键
			('username', 'VARCHAR(255) NOT NULL UNIQUE'),  # 用户名
			('status', 'TINYINT NOT NULL DEFAULT 0'),  # 0: 未处理, 1: 处理中, 2: 已完成
			('retry_count', 'INT DEFAULT 0'),  # 重试次数
			('last_processed', 'DATETIME'),  # 上次处理时间
			('machine_id', 'VARCHAR(50)'),  # 机器ID
			('is_private', 'BOOLEAN DEFAULT FALSE'),  # 是否私密
			('is_blue_v', 'BOOLEAN DEFAULT FALSE'),  # 是否蓝V
			('follower_count', 'INT DEFAULT 0'),  # 粉丝数
			('region', 'VARCHAR(50)'),  # 地区
			('priority', 'TINYINT DEFAULT 5'),  # 优先级
			('task_source', 'VARCHAR(50)'),  # 任务来源
			('remark', 'TEXT'),  # 备注
			('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),  # 创建时间
			('updated_at',
			 'DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
			# 更新时间
			('updated_at',
			 'DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
			# 更新时间
		]
		
		with self.pool[0].cursor() as cursor:
			# 创建表时直接定义索引
			cursor.execute(f"""
				CREATE TABLE IF NOT EXISTS `{self.config['table_name']}` (
					id INT AUTO_INCREMENT PRIMARY KEY,
					username VARCHAR(255) NOT NULL UNIQUE,
					status TINYINT NOT NULL DEFAULT 0,
					retry_count INT DEFAULT 0,
					last_processed DATETIME,
					machine_id VARCHAR(50),
					is_private BOOLEAN DEFAULT FALSE,
					is_blue_v BOOLEAN DEFAULT FALSE,
					follower_count INT DEFAULT 0,
					region VARCHAR(50),
					priority TINYINT DEFAULT 5,
					task_source VARCHAR(50),
					remark TEXT,
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
					updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

					# 索引定义直接放在表结构中
					INDEX idx_status (status),
					INDEX idx_region (region),
					INDEX idx_machine (machine_id),
					INDEX idx_username (username)
				) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
			""")
			self.pool[0].commit()
	
	# 核心操作方法
	def batch_upsert_users(self, users: List[Dict[str, Any]]):
		"""更新后的批量插入方法"""
		sql = f"""
		INSERT INTO {self.config['table_name']} (
			username,
			status,
			follower_count,
			region
		)
		VALUES (%s, %s, %s, %s)
		ON DUPLICATE KEY UPDATE
			status = VALUES(status),
			follower_count = VALUES(follower_count),
			region = VALUES(region)
		"""
		params = [
			(
				u['username'],
				u.get('status', 0),
				u.get('follower_count', 0),
				u.get('region', '')
			)
			for u in users
		]
		
		conn = self.pool.pop(0)
		try:
			with conn.cursor() as cursor:
				cursor.executemany(sql, params)
				conn.commit()
			return cursor.rowcount
		finally:
			self.pool.append(conn)
	
	def assign_task(self, machine_id: str) -> dict:
		"""单账号分配方法 每次只获取一个未处理账号"""
		conn = self.pool.pop(0)
		try:
			with conn.cursor() as cursor:
				conn.begin()
				
				# 获取未处理账号（带行锁）
				cursor.execute(f"""
					SELECT id, username
					FROM {self.config['table_name']}
					WHERE status = 0
					ORDER BY priority DESC
					LIMIT 1
					FOR UPDATE SKIP LOCKED
				""")
				task = cursor.fetchone()
				
				if task:
					# 标记状态为处理中 (status=1)
					cursor.execute(f"""
						UPDATE {self.config['table_name']}
						SET status = 1,
							machine_id = %s,
							last_processed = NOW()
						WHERE id = %s
					""", (machine_id, task['id']))
				
				conn.commit()
				return task
		finally:
			self.pool.append(conn)
	
	def update_field(self, field_name: str, new_value: Any,
					 condition_field: str, condition_value: Any) -> bool:
		"""
		通用字段更新方法
		:param field_name: 要更新的字段名（需预先校验合法性）
		:param new_value: 新值
		:param condition_field: 条件字段名
		:param condition_value: 条件值
		:return: 是否更新成功
		"""
		# 允许更新的字段白名单
		allowed_fields = ['username', 'status', 'priority', 'is_private',
						  'is_blue_v', 'machine_id',
						  'last_processed', 'follower_count', 'retry_count']
		
		if field_name not in allowed_fields:
			raise ValueError(f"不允许更新字段: {field_name}")
		
		conn = self.pool.pop()
		try:
			with conn.cursor() as cursor:
				sql = f"""
					   UPDATE {self.config['table_name']}
					   SET {field_name} = %s
					   WHERE {condition_field} = %s
				   """
				affected_rows = cursor.execute(sql,
											   (new_value, condition_value))
				conn.commit()
				return affected_rows > 0
		except pymysql.Error as e:
			conn.rollback()
			logger.error(f"更新字段失败: {str(e)}")
			return False
		finally:
			self.pool.append(conn)
	
	def update_task_status(self, task_id: int, status: int, remark: str = ""):
		"""更新任务状态"""
		sql = f"""
		UPDATE {self.config['table_name']}
		SET status = %s,
			remark = CONCAT(IFNULL(remark, ''), %s),
			updated_at = NOW()
		WHERE id = %s
		"""
		conn = self.pool.pop(0)
		try:
			with conn.cursor() as cursor:
				cursor.execute(sql, (status, remark, task_id))
				conn.commit()
				return cursor.rowcount
		finally:
			self.pool.append(conn)
	
	def complete_task(self, task_id: int, username: str):
		conn = self.pool.pop(0)
		try:
			with conn.cursor() as cursor:
				# 添加username作为复合条件
				cursor.execute(f"""
					UPDATE {self.config['table_name']}
					SET status = 2,
						machine_id = NULL
					WHERE id = %s
					AND username = %s
				""", (task_id, username))
				conn.commit()
		finally:
			self.pool.append(conn)
	
	def fail_task(self, task_id: int, username: str, max_retries=3):
		conn = self.pool.pop(0)
		try:
			with conn.cursor() as cursor:
				# 双重验证条件
				cursor.execute(f"""
					UPDATE {self.config['table_name']}
					SET status = IF(retry_count >= %s, 3, 0),
						retry_count = retry_count + 1,
						machine_id = NULL
					WHERE id = %s
					AND username = %s
				""", (max_retries - 1, task_id, username))
				conn.commit()
		finally:
			self.pool.append(conn)
	
	def import_users_from_txt(self, file_path: str):
		"""
		从TXT文件导入用户名到数据库
		:param file_path: TXT文件路径，每行一个用户名
		:return: 成功导入的数量
		"""
		imported_count = 0
		conn = self.pool.pop(0)
		
		try:
			with conn.cursor() as cursor:
				# 读取文件并去重
				with open(file_path, 'r', encoding='utf-8') as f:
					usernames = {line.strip() for line in f if line.strip()}
				
				# 批量插入（使用INSERT IGNORE避免重复）
				cursor.executemany(f"""
					INSERT IGNORE INTO {self.config['table_name']}
					(username, status, priority, machine_id, last_processed, retry_count)
					VALUES (%s, 0, 1, NULL, NULL, 0)
				""", [(username,) for username in usernames])
				
				conn.commit()
				imported_count = cursor.rowcount
				# 弹出提示  导入成功
				QMessageBox.information(
					None, "导入完成",
					f"成功导入 {imported_count} 个用户"
				)
		
		except FileNotFoundError:
			logger.error(f"文件不存在: {file_path}")
		except Exception as e:
			logger.error(f"导入用户时发生错误: {str(e)}")
			conn.rollback()
		finally:
			self.pool.append(conn)
			return imported_count
# 示例用法
# if __name__ == "__main__":
# 	em = EmulatorThread(3,2,None)
#
# 	em._refresh_xml_cache()

# 	db_config = {
# 		'host': '*************',
# 		'user': 'root',
# 		'password': 'xixi1214X!@#',
# 		'database': 'test_db',
# 		'port': 3306,
# 		'pool_size': 3
# 	}
#
# 	db = AdvancedDatabaseManager(db_config)
#
# 	# 初始化表结构
# 	db.create_target_users_table()

# 批量导入测试数据
# test_users = [
# 	{'username': 'user1', 'follower_count': 1000, 'region': '日本'},
# 	{'username': 'user2', 'status': 3, 'remark': '已失效账号'}
# ]
# print(f"插入/更新 {db.batch_upsert_users(test_users)} 条记录")
#
# # 模拟任务分配
# assigned_tasks = db.assign_task('LD-001', 5)
# print(f"分配到 {len(assigned_tasks)} 个任务")
#
# 更新任务状态（示例）
# if assigned_tasks:
# 	db.update_task_status(assigned_tasks[0]['id'], 2, '处理成功')

