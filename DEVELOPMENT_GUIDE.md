# 🛡️ 企业级开发指南

> 📋 **完整的开发标准和质量保障指南** - 确保每一行代码都符合企业级标准

---

## 🎯 核心开发原则

### 🚨 零容忍政策（最高优先级）

#### 1. UI阻塞零容忍
- **绝对禁止**: 在UI线程中使用任何阻塞操作
- **响应时间**: UI响应必须 < 16ms（60 FPS标准）
- **强制异步**: 所有耗时操作必须使用异步处理
- **实时检测**: 每次修改UI代码后必须运行UI阻塞检测

#### 2. 架构违规零容忍
- **对应代码放在对应文件**: 严格遵循分层架构
- **禁止跨层调用**: UI层不能直接调用数据层
- **统一接口**: 使用事件总线或依赖注入解耦

#### 3. 功能分组零容忍
- **所有文件必须有清晰的功能分组**: 使用标准化注释格式
- **方法按功能组织**: 初始化→业务→事件→更新→私有
- **职责单一**: 每个类和方法只负责一个明确功能

#### 4. 只增不删零容忍
- **删除冗余代码**: 修改时必须删除不再使用的代码
- **合并重复逻辑**: 发现重复必须提取为公共方法
- **统一验证逻辑**: 避免多套检查逻辑

---

## 🚀 强制性开发流程

### 📋 每次开发前必须执行

```bash
# 🚀 强制性开发入口（每次开发前必须运行）
python start_development.py "具体任务描述"

# 示例：
python start_development.py "实现模拟器管理功能"
python start_development.py "修复UI阻塞问题"
python start_development.py "添加代理管理模块"
```

### 🔍 开发入口检查项目

1. **📚 强制检查开发标准文档**
   - 验证 DEVELOPMENT_GUIDE.md 存在且完整
   - 验证 DEV_PLAN.md 存在且完整
   - 确保开发者已阅读并理解企业级标准

2. **🔧 验证开发环境**
   - 检查Python版本（需要3.12+）
   - 验证所有依赖包已安装
   - 检查项目结构完整性

3. **🛠️ 验证质量工具**
   - 检查所有质量检查工具可用
   - 测试工具功能正常
   - 验证配置文件正确

4. **🔗 检查Git钩子状态**
   - 验证Git钩子已安装
   - 检查钩子健康状态
   - 自动修复损坏的钩子

5. **✅ 启动实时监控**
   - 自动启动UI阻塞监控
   - 自动启动功能分组监控
   - 实时检测代码质量问题

---

## 🏗️ 分层架构标准

### 📁 严格的分层结构

```
┌─────────────────┐
│   UI层 (ui/)    │ ← 用户界面，事件处理
├─────────────────┤
│ 业务层(business/)│ ← 业务逻辑，流程控制  
├─────────────────┤
│ 数据层 (data/)  │ ← 数据访问，持久化
├─────────────────┤
│ 系统层(system/) │ ← 系统调用，外部接口
├─────────────────┤
│ 核心层 (core/)  │ ← 基础设施，工具类
└─────────────────┘
```

### 🚫 禁止的跨层调用

- **UI层** 禁止直接调用 **数据层** 或 **系统层**
- **业务层** 禁止直接调用 **UI层**
- **数据层** 禁止调用 **业务层** 或 **UI层**
- **系统层** 禁止调用 **业务层** 或 **UI层**

### ✅ 正确的通信方式

```python
# ✅ 正确：通过事件总线通信
class MainWindow(QMainWindow):
    def on_button_click(self):
        # UI层发送事件到业务层
        self.event_bus.emit('emulator.start', {'id': 'emu1'})

class EmulatorHandler:
    def handle_start_request(self, data):
        # 业务层处理逻辑
        result = self.unified_manager.start_emulator(data['id'])
        # 通过事件总线通知UI更新
        self.event_bus.emit('ui.update.status', result)
```

---

## 🎯 代码组织标准

### 📋 标准化功能分组注释

```python
# ========================================
# 🎯 [功能组名称] - 必须标注
# ========================================
# 功能描述: [详细描述该功能组的作用和职责]
# 主要方法: [列出核心方法名称]
# 调用关系: [说明与其他组件的调用关系]
# 注意事项: [重要的使用注意事项和限制]
# ========================================
```

### 📊 方法组织顺序

```python
class ExampleClass:
    # ========================================
    # 🔧 初始化方法组
    # ========================================
    def __init__(self):
        pass
    
    def setup_components(self):
        pass
    
    # ========================================
    # 🎯 核心业务方法组
    # ========================================
    def main_business_method(self):
        pass
    
    # ========================================
    # 📡 事件处理方法组
    # ========================================
    def on_event_occurred(self):
        pass
    
    def handle_user_action(self):
        pass
    
    # ========================================
    # 🔄 UI更新方法组
    # ========================================
    def update_display(self):
        pass
    
    def refresh_status(self):
        pass
    
    # ========================================
    # 🛠️ 私有工具方法组
    # ========================================
    def _private_helper(self):
        pass
```

---

## 🚨 UI线程安全指南

### ❌ 绝对禁止的操作

```python
# ❌ 错误：UI线程中的阻塞操作
def on_button_click(self):
    time.sleep(2)  # 禁止！
    subprocess.run(['cmd'])  # 禁止！
    requests.get('http://api.com')  # 禁止！
    input('请输入:')  # 禁止！
```

### ✅ 正确的异步处理

```python
# ✅ 正确：使用事件总线异步处理
def on_button_click(self):
    # UI层只负责发送事件
    self.event_bus.emit('business.process_data', {'data': self.get_input()})

# ✅ 正确：使用QTimer延迟执行
def delayed_action(self):
    QTimer.singleShot(100, self.actual_action)

# ✅ 正确：使用异步业务层
async def handle_business_logic(self):
    result = await self.async_operation()
    self.event_bus.emit('ui.update', result)
```

### 🛡️ UI更新管理器

```python
class UIUpdateManager:
    def safe_update_ui(self, update_func, *args, **kwargs):
        """安全的UI更新方法，确保在主线程执行"""
        if threading.current_thread() == threading.main_thread():
            update_func(*args, **kwargs)
        else:
            QTimer.singleShot(0, lambda: update_func(*args, **kwargs))
```

---

## 🔧 质量保障工具使用

### 🚨 实时监控工具

```bash
# UI阻塞实时监控（开发时必须启动）
python 质量保障工具/ui_blocking_detector.py --watch

# 功能分组实时监控
python 质量保障工具/check_function_grouping.py --watch
```

### 🔍 手动质量检查

```bash
# 完整质量检查套件
python 质量保障工具/ui_blocking_detector.py --ci
python 质量保障工具/check_code_cleanup.py
python 质量保障工具/check_function_grouping.py
python 质量保障工具/check_layer_compliance.py
```

### 🛡️ Git钩子自动检查

Git钩子会在以下时机自动执行：

- **Pre-commit**: UI阻塞检测 + 代码清理 + 功能分组检查
- **Pre-push**: 分层架构检查 + 完整UI阻塞检测

---

## 📝 代码质量标准

### 📏 函数长度限制
- **建议**: ≤ 50行
- **强制**: ≤ 100行
- **超过限制**: 必须拆分为多个函数

### 📋 文档要求
```python
def example_function(param1: str, param2: int) -> bool:
    """
    函数功能的简短描述
    
    Args:
        param1: 参数1的详细说明
        param2: 参数2的详细说明
    
    Returns:
        返回值的详细说明
    
    Raises:
        Exception: 可能抛出的异常说明
    
    Note:
        重要的使用注意事项
    """
    pass
```

### 🚫 代码清理要求

```python
# ❌ 错误：只增不删，导致重复逻辑
def validate_path_old(self, path):  # 旧方法未删除
    pass

def validate_path_new(self, path):  # 新方法
    pass

# ✅ 正确：删除冗余，统一逻辑
def validate_path(self, path):  # 统一的验证方法
    return self.path_validator.validate(path)  # 使用统一服务
```

---

## 🔄 开发会话管理

### 📊 查看开发状态
```bash
# 查看当前活跃会话
python development_session_manager.py --status

# 生成开发报告
python development_session_manager.py --report 7

# 停止所有会话
python development_session_manager.py --stop-all
```

### 📈 开发统计
- **会话时长统计**: 跟踪每次开发会话的时间
- **任务类型分析**: 分析开发任务的分布
- **质量指标**: 记录质量检查通过率

---

## 🆘 故障排除

### 常见问题解决

#### 🔧 环境问题
```bash
python 质量保障工具/pre_development_check.py
```

#### 🛠️ 工具问题
```bash
python 质量保障工具/git_quality_enforcer.py --check-tools
```

#### 🔗 钩子问题
```bash
python 质量保障工具/git_hooks_manager.py --repair-all
```

#### 📊 会话问题
```bash
python development_session_manager.py --stop-all
```

---

## 📚 相关文档

- **README.md** - 项目总览和快速开始
- **DEV_PLAN.md** - 详细开发计划和技术方案
- **质量保障工具/README.md** - 质量工具使用指南

---

## 🎯 实际开发示例

### 📝 正确的开发流程示例

#### 1. 开始开发
```bash
# 每次开发前必须运行
python start_development.py "实现模拟器状态监控功能"
```

#### 2. 创建新功能（遵循分层架构）

**UI层 (ui/emulator_status_widget.py)**
```python
# ========================================
# 🎯 模拟器状态显示功能组
# ========================================
# 功能描述: 负责显示模拟器状态信息的UI组件
# 主要方法: update_status(), refresh_display()
# 调用关系: 接收事件总线消息，更新UI显示
# 注意事项: 禁止直接调用业务逻辑，只负责UI展示
# ========================================

class EmulatorStatusWidget(QWidget):
    def __init__(self, event_bus):
        super().__init__()
        self.event_bus = event_bus
        self.setup_ui()
        self.setup_event_listeners()

    # ========================================
    # 🔧 初始化方法组
    # ========================================
    def setup_ui(self):
        """设置UI组件"""
        self.status_label = QLabel("状态: 未知")
        # ... UI设置代码

    def setup_event_listeners(self):
        """设置事件监听器"""
        self.event_bus.subscribe('emulator.status.updated', self.on_status_updated)

    # ========================================
    # 📡 事件处理方法组
    # ========================================
    def on_status_updated(self, status_data):
        """处理状态更新事件"""
        # ✅ 正确：只处理UI更新，不包含业务逻辑
        self.update_status_display(status_data)

    # ========================================
    # 🔄 UI更新方法组
    # ========================================
    def update_status_display(self, status_data):
        """更新状态显示"""
        self.status_label.setText(f"状态: {status_data['status']}")
        # 更多UI更新代码...
```

**业务层 (business/emulator_status_monitor.py)**
```python
# ========================================
# 🎯 模拟器状态监控功能组
# ========================================
# 功能描述: 负责监控模拟器状态变化的业务逻辑
# 主要方法: start_monitoring(), check_status()
# 调用关系: 调用系统层接口，通过事件总线通知UI层
# 注意事项: 所有状态检查必须异步执行，避免阻塞
# ========================================

class EmulatorStatusMonitor:
    def __init__(self, event_bus, emulator_manager):
        self.event_bus = event_bus
        self.emulator_manager = emulator_manager
        self.monitoring_active = False

    # ========================================
    # 🎯 核心业务方法组
    # ========================================
    async def start_monitoring(self):
        """开始状态监控"""
        self.monitoring_active = True
        while self.monitoring_active:
            # ✅ 正确：异步执行，不阻塞UI
            await self.check_all_emulators()
            await asyncio.sleep(5)  # 5秒检查一次

    async def check_all_emulators(self):
        """检查所有模拟器状态"""
        emulators = await self.emulator_manager.get_all_emulators()
        for emulator in emulators:
            status = await self.emulator_manager.get_status(emulator.id)
            # 通过事件总线通知UI更新
            self.event_bus.emit('emulator.status.updated', {
                'emulator_id': emulator.id,
                'status': status
            })
```

#### 3. 提交代码（自动质量检查）
```bash
# Git提交时会自动执行质量检查
git add .
git commit -m "实现模拟器状态监控功能"
# 自动执行: UI阻塞检测 + 代码清理 + 功能分组检查

git push
# 自动执行: 分层架构检查 + 完整UI阻塞检测
```

### 🚫 常见错误示例

#### ❌ 错误的跨层调用
```python
# ❌ 错误：UI层直接调用数据层
class MainWindow(QMainWindow):
    def on_button_click(self):
        # 错误：UI层不应该直接访问数据库
        data = self.database.query("SELECT * FROM emulators")
        self.update_display(data)
```

#### ❌ 错误的UI阻塞操作
```python
# ❌ 错误：UI线程中的阻塞操作
def on_start_emulator(self):
    # 错误：会阻塞UI线程
    result = subprocess.run(['adb', 'devices'], capture_output=True)
    self.show_result(result.stdout)
```

#### ❌ 错误的功能分组
```python
# ❌ 错误：缺少功能分组注释，方法组织混乱
class BadExample:
    def __init__(self):
        pass

    def _private_method(self):  # 私有方法放在前面（错误）
        pass

    def public_method(self):    # 公共方法放在后面（错误）
        pass

    def on_event(self):         # 事件处理方法位置错误
        pass
```

---

## 🔧 高级开发技巧

### 🎯 事件总线最佳实践

```python
# ✅ 正确的事件总线使用
class EventBusExample:
    def __init__(self, event_bus):
        self.event_bus = event_bus
        self.setup_event_listeners()

    def setup_event_listeners(self):
        """集中设置所有事件监听器"""
        self.event_bus.subscribe('data.loaded', self.on_data_loaded)
        self.event_bus.subscribe('error.occurred', self.on_error_occurred)

    def trigger_data_load(self):
        """触发数据加载事件"""
        self.event_bus.emit('data.load.requested', {
            'source': 'user_action',
            'timestamp': datetime.now().isoformat()
        })

    def on_data_loaded(self, data):
        """处理数据加载完成事件"""
        # 处理数据加载完成后的逻辑
        pass
```

### 🛡️ 异步操作模式

```python
# ✅ 正确的异步操作模式
class AsyncOperationExample:
    def __init__(self, event_bus):
        self.event_bus = event_bus
        self.thread_pool = QThreadPool()

    def start_long_operation(self):
        """启动长时间运行的操作"""
        # 创建工作线程
        worker = LongOperationWorker()
        worker.signals.finished.connect(self.on_operation_finished)
        worker.signals.error.connect(self.on_operation_error)

        # 在线程池中执行
        self.thread_pool.start(worker)

    def on_operation_finished(self, result):
        """操作完成回调（在主线程中执行）"""
        self.event_bus.emit('operation.completed', result)

    def on_operation_error(self, error):
        """操作错误回调"""
        self.event_bus.emit('operation.failed', {'error': str(error)})

class LongOperationWorker(QRunnable):
    def __init__(self):
        super().__init__()
        self.signals = WorkerSignals()

    def run(self):
        """在后台线程中执行的操作"""
        try:
            # 执行耗时操作
            result = self.perform_long_operation()
            self.signals.finished.emit(result)
        except Exception as e:
            self.signals.error.emit(e)
```

### 📊 性能监控集成

```python
# ✅ 集成性能监控
class PerformanceAwareComponent:
    def __init__(self, performance_monitor):
        self.performance_monitor = performance_monitor

    @performance_monitor.measure_time
    def critical_operation(self):
        """关键操作（自动测量执行时间）"""
        # 关键业务逻辑
        pass

    def ui_update_operation(self):
        """UI更新操作（检查响应时间）"""
        start_time = time.time()

        # UI更新逻辑
        self.update_ui_components()

        # 检查是否超过16ms
        duration = (time.time() - start_time) * 1000
        if duration > 16:
            self.performance_monitor.log_ui_blocking(duration)
```

---

## 📋 开发检查清单

### ✅ 每次提交前必须检查

- [ ] **UI线程安全**: 没有任何UI阻塞操作
- [ ] **分层架构**: 代码放在正确的层级目录
- [ ] **功能分组**: 所有文件都有清晰的功能分组注释
- [ ] **方法组织**: 方法按正确顺序组织
- [ ] **代码清理**: 删除了冗余代码和重复逻辑
- [ ] **文档完整**: 所有方法都有完整的文档字符串
- [ ] **错误处理**: 适当的异常处理和日志记录
- [ ] **测试覆盖**: 关键功能有对应的单元测试

### ✅ 每次推送前必须检查

- [ ] **架构合规**: 通过分层架构检查
- [ ] **完整测试**: 运行完整的质量检查套件
- [ ] **性能验证**: 确认没有性能回归
- [ ] **文档更新**: 相关文档已更新

---

**🎯 记住：质量是我们的生命线，用户体验是我们的追求！每次开发都要遵循企业级标准！**
