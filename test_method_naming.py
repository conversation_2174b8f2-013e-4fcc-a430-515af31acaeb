#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的方法命名和向后兼容性
验证多语言支持方法的命名改进
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.leidianapi.LeiDian_Reorganized import Dnconsole
from core.logger_manager import log_info, log_error, log_warning
from core.simple_config import get_config_manager

class MethodNamingTester:
    def __init__(self):
        # 初始化配置管理器
        self.config_manager = get_config_manager()
        
        # 从配置文件获取雷电模拟器路径
        base_path = self.config_manager.get("emulator_path", "G:/leidian/LDPlayer9")
        share_path = self.config_manager.get("basic_config.emulator_shared_path", "C:/Users/<USER>/Documents/leidian64")
        
        if not share_path:
            share_path = "C:/Users/<USER>/Documents/leidian64"
        
        self.ld = Dnconsole(base_path=base_path, share_path=share_path)
        self.emulator_id = 2
        
    async def test_method_naming(self):
        """测试新的方法命名和向后兼容性"""
        log_info("=" * 80)
        log_info("🧪 测试新的方法命名和向后兼容性")
        log_info("=" * 80)
        
        # 测试1: 新方法名
        await self.test_new_method_names()
        
        # 测试2: 向后兼容性
        await self.test_backward_compatibility()
        
        # 测试3: 功能一致性
        await self.test_functionality_consistency()
        
        # 测试4: 方法文档
        await self.test_method_documentation()
        
    async def test_new_method_names(self):
        """测试新的方法名"""
        log_info("\n🆕 测试新的方法名")
        log_info("-" * 60)
        
        try:
            # 测试新的主要方法
            log_info("📝 测试 execute_ld_with_multilingual_support:")
            success, xml_content = self.ld.execute_ld_with_multilingual_support(
                self.emulator_id, 
                "uiautomator dump /sdcard/ui_new_method.xml"
            )
            
            if success and xml_content:
                # 分析多语言字符
                korean_chars = len([c for c in xml_content if '\uac00' <= c <= '\ud7af'])
                chinese_chars = len([c for c in xml_content if '\u4e00' <= c <= '\u9fff'])
                japanese_chars = len([c for c in xml_content if '\u3040' <= c <= '\u309f' or '\u30a0' <= c <= '\u30ff'])
                
                log_info(f"   ✅ 成功获取XML内容")
                log_info(f"   📊 多语言字符统计:")
                log_info(f"      韩文字符: {korean_chars} 个")
                log_info(f"      中文字符: {chinese_chars} 个")
                log_info(f"      日文字符: {japanese_chars} 个")
                log_info(f"      XML总长度: {len(xml_content):,} 字符")
            else:
                log_error("   ❌ 新方法执行失败")
            
            # 测试新的便捷方法
            log_info("\n📝 测试 get_ui_xml_with_multilingual_support:")
            success, xml_content2 = self.ld.get_ui_xml_with_multilingual_support(self.emulator_id)
            
            if success and xml_content2:
                log_info(f"   ✅ 便捷方法成功")
                log_info(f"   📊 XML长度: {len(xml_content2):,} 字符")
            else:
                log_error("   ❌ 便捷方法执行失败")
                
        except Exception as e:
            log_error(f"❌ 新方法测试失败: {e}")
    
    async def test_backward_compatibility(self):
        """测试向后兼容性"""
        log_info("\n🔄 测试向后兼容性")
        log_info("-" * 60)
        
        try:
            # 测试旧方法名仍然可用
            log_info("📝 测试 execute_ld_with_korean_support (旧方法名):")
            success, xml_content = self.ld.execute_ld_with_korean_support(
                self.emulator_id, 
                "uiautomator dump /sdcard/ui_old_method.xml"
            )
            
            if success and xml_content:
                log_info(f"   ✅ 旧方法名仍然可用")
                log_info(f"   📊 XML长度: {len(xml_content):,} 字符")
            else:
                log_error("   ❌ 旧方法名执行失败")
            
            # 测试旧的便捷方法
            log_info("\n📝 测试 get_ui_xml_with_korean_support (旧方法名):")
            success, xml_content2 = self.ld.get_ui_xml_with_korean_support(self.emulator_id)
            
            if success and xml_content2:
                log_info(f"   ✅ 旧便捷方法仍然可用")
                log_info(f"   📊 XML长度: {len(xml_content2):,} 字符")
            else:
                log_error("   ❌ 旧便捷方法执行失败")
                
        except Exception as e:
            log_error(f"❌ 向后兼容性测试失败: {e}")
    
    async def test_functionality_consistency(self):
        """测试功能一致性"""
        log_info("\n⚖️ 测试功能一致性")
        log_info("-" * 60)
        
        try:
            # 使用新方法获取XML
            success1, xml1 = self.ld.execute_ld_with_multilingual_support(
                self.emulator_id, 
                "uiautomator dump /sdcard/ui_consistency1.xml"
            )
            
            # 使用旧方法获取XML
            success2, xml2 = self.ld.execute_ld_with_korean_support(
                self.emulator_id, 
                "uiautomator dump /sdcard/ui_consistency2.xml"
            )
            
            if success1 and success2 and xml1 and xml2:
                # 比较结果
                korean_chars1 = len([c for c in xml1 if '\uac00' <= c <= '\ud7af'])
                korean_chars2 = len([c for c in xml2 if '\uac00' <= c <= '\ud7af'])
                
                chinese_chars1 = len([c for c in xml1 if '\u4e00' <= c <= '\u9fff'])
                chinese_chars2 = len([c for c in xml2 if '\u4e00' <= c <= '\u9fff'])
                
                log_info(f"📊 功能一致性对比:")
                log_info(f"   新方法 - 韩文字符: {korean_chars1}, 中文字符: {chinese_chars1}")
                log_info(f"   旧方法 - 韩文字符: {korean_chars2}, 中文字符: {chinese_chars2}")
                
                if korean_chars1 == korean_chars2 and chinese_chars1 == chinese_chars2:
                    log_info(f"   ✅ 功能完全一致")
                else:
                    log_warning(f"   ⚠️ 功能存在差异")
                    
            else:
                log_error("   ❌ 功能一致性测试失败")
                
        except Exception as e:
            log_error(f"❌ 功能一致性测试异常: {e}")
    
    async def test_method_documentation(self):
        """测试方法文档"""
        log_info("\n📚 测试方法文档")
        log_info("-" * 60)
        
        try:
            # 检查新方法的文档
            new_method = self.ld.execute_ld_with_multilingual_support
            new_doc = new_method.__doc__
            
            log_info("📝 新方法文档:")
            if new_doc:
                # 提取关键信息
                if "多语言" in new_doc:
                    log_info("   ✅ 包含多语言说明")
                if "韩文" in new_doc and "日文" in new_doc and "中文" in new_doc:
                    log_info("   ✅ 列出了支持的语言")
                if "Unicode" in new_doc:
                    log_info("   ✅ 提到了Unicode支持")
            else:
                log_warning("   ⚠️ 新方法缺少文档")
            
            # 检查旧方法的文档
            old_method = self.ld.execute_ld_with_korean_support
            old_doc = old_method.__doc__
            
            log_info("\n📝 旧方法文档:")
            if old_doc:
                if "向后兼容" in old_doc or "别名" in old_doc:
                    log_info("   ✅ 包含向后兼容说明")
                if "重命名" in old_doc:
                    log_info("   ✅ 提到了方法重命名")
                if "建议" in old_doc:
                    log_info("   ✅ 包含使用建议")
            else:
                log_warning("   ⚠️ 旧方法缺少文档")
                
        except Exception as e:
            log_error(f"❌ 方法文档测试异常: {e}")
    
    async def test_real_world_usage(self):
        """测试实际使用场景"""
        log_info("\n🌍 测试实际使用场景")
        log_info("-" * 60)
        
        try:
            # 场景1: Instagram多语言用户检测
            log_info("📱 场景1: Instagram多语言用户检测")
            success, xml_content = self.ld.execute_ld_with_multilingual_support(
                self.emulator_id, 
                "uiautomator dump /sdcard/ui_instagram.xml"
            )
            
            if success and xml_content:
                # 提取用户名和显示名
                import re
                usernames = re.findall(r'resource-id="com\.instagram\.android:id/follow_list_username"[^>]*text="([^"]*)"', xml_content)
                subtitles = re.findall(r'resource-id="com\.instagram\.android:id/follow_list_subtitle"[^>]*text="([^"]*)"', xml_content)
                
                log_info(f"   检测到 {len(usernames)} 个用户名")
                log_info(f"   检测到 {len(subtitles)} 个显示名")
                
                # 分析语言分布
                multilingual_users = []
                for subtitle in subtitles:
                    if any('\uac00' <= c <= '\ud7af' for c in subtitle):  # 韩文
                        multilingual_users.append(f"韩文: {subtitle}")
                    elif any('\u3040' <= c <= '\u309f' or '\u30a0' <= c <= '\u30ff' for c in subtitle):  # 日文
                        multilingual_users.append(f"日文: {subtitle}")
                    elif any('\u4e00' <= c <= '\u9fff' for c in subtitle):  # 中文
                        multilingual_users.append(f"中文: {subtitle}")
                
                if multilingual_users:
                    log_info(f"   ✅ 检测到多语言用户:")
                    for user in multilingual_users[:3]:  # 只显示前3个
                        log_info(f"      {user}")
                else:
                    log_info(f"   📝 当前界面主要为英文用户")
            
        except Exception as e:
            log_error(f"❌ 实际使用场景测试异常: {e}")

async def main():
    """主函数"""
    tester = MethodNamingTester()
    
    try:
        await tester.test_method_naming()
        await tester.test_real_world_usage()
        
        log_info("\n" + "=" * 80)
        log_info("🎉 方法命名测试完成！")
        log_info("=" * 80)
        log_info("💡 改进总结:")
        log_info("   1. ✅ 添加了更准确的方法名")
        log_info("   2. ✅ 保持了向后兼容性")
        log_info("   3. ✅ 功能完全一致")
        log_info("   4. ✅ 文档更加清晰")
        log_info("   5. ✅ 避免了命名误导")
        log_info("")
        log_info("🔧 推荐使用:")
        log_info("   - execute_ld_with_multilingual_support (新方法)")
        log_info("   - get_ui_xml_with_multilingual_support (新便捷方法)")
        log_info("")
        log_info("⚠️ 向后兼容:")
        log_info("   - execute_ld_with_korean_support (仍可用)")
        log_info("   - get_ui_xml_with_korean_support (仍可用)")
        
    except Exception as e:
        log_error(f"❌ 测试过程异常: {e}")
        import traceback
        log_error(f"详细错误信息:\n{traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(main())
