#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终Instagram跳转测试
========================================
功能描述: 测试修复后的Instagram用户资料页跳转功能
创建时间: 2025-07-25
作者: AI Assistant
"""

import asyncio
import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入核心模块
from core.instagram_follow_task import InstagramFollowTask
from core.logger_manager import log_info, log_error, log_warning


async def final_navigation_test(emulator_id: int = 2, user_id: str = "katoco0326"):
    """最终跳转测试"""
    try:
        print(f"🚀 最终Instagram跳转测试")
        print(f"模拟器ID: {emulator_id}")
        print(f"目标用户: {user_id}")
        print("=" * 60)
        
        # 创建Instagram关注任务实例
        instagram_task = InstagramFollowTask(emulator_id)
        
        # 验证环境
        if not instagram_task.is_ld_available():
            print("❌ 雷电API不可用")
            return False
            
        # 检查模拟器运行状态
        is_running, is_android, _ = instagram_task.ld.is_running(emulator_id)
        if not is_running or not is_android:
            print(f"❌ 模拟器{emulator_id}状态异常: 运行={is_running}, Android={is_android}")
            return False
        
        print(f"✅ 环境检查通过")
        
        # 记录初始状态
        initial_activity = instagram_task.ld.get_activity_name()
        print(f"📱 初始Activity: {initial_activity}")
        
        # 执行修复后的跳转功能
        print(f"\n🎯 执行修复后的跳转功能")
        print("修复内容:")
        print("  - 改进了ADB命令执行逻辑")
        print("  - 修复了shell命令格式问题")
        print("  - 增强了成功判断条件")
        print("  - 添加了详细的调试信息")
        
        start_time = time.time()
        
        # 执行跳转
        result = await instagram_task.navigate_to_profile(user_id)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出结果
        print(f"\n📊 测试结果:")
        print(f"跳转结果: {'✅ 成功' if result else '❌ 失败'}")
        print(f"总耗时: {duration:.2f}秒")
        
        # 记录最终状态
        final_activity = instagram_task.ld.get_activity_name()
        print(f"📱 最终Activity: {final_activity}")
        print(f"Activity变化: {'✅ 是' if initial_activity != final_activity else '❌ 否'}")
        
        # 详细验证
        if result:
            print(f"\n🔍 详细验证:")
            
            # 再次验证资料页
            profile_check = await instagram_task.check_current_profile(user_id)
            print(f"资料页验证: {'✅ 确认在资料页' if profile_check else '❌ 验证失败'}")
            
            # 检查关键元素
            elements_to_check = [
                ("com.instagram.android:id/action_bar_title", "标题栏"),
                ("com.instagram.android:id/row_profile_header_textview_followers_count", "粉丝数"),
                ("com.instagram.android:id/profile_header_follow_button", "关注按钮"),
                ("com.instagram.android:id/row_profile_header_textview_post_count", "帖子数"),
                ("com.instagram.android:id/row_profile_header_textview_following_count", "关注数")
            ]
            
            found_elements = []
            for element_id, element_name in elements_to_check:
                element = instagram_task.ld.find_node(resource_id=element_id)
                if element:
                    found_elements.append(element_name)
                    if element_id == "com.instagram.android:id/action_bar_title":
                        title_text = element.get('text', '')
                        print(f"  {element_name}: ✅ 找到 (文本: '{title_text}')")
                        if title_text == user_id:
                            print(f"  🎉 用户名匹配！")
                    else:
                        print(f"  {element_name}: ✅ 找到")
                else:
                    print(f"  {element_name}: ❌ 未找到")
            
            print(f"找到元素数量: {len(found_elements)}/{len(elements_to_check)}")
            
            # 最终判断
            title_element = instagram_task.ld.find_node(resource_id="com.instagram.android:id/action_bar_title")
            if title_element:
                title_text = title_element.get('text', '')
                if title_text == user_id:
                    print(f"\n🎉 跳转完全成功！")
                    print(f"✅ 已成功跳转到用户 {user_id} 的资料页")
                    return True
                else:
                    print(f"\n⚠️ 跳转部分成功")
                    print(f"在Instagram中，但不是目标用户资料页")
                    print(f"当前页面: {title_text}")
                    return False
            else:
                print(f"\n⚠️ 跳转状态不明")
                print(f"无法确定当前页面状态")
                return False
        
        else:
            print(f"\n❌ 跳转失败分析:")
            
            # 分析失败原因
            print(f"可能的原因:")
            
            # 检查Instagram应用状态
            current_activity = instagram_task.ld.get_activity_name()
            if "instagram" not in current_activity.lower():
                print(f"  1. Instagram应用未运行或不在前台")
                print(f"     当前Activity: {current_activity}")
            else:
                print(f"  1. Instagram应用正常运行")
            
            # 检查用户是否存在
            print(f"  2. 用户名 '{user_id}' 可能不存在或已更改")
            
            # 检查网络连接
            print(f"  3. 网络连接可能有问题")
            
            # 检查Instagram版本
            print(f"  4. Instagram应用版本可能不支持Deep Link")
            
            # 检查登录状态
            print(f"  5. Instagram应用可能未登录")
            
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


async def quick_manual_test(emulator_id: int = 2):
    """快速手动测试"""
    try:
        print(f"\n🔧 快速手动测试")
        print("=" * 60)
        
        instagram_task = InstagramFollowTask(emulator_id)
        
        # 手动测试步骤
        print(f"请按照以下步骤手动验证:")
        print(f"1. 确认Instagram应用已打开并登录")
        print(f"2. 手动在Instagram中搜索用户 'katoco0326'")
        print(f"3. 确认该用户是否存在")
        
        input("完成手动验证后按回车继续...")
        
        # 获取当前状态
        current_activity = instagram_task.ld.get_activity_name()
        print(f"当前Activity: {current_activity}")
        
        # 检查是否在Instagram中
        if "instagram" in current_activity.lower():
            print(f"✅ Instagram应用正常运行")
            
            # 尝试简单的跳转测试
            print(f"\n尝试简单的跳转测试...")
            
            # 直接使用execute_ld方法
            cmd = 'am start -a android.intent.action.VIEW -d "instagram://user?username=katoco0326"'
            success, output = instagram_task.ld.execute_ld(emulator_id, cmd, silence=False)
            
            print(f"命令执行: {'✅ 成功' if success else '❌ 失败'}")
            print(f"输出: {output}")
            
            await asyncio.sleep(3)
            
            # 检查结果
            after_activity = instagram_task.ld.get_activity_name()
            print(f"执行后Activity: {after_activity}")
            
            profile_check = await instagram_task.check_current_profile("katoco0326")
            print(f"资料页检查: {'✅ 在资料页' if profile_check else '❌ 不在资料页'}")
            
            return profile_check
        else:
            print(f"❌ Instagram应用未运行")
            return False
        
    except Exception as e:
        print(f"❌ 手动测试异常: {e}")
        return False


async def main():
    """主测试函数"""
    try:
        print("最终Instagram跳转测试工具")
        print("=" * 70)
        
        # 获取用户输入
        emulator_input = input("请输入模拟器ID (默认2): ").strip()
        emulator_id = int(emulator_input) if emulator_input else 2
        
        user_input = input("请输入要测试的用户名 (默认katoco0326): ").strip()
        user_id = user_input if user_input else "katoco0326"
        
        test_choice = input("选择测试类型 (1=完整测试, 2=快速手动测试, 默认1): ").strip()
        
        if test_choice == "2":
            success = await quick_manual_test(emulator_id)
        else:
            success = await final_navigation_test(emulator_id, user_id)
        
        print(f"\n{'='*70}")
        print(f"最终结果: {'✅ 测试通过' if success else '❌ 测试失败'}")
        print(f"{'='*70}")
        
        if success:
            print(f"\n🎉 恭喜！Instagram跳转功能已修复！")
            print(f"✅ 可以正常使用Instagram用户资料页跳转功能")
            print(f"💡 建议在实际关注流程中使用此功能")
        else:
            print(f"\n🔧 需要进一步调试:")
            print(f"1. 运行 test_direct_adb.py 进行更深入的调试")
            print(f"2. 运行 deep_debug_adb.py 进行全面的命令测试")
            print(f"3. 手动验证Instagram应用状态和用户存在性")
            print(f"4. 检查网络连接和应用版本")
        
    except Exception as e:
        print(f"❌ 主程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
