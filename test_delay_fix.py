#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
延迟时间修复测试
========================================
功能描述: 测试修复后的延迟时间是否正确显示和执行
创建时间: 2025-07-25
作者: AI Assistant
"""

import asyncio
import sys
import time
import random
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入核心模块
from core.instagram_follow_task import InstagramFollowTask
from core.logger_manager import log_info


async def test_delay_conversion():
    """测试延迟时间转换"""
    try:
        print("🧪 测试延迟时间转换")
        print("=" * 50)
        
        # 创建Instagram关注任务实例
        instagram_task = InstagramFollowTask(2)
        
        print(f"配置的切换延迟范围: {instagram_task.switch_delay_min} - {instagram_task.switch_delay_max} 毫秒")
        print(f"配置的关注延迟范围: {instagram_task.follow_delay_min} - {instagram_task.follow_delay_max} 毫秒")
        
        # 测试切换用户延迟
        print(f"\n🔄 测试切换用户延迟转换:")
        for i in range(3):
            delay_ms = random.randint(instagram_task.switch_delay_min, instagram_task.switch_delay_max)
            delay_seconds = delay_ms / 1000.0
            print(f"  测试 {i+1}: {delay_ms}毫秒 = {delay_seconds}秒")
            
            # 验证转换是否正确
            expected_seconds = delay_ms / 1000.0
            if abs(delay_seconds - expected_seconds) < 0.001:
                print(f"    ✅ 转换正确")
            else:
                print(f"    ❌ 转换错误")
        
        # 测试关注延迟
        print(f"\n❤️ 测试关注延迟转换:")
        for i in range(3):
            delay_ms = random.randint(instagram_task.follow_delay_min, instagram_task.follow_delay_max)
            delay_seconds = delay_ms / 1000.0
            print(f"  测试 {i+1}: {delay_ms}毫秒 = {delay_seconds}秒")
            
            # 验证转换是否正确
            expected_seconds = delay_ms / 1000.0
            if abs(delay_seconds - expected_seconds) < 0.001:
                print(f"    ✅ 转换正确")
            else:
                print(f"    ❌ 转换错误")
        
        # 测试实际延迟执行
        print(f"\n⏱️ 测试实际延迟执行:")
        test_delay_ms = 500  # 500毫秒
        test_delay_seconds = test_delay_ms / 1000.0
        
        print(f"测试延迟: {test_delay_ms}毫秒 ({test_delay_seconds}秒)")
        
        start_time = time.time()
        await asyncio.sleep(test_delay_seconds)
        end_time = time.time()
        
        actual_delay = end_time - start_time
        print(f"实际延迟: {actual_delay:.3f}秒")
        
        # 验证延迟是否准确（允许±50ms误差）
        if abs(actual_delay - test_delay_seconds) < 0.05:
            print(f"✅ 延迟执行准确")
        else:
            print(f"❌ 延迟执行不准确")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


async def test_log_output():
    """测试日志输出格式"""
    try:
        print(f"\n📝 测试日志输出格式:")
        print("=" * 50)
        
        # 模拟切换用户延迟日志
        delay_ms = 1447
        log_info(f"[模拟器2] ⏱️ 切换用户延迟: {delay_ms}毫秒", component="InstagramFollowTask")
        print(f"✅ 切换用户延迟日志: {delay_ms}毫秒")
        
        # 模拟关注延迟日志
        follow_delay_ms = 1200
        log_info(f"[模拟器2] 已关注数：5/50, {follow_delay_ms}毫秒后下一个, 耗时: 120.5 秒", component="InstagramFollowTask")
        print(f"✅ 关注延迟日志: {follow_delay_ms}毫秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志测试异常: {e}")
        return False


async def main():
    """主测试函数"""
    try:
        print("延迟时间修复测试工具")
        print("=" * 60)
        
        # 测试延迟转换
        conversion_success = await test_delay_conversion()
        
        # 测试日志输出
        log_success = await test_log_output()
        
        print(f"\n{'='*60}")
        print(f"测试结果:")
        print(f"  延迟转换: {'✅ 通过' if conversion_success else '❌ 失败'}")
        print(f"  日志输出: {'✅ 通过' if log_success else '❌ 失败'}")
        
        overall_success = conversion_success and log_success
        print(f"  总体结果: {'✅ 修复成功' if overall_success else '❌ 需要进一步修复'}")
        print(f"{'='*60}")
        
        if overall_success:
            print(f"\n🎉 延迟时间修复成功！")
            print(f"✅ 现在延迟时间将正确显示为毫秒")
            print(f"✅ 实际延迟执行时间也是正确的")
            print(f"💡 之前显示1447秒的问题已解决")
        else:
            print(f"\n🔧 需要进一步检查:")
            print(f"1. 检查配置文件中的延迟参数")
            print(f"2. 验证代码中的时间转换逻辑")
            print(f"3. 确认日志输出格式")
        
    except Exception as e:
        print(f"❌ 主程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
