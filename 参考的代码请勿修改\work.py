"""
雷电模拟器管理工具
1,雷电模拟器版本 V9.0.66 (其他版本自测)
2,框架兼容win10,win11 (其他版本自测)
3,本程序采用Python 3.9.0 开发
4,使用PyQt6作为GUI开发框架,兼容PyQt5及以上版本
5,多线程并发模拟器,支持多模拟器同时运行,稳定运行,代码含有详细注释,可供学习参考
6,带有WebSocket服务端以及客户端 (具体ws功能可自行研究)
7,支持多种模拟器配置,包括CPU,内存,分辨率等
8,详细的日志记录,可追踪模拟器运行过程
9,扩展性强,模拟器基本功能都已写好,可方便的添加新的任务逻辑.
温馨提示:
本商品是源码~~商品已经发出,不接受任何理由退货.
适合有python基础的朋友学习,也可作为模拟器的辅助工具.
小白慎拍~
"""
import faulthandler
import queue
import traceback
import ctypes
import json
import os
import random
import string
import sys
import threading
import time
from typing import Callable
import logging
import psutil
import win32gui
from PyQt6 import QtCore
from PyQt6.QtGui import QAction, QFont
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtWidgets import QApplication, \
	QMainWindow, QCheckBox, QTableWidgetItem, QMenu, QFileDialog, \
	QMessageBox, QInputDialog, QTableWidget
from my_form import Ui_MainWindow
from datetime import datetime as dt
from Emulator import EmulatorThread
from qasync import QApplication
from logger_config import setup_logger
from LeiDian import Dnconsole

logger = setup_logger("MainWindow",debug_mode=True)



class MainWindow(QMainWindow):
	# 定义信号来确保在主线程中更新日志
	log_signal = pyqtSignal(str)  # 用于传递日志消息
	task_stop_signal = pyqtSignal(bool)
	def __init__(self):
		super().__init__()
		self.end_date = None or dt.now().strftime("%Y-%m-%d")
		self.log_signal.connect(self.update_log)  # 将信号连接到更新日志的方法
		self.Display_log = True
		self.ld_base_path = None
		self.ld_share_path = None
		self.emulator_list = []
		self.delay_time = None
		self.hwnd = None
		self.db = None
		self.active_thread_count = 0  # 新增活动线程计数
		# 加载UI
		self.set_global_font_size(10)  # 设置字体大小为 12
		self.init_ui()
		# 初始化状态栏
		self.init_status_bar()
		self.config_data = None
		self.stop_flag = False  # 添加停止标志
		self.change_title = False  # 添加标题切换标志
		self.lock = threading.Lock()  # 创建一个线程锁
		self.user_lock = threading.Lock()  # 创建一个线程锁
		self.hwnd_list = []
		self.emulator_threads = {}
		self.selected_threads = {}
		self.checked_emulators = []
		self.log_windows = {}  # 存储日志窗口的字典
		self.simulators = 20  # 假设我们有20个模拟器
		self.running_threads = {}  # 当前正在运行的线程
		self.thread_pool = {}  # 用来存放所有的线程
		self.max_concurrent_threads = 0
		self.emulator_start_delay = 0
		self.task_interval = 0 # 任务延迟时间
		self.emulator_count = 0
		self.timeout_total = 900 # 总的任务执行时间
		self.timeout_v2ray = 300
		self.timeout_v2ray = 300 # v2ray总任务超时时间
		self.timeout_v2ray_node = 300 # v2ray节点检测超时时间
		self.timeout_ins_shouye = 300 # INS首页检测超时时间
		self.timeout_ins_zhuangtai = 300 # INS状态检测超时时间
		self.task_total_Num = 0 # 任务总数
		self.task_finished_Num = 0 # 任务完成数
		self.task_abnormal_Num = 0 # 任务异常数
		# 加载配置文件
		self.load_config()
		self.LD = None
		self.game = None
		# 连接信号槽
		self.connect_slots()
		# 初始化表格
		self.init_table_widget()
		# 初始化菜单栏
		self.init_menu()
		
		# 初始化模拟器路径
		# self.int_emulator()
		# 加载模拟器线程
		# self.init_emulator_threads()
	
		# 初始化 WebSocket 服务
		# self.ws_server = WsServer(host='*************', port=12345)
		# # 连接日志信号到槽函数
		# self.ws_server.log_signal.connect(self.log)
		# self.ws_server.message_received_signal.connect(self.message_received)
		# self.ws_server.start()  # 启动 WebSocket 服务
		
		# 初始化异常图片保存文件夹 abnormal_Img
		self.abnormal_Img = "abnormal_Img"
		if not os.path.exists(self.abnormal_Img):
			os.makedirs(self.abnormal_Img)
		

		self.window_arrange_stop_event = threading.Event()
		self.window_arrange_queue = queue.Queue()  # 创建窗口排列任务队列
		self.window_arrange_thread = threading.Thread(
			target=self.process_window_arrange_queue)
		self.window_arrange_thread.daemon = True
		self.window_arrange_thread_isrunning = False
		self.window_arrange_stop_flag = False  # 添加停止标志

		self.log("初始化完成")
		

	# 加载UI
	def init_ui(self):
		# 暂时禁用 connectSlotsByName
		original_connectSlotsByName = QtCore.QMetaObject.connectSlotsByName
		QtCore.QMetaObject.connectSlotsByName = lambda *args: None
		# 下面2行代码是为了防止窗口闪烁
		self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
		# self.setUpdatesEnabled(False)
		self.ui = Ui_MainWindow()
		self.ui.setupUi(self)
		self.setUpdatesEnabled(True)
		# self.setWindowTitle("雷电助手      到期日期：" + self.end_date)
		# 恢复 connectSlotsByName
		QtCore.QMetaObject.connectSlotsByName = original_connectSlotsByName

		# comboBox_Cpu 和 comboBox_Memory 初始化一下数值
		self.ui.comboBox_Cpu.addItems(["1", "2", "4"])  # 貌似最大支持4核
		self.ui.comboBox_Dpi.addItems(['240', '320', '480'])
		self.ui.comboBox_Memory.addItems(
			["256", "512", "1024", "2048", "4096", "6144", "8192"])
		# 并且设置一个默认值
		self.ui.comboBox_Cpu.setCurrentIndex(2)
		self.ui.comboBox_Memory.setCurrentIndex(4)
		
		# 为 lineEdit_App_Path 启用拖放功能
		self.ui.lineEdit_App_Path.setAcceptDrops(True)
		self.ui.lineEdit_App_Path.dragEnterEvent = self.dragEnterEvent
		self.ui.lineEdit_App_Path.dropEvent = self.dropEvent

	def init_table_widget(self):
		# 加载表格数据
		# 获取 tableWidget
		self.table_widget = self.ui.tableWidget
		
		self.table_widget.setSelectionBehavior(
			QTableWidget.SelectionBehavior.SelectRows)  # 关键设置1：选择整行
		self.table_widget.setSelectionMode(
			QTableWidget.SelectionMode.SingleSelection)  # 关键设置2：单选模式
		self.table_widget.setEditTriggers(
			QTableWidget.EditTrigger.NoEditTriggers)  # 可选：禁止编辑
		# 选中行显示背景色 蓝色
		# e6e6e6  蓝色对应颜色值是啥
		self.table_widget.setStyleSheet(
			"QTableWidget::item:selected {background-color: #ADD8E6;}")
		
		# 禁止排序
		# 加载表格数据
		self.load_data_from_json()
		self.load_emulator_list()
		# 给列表框指定列设置列宽
		self.table_widget.setColumnWidth(0, 40)
		self.table_widget.setColumnWidth(1, 40)
		self.table_widget.setColumnWidth(2, 200)
		self.table_widget.setColumnWidth(7, 150)
		self.table_widget.setColumnWidth(8, 350)


	# 初始化菜单栏
	def init_menu(self):
		"""
		初始化菜单栏
		"""
		self.set_menu_font_size(14)  # 设置字体大小为16px
		# 超级列表框关联右键菜单
		self.ui.tableWidget.cellClicked.connect(self.select_row)
		self.ui.tableWidget.setContextMenuPolicy(
			Qt.ContextMenuPolicy.CustomContextMenu)
		self.ui.tableWidget.customContextMenuRequested.connect(
			self.show_context_menu)

	# 控件连接信号槽设置
	def connect_slots(self):
		# 手动连接所有需要的信号和槽

		# 连接双击事件
		# 连接信号到槽
		self.ui.pushButton_emulator_path_Browse.clicked.connect(
			self.get_emulator_path)
		self.ui.pushButton_share_path_Browse.clicked.connect(
			self.get_share_path)
		self.ui.pushButton_modifyResolution.clicked.connect(
			self.modifyResolution)
		self.ui.pushButton_modifyCPU.clicked.connect(self.modifyCPU)
		self.ui.pushButton_random_device.clicked.connect(
			self.generate_random_phone_data)
		self.ui.pushButton_modifyPhone.clicked.connect(self.modifyPhone)
		self.ui.pushButton_add.clicked.connect(self.add_new_emulator_ui)
		# self.ui.pushButton_App_Install.clicked.connect(
		# 	self.installApk_emulators)
		self.ui.pushButton_copy_emulator.clicked.connect(
			self.copy_emulator_ui)
		self.ui.pushButton_sortWnd.clicked.connect(self.sortWnd)

		self.ui.pushButton_App_Install.clicked.connect(lambda: self.installApk_emulators(None))

		self.ui.pushButton_App_UnInstall.clicked.connect(lambda: self.uninstallApk_emulators(None))
		self.ui.pushButton_App_Browse.clicked.connect(
			self.get_App_path)
		self.ui.pushButton_App_launch.clicked.connect(lambda: self.startApp_emulators(None))
		self.ui.pushButton_App_kill.clicked.connect(lambda: self.killApp_emulators(None))
		self.ui.pushButton_root_open.clicked.connect(lambda: self.rootOpen_emulators(None))
		self.ui.pushButton_root_close.clicked.connect(lambda: self.rootClose_emulators(None))
		self.ui.pushButton_push_file.clicked.connect(lambda: self.push_file_emulators(None))
		self.ui.pushButton_pull_file.clicked.connect(lambda: self.pull_file_emulators(None))
		self.ui.pushButton_tiaozhuan.clicked.connect(self.tiaozhuan_emulators)
		
		self.ui.pushButton_start_task.clicked.connect(lambda: self.work(None,'check_app_task'))
		self.ui.pushButton_task_2.clicked.connect(lambda: self.work(None,'check_app_task_2'))
		self.ui.pushButton_task_follow.clicked.connect(lambda: self.work(None,''))
		self.ui.pushButton_stop_task.clicked.connect(self.stop_work)
		# self.ui.pushButton_4.clicked.connect(self.test_swip)
		self.ui.pushButton_export.clicked.connect(self.export_user_data)
		self.ui.pushButton_save_config.clicked.connect(self.save_config)
		self.ui.pushButton_save_config_2.clicked.connect(self.save_config)

		
		self.ui.pushButton_select_title_text.clicked.connect(self.select_title_text)
		self.ui.pushButton_update_title.clicked.connect(self.update_title_by_text)
		self.ui.pushButton_update_remark.clicked.connect(self.update_remark_by_text)

		
		self.ui.pushButton_select_target_user_path.clicked.connect(self.select_target_user_path)
		# self.ui.pushButton_tiaoshi1.clicked.connect(lambda: self.tiaoshi1())
		
	

	def set_global_font_size(self, size):
		font = QFont()
		font.setPointSize(size)
		QApplication.instance().setFont(font)

	# 初始化模拟器线程,根据模拟器的数量创建线程
	def init_emulator_threads(self, checked_emulators=None):
	
		self.emulator_list = self.get_all_list()
		for row in range(self.table_widget.rowCount()):
			checkbox = self.table_widget.cellWidget(row, 0)  # 假设勾选框在第0列
			if checkbox and checkbox.isChecked():
				# 根据行号从 self.emulator_list 中获取对应的模拟器信息
				if row < len(self.emulator_list):
					emulator = self.emulator_list[row]
					checked_emulators.append(emulator)
				else:
					logger.info(
						f"行号 {row} 超出了 self.emulator_list 的范围，跳过此勾选")
		
		for row, emulator in enumerate(checked_emulators):
			try:
				if emulator and isinstance(emulator, str):
					emulator_info = emulator.split(',')
					emulator_id = int(emulator_info[0])
					
					if emulator_id not in self.thread_pool:
						# 实例化模拟器线程
						emulator_thread = EmulatorThread(emulator_id, row, self)
						self.thread_pool[emulator_id] = emulator_thread
						
						# 连接任务完成的信号
						emulator_thread.task_finished.connect(
							self.on_task_finished)
						# 连接日志信号
						emulator_thread.task_log.connect(self.log)
						# 连接窗口状态更新信号
						emulator_thread.emulator_info_updated.connect(
							self.on_update_emulator_info)
						# 连接记录最后一个设备的信号
						emulator_thread.save_last_device.connect(
							self.on_save_last_device)
						emulator_thread.sortWdnd.connect(self.sortWnd)
						
						# app账号状态更新信号
						emulator_thread.user_status_updated.connect(
							self.on_update_user_status)
						
						try:
							# 处理获取模拟器状态时可能出现的 None 对象
							item = self.table_widget.item(row, 6)
							if item:
								emulator_status = item.text()
								if '已启动' in emulator_status:
									emulator_thread.emulator_is_running = True
						except Exception as e:
							logger.info(f'获取模拟器{emulator_id}状态失败: {e}')
				else:
					logger.info(f"跳过非字符串元素: {emulator}")
			except Exception as e:
				logger.info(f'初始化模拟器线程失败: {e}')
		
		logger.info(f'模拟器线程创建完毕')
		
		
		
	def int_emulator_config(self):
		# 获取模拟器安装路径
		self.ins_path = self.ui.lineEdit_ospath.text()
		if not self.ins_path or os.path.exists(self.ins_path) is False:
			#弹出警告框
			# 弹出警告窗口
			ctypes.windll.user32.MessageBoxW(None,
											 "模拟器安装路径不能为空！",
											 "错误",
											 0x10)  # 0x10: 警告图标
			return
		# Dnconsole程序路径
		self.console_path = self.ins_path + r'\ldconsole.exe '
		# if Dnconsole程序路径检测
		if os.path.exists(self.console_path) is False:
			logger.info(
				'Dnconsole程序路径不存在！\n请确认模拟器安装文件是否完整或模拟器版本是否不符！')
			return
		self.adb_path = self.ins_path + r'\adb.exe '
		# 本地图片保存路径
		self.images_path = self.ui.lineEdit_sharepath.text()
		if os.path.exists(self.images_path) is False:
			logger.info(
				'共享文件夹路径没有设置')
			return

	# 初始化状态栏
	def init_status_bar(self):
		# 需要创建一个QTimer对象，每隔一段时间就调用一次update_status_bar函数
		self.timer = QTimer(self)
		self.timer.timeout.connect(self.update_status_bar)
		self.timer.start(1000)  # 每隔1秒钟更新一次状态栏
	def update_status_bar(self):
		"""
		更新状态栏
		"""
		# self.getCpuInfo()  # 获取CPU信息
		# 显示模拟器数量
		# emulator_count = self.table_widget.rowCount()
		self.ui.statusbar.showMessage(
			f"模拟器数量: {self.emulator_count}     正在运行: {len(self.running_threads)}    最大并发: {self.max_concurrent_threads}   |  总任务数:   {self.task_total_Num}   已完成:    {self.task_finished_Num}")
	

	
	def closeEvent(self, event):
		logger.info('MainWindow关闭事件')
		# 在这里添加任何需要在关闭窗口时执行的异步清理代码
		self.save_table_widget_data()  # 保存表格数据
		self.save_config()  # 保存配置文件
		# 关闭所有模拟器线程
		for emulator_id, thread in self.emulator_threads.items():
			if thread.isRunning():
				thread.stop()

	# 等待所有模拟器线程退出
	def update_log(self, msg):
		"""
		在主线程中更新日志信息
		"""
		# 格式化时间
		formatted_time = dt.now().strftime('%Y-%m-%d %H:%M:%S')
		if self.Display_log:
			self.ui.plainTextEdit_log.appendPlainText(
				formatted_time + "  :  " + msg)

	def log(self, msg, row=None):
		""" 日志打印函数 """
		logger.info(msg)  # 这里是写入到日志文件或控制台
		if self.Display_log:
			# 通过信号传递日志到主线程更新 UI
			self.log_signal.emit(msg)
		# 更新到列表框
		if row is not None:
			self.更新状态(row, msg)  # 如果需要，依然执行其他操作

	def message_received(self, message):
		"""
		WebSocket 服务端接收到消息的处理函数
		"""
		logger.info(f'WebSocket 服务端接收到消息: {message}')

	def select_row(self, row, column):
		"""
		选中表格中的行
		"""
		# 延迟选择整行以减少闪烁
		QTimer.singleShot(0, lambda: self.ui.tableWidget.selectRow(row))

	# 设置菜单字体大小
	def set_menu_font_size(self, size):
		self.menu_style = f"""
			QMenu {{
				font-size: {size}px;
			}}
			QMenu::item {{
				padding: 5px 25px 5px 30px;
				border: 1px solid transparent;
			}}
			QMenu::item:selected {{
				background-color: #0078d7;
				color: white;
			}}
		"""
	# 拖动控件事件
	def dragEnterEvent(self, event):
		if event.mimeData().hasUrls():
			event.acceptProposedAction()
		else:
			event.ignore()
	
	def dropEvent(self, event):
		if event.mimeData().hasUrls():
			# 获取当前 lineEdit_App_Path 中的内容
			current_text = self.ui.lineEdit_App_Path.text()
			new_paths = []
			for url in event.mimeData().urls():
				file_path = url.toLocalFile()
				new_paths.append(file_path)
			new_text = "|".join(new_paths)
			if current_text:
				# 如果已有内容，追加新路径
				self.ui.lineEdit_App_Path.setText(f"{current_text}|{new_text}")
			else:
				# 如果没有内容，直接设置新路径
				self.ui.lineEdit_App_Path.setText(new_text)
			self.App_Path = self.ui.lineEdit_App_Path.text()
			self.log(f"App 路径已更新: {self.App_Path}")
			event.acceptProposedAction()
		else:
			event.ignore()
	def show_context_menu(self, pos):
		"""
		右键点击表格显示菜单
		"""
		menu = QMenu(self)
		menu.setStyleSheet(self.menu_style)

		# 添加菜单项，并设置名称以便识别
		action7 = QAction("全部勾选", self)
		action7.triggered.connect(self.handle_menu_action)
		menu.addAction(action7)

		action8 = QAction("取消勾选", self)
		action8.triggered.connect(self.handle_menu_action)
		menu.addAction(action8)
		
		action10 = QAction("跳转到指定序号", self)
		action10.triggered.connect(self.handle_menu_action)
		menu.addAction(action10)
		
		action9 = QAction("从本行开始勾选", self)
		action9.triggered.connect(self.handle_menu_action)
		menu.addAction(action9)
		
		# 创建子菜单 勾选
		submenu3 = QMenu("按状态勾选", self)
		submenu3.setStyleSheet(self.menu_style)  # 应用相同的样式
		menu.addMenu(submenu3)
		action13 = QAction("正常", self)
		action13.triggered.connect(self.handle_menu_action)
		submenu3.addAction(action13)
		action14 = QAction("异常", self)
		action14.triggered.connect(self.handle_menu_action)
		submenu3.addAction(action14)
		action15 = QAction("空状态", self)
		action15.triggered.connect(self.handle_menu_action)
		submenu3.addAction(action15)
		action16 = QAction("自定义", self)
		action16.triggered.connect(self.handle_menu_action)
		submenu3.addAction(action16)
		
		
		action15 = QAction("导出勾选", self)
		action15.triggered.connect(self.handle_menu_action)
		menu.addAction(action15)

		# 创建子菜单
		submenu = QMenu("模拟器操作", self)
		submenu.setStyleSheet(self.menu_style)  # 应用相同的样式

		# 添加子菜单项
		action_start = QAction("启动当前行模拟器", self)
		action_start.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_start)

		action_stop = QAction("停止当前行模拟器", self)
		action_stop.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_stop)

		action_restart = QAction("重启当前行模拟器", self)
		action_restart.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_restart)

		action_delete = QAction("删除当前行模拟器", self)
		action_delete.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_delete)

		action_fenge = QAction("----", self)
		submenu.addAction(action_fenge)

		action_start_selected = QAction("启动勾选模拟器", self)
		action_start_selected.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_start_selected)

		action_stop_selected = QAction("停止勾选模拟器", self)
		action_stop_selected.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_stop_selected)

		action_restart_selected = QAction("重启勾选模拟器", self)
		action_restart_selected.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_restart_selected)
		action_delete_selected = QAction("删除勾选模拟器", self)
		action_delete_selected.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_delete_selected)
		action_fenge2 = QAction("----", self)
		submenu.addAction(action_fenge2)
		action_modifyResolution = QAction("批量修改分辨率", self)
		action_modifyResolution.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_modifyResolution)
		# 批量修改标题
		action_modifyTitle = QAction("批量修改标题", self)
		action_modifyTitle.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_modifyTitle)
		# 批量修改桥接模式
		action_modifyBridge = QAction("批量桥接模式打开", self)
		action_modifyBridge.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_modifyBridge)
		# 批量桥接模式关闭
		action_modifyBridge_close = QAction("批量桥接模式关闭", self)
		action_modifyBridge_close.triggered.connect(self.handle_menu_action)
		submenu.addAction(action_modifyBridge_close)

		# 将子菜单添加到主菜单
		menu.addMenu(submenu)
		# 创建子菜单
		submenu2 = QMenu("任务操作", self)
		submenu2.setStyleSheet(self.menu_style)  # 应用相同的样式
		# 将子菜单添加到主菜单
		menu.addMenu(submenu2)
		
		action11 = QAction("启动检查APP", self)
		action11.triggered.connect(self.handle_menu_action)
		submenu2.addAction(action11)
		action12 = QAction("停止任务", self)
		action12.triggered.connect(self.handle_menu_action)
		submenu2.addAction(action12)

		action5 = QAction("新增一行", self)
		action5.triggered.connect(self.handle_menu_action)
		menu.addAction(action5)

		action1 = QAction("新建模拟器", self)
		action1.triggered.connect(self.handle_menu_action)
		menu.addAction(action1)
		action3 = QAction("重命名当前模拟器", self)
		action3.triggered.connect(self.handle_menu_action)
		menu.addAction(action3)

		action6 = QAction("刷新模拟器列表", self)
		action6.triggered.connect(self.handle_menu_action)
		menu.addAction(action6)
		

		# 显示菜单
		menu.exec(self.ui.tableWidget.mapToGlobal(pos))

	def handle_menu_action(self):
		"""
		处理右键菜单的点击事件
		"""
		# 获取发送信号的 QAction 对象
		action = self.sender()
		# 获取当前选中的行
		current_row = int(self.table_widget.currentRow())
		logger.info(f"当前选中的行: {current_row}")
		# 这里应该修改一下 获取当前选中行

		if action:
			logger.info(f"点击了 {action.text()}")
			if action.text() == "新建模拟器":
				self.add_new_emulator()
			elif action.text() == "新增一行":
				self.add_new_line()
			elif action.text() == "删除当前行模拟器":
				# 处理删除模拟器的操作
				self.delete_emulators(current_row)
			elif action.text() == "删除勾选模拟器":
				# 处理删除勾选模拟器的操作
				self.delete_emulators(None)

			elif action.text() == "启动当前行模拟器":
				self.start_emulators(current_row)
			elif action.text() == "停止当前行模拟器":
				self.stop_emulators(current_row)
			elif action.text() == "启动勾选模拟器":
				# 处理启动勾选的操作
				self.start_emulators()
			elif action.text() == "暂停勾选模拟器":
				# 处理暂停勾选的操作
				self.pause_emulators()
			elif action.text() == "停止勾选模拟器":
				# 处理停止勾选的操作
				self.stop_emulators()
			elif action.text() == "重启勾选模拟器":
				# 处理重启勾选的操作
				self.work()

			elif action.text() == "刷新模拟器列表":
				# 清空所有单元格内容
				self.table_widget.clearContents()
				# 设置行数为0，effectively 删除所有行
				self.table_widget.setRowCount(0)
				# 再刷新模拟器列表
				self.load_emulator_list()
			elif action.text() == "全部勾选":
				# 处理全部勾选的操作
				for row in range(self.table_widget.rowCount()):
					checkbox = self.table_widget.cellWidget(row, 0)
					if isinstance(checkbox, QCheckBox):
						checkbox.setChecked(True)
			elif action.text() == "取消勾选":
				# 处理全部取消勾选的操作
				for row in range(self.table_widget.rowCount()):
					checkbox = self.table_widget.cellWidget(row, 0)
					if isinstance(checkbox, QCheckBox):
						checkbox.setChecked(False)
			elif action.text() == "重命名当前模拟器":
				# 处理重命名模拟器的操作
				emulator_id = int(self.table_widget.item(current_row, 1).text())
				emulator_newname = self.table_widget.item(current_row, 2).text()
				self.rename_emulator(emulator_id, emulator_newname)
				self.log(f"模拟器 {emulator_id} 重命名为 {emulator_newname}")
			elif action.text() == "启动检查APP":
				self.work()
			elif action.text() == "跳转到指定序号":
				# 弹出输入框
				row, ok = QInputDialog.getInt(self, "输入序号", "请输入模拟器序号:", 1, 1, self.table_widget.rowCount())
				if ok:
					# 根据模拟器序号,找到行号,然后选中
					for i in range(self.table_widget.rowCount()):
						if int(self.table_widget.item(i, 1).text()) == row:
							self.table_widget.setCurrentCell(i, 0)
							self.table_widget.setFocus()
							break
			elif action.text() == "批量修改分辨率":
				self.modifyResolution_emulators(None)
			elif action.text() == "批量修改标题":
				self.modifyTitle_emulators(None)
			elif action.text() == "批量桥接模式打开":
				self.modifyBridge_emulators(None,True)
			elif action.text() == "批量桥接模式关闭":
				self.modifyBridge_emulators(None,False)
			
			elif action.text() == "从本行开始勾选":
				# 处理从本行开始勾选的操作
				# 增加一个选择,点击后提示输入选择多少行,比如选择100个,就从本行开始勾选100个,如果超过最大行数,就勾选到最后一行
				if current_row < self.table_widget.rowCount():
					while True:
						# 弹出输入框
						row_str, ok = QInputDialog.getText(self, "输入勾选行数",
														   "请输入勾选行数:")
						if not ok:
							break
						try:
							row = int(row_str)
							# 计算可以勾选的最大行数
							max_rows = self.table_widget.rowCount() - current_row
							# 确定实际要勾选的行数
							actual_rows = min(row, max_rows)
							# 根据模拟器序号,找到行号,然后选中
							for i in range(current_row,
										   current_row + actual_rows):
								if i < self.table_widget.rowCount():
									checkbox = self.table_widget.cellWidget(
										i, 0)
									if isinstance(checkbox, QCheckBox):
										checkbox.setChecked(True)
							break
						except ValueError:
							QMessageBox.warning(self, "输入错误",
												"请输入有效的整数。")
							
			
			
			
			elif action.text() == "停止任务":
				# 停止所有任务 这里停止的话,把Qt事件循环也停了,所以不能在这里写停止的代码
				self.stop_work()
				# 修改后的条件分支
			elif action.text() == "正常":
				self.toggle_rows_by_condition(
					lambda _, status: "正常" in status)
			
			elif action.text() == "异常":
				self.toggle_rows_by_condition(
					lambda _, status: "异常" in status)
			
			elif action.text() == "自定义":
				keyword, ok = QInputDialog.getText(self, "输入关键字",
												   "请输入关键字:")
				if ok:
					self.toggle_rows_by_condition(
						lambda _, status: keyword in status)
			
			elif action.text() == "空状态":
				self.toggle_rows_by_condition(
					lambda _, status: status.strip() == "")
			
			elif action.text() == "导出勾选":
				# 导出勾选的模拟器
				try:
					# 打开文件保存对话框
					file_path, _ = QFileDialog.getSaveFileName(self, "保存文件",
															   "",
															   "文本文件 (*.txt)")
					if file_path:
						with open(file_path, 'w', encoding='utf-8') as f:
							for row in range(self.table_widget.rowCount()):
								checkbox = self.table_widget.cellWidget(row, 0)
								if isinstance(checkbox,
											  QCheckBox) and checkbox.isChecked():
									# 获取第1列和第7列的内容
									col1_item = self.table_widget.item(row, 1)
									col7_item = self.table_widget.item(row, 7)
									if col1_item and col7_item:
										col1_text = col1_item.text()
										col7_text = col7_item.text()
										# 写入文件
										f.write(f"{col1_text}----{col7_text}\n")
						QMessageBox.information(self, "导出成功",
												"勾选的模拟器信息已导出。")
				except Exception as e:
					QMessageBox.warning(self, "导出失败", f"导出失败: {e}")
				

			else:
				logger.info(f"未知操作: {action.text()}")

	# 更新列表框指定列的内容
	def update_list_widget(self, row, column, content):
		# logger.info(f'更新列表框指定列的内容 {row} {column} {content}')
		try:
			item = QTableWidgetItem(content)
			self.table_widget.setItem(row, column, item)
		except Exception as e:
			logger.info(f'更新列表框指定列的内容失败: {e}')

	#  更新模拟器运行状态列
	def 更新状态(self, row, msg):
		self.update_list_widget(row, 8, msg)
	
	def get_int_value(self, line_edit, default=0):
		"""
		从 QLineEdit 控件中获取整数值，如果输入无效则返回默认值
		:param line_edit: QLineEdit 控件
		:param default: 默认值
		:return: 整数值
		"""
		try:
			text = line_edit.text()
			if text:
				return int(text)
		except ValueError:
			pass
		return default
	# 保存配置文件
	def save_config(self, config_data=None):
		# 如果指定了 config_data，则使用指定的配置数据，否则使用当前配置数据
		if config_data:
			config = config_data
		else:
			# 获取模拟器安装路径
			self.ins_path = self.ui.lineEdit_ospath.text()
			# 获取共享文件夹路径
			self.share_path = self.ui.lineEdit_share_path.text()
			# 新建模拟器延迟时间
			self.new_emulator_delay = self.get_int_value(
				self.ui.lineEdit_emulator_delay)
			self.emulator_start_delay = self.get_int_value(
				self.ui.lineEdit_start_delay)
			self.apk_path = self.ui.lineEdit_App_Path.text()
			self.apk_package_name = self.ui.lineEdit_App_pkname.text()
			self.task_out = self.get_int_value(self.ui.lineEdit_task_out)
			# self.task_out2 = int(self.ui.lineEdit_task_out2.text())
			self.max_concurrent_threads = self.get_int_value(
				self.ui.lineEdit_max_concurrent_threads)
			self.task_interval = self.get_int_value(self.ui.lineEdit_task_delay)
			self.host_file_path = self.ui.lineEdit_host_file_path.text()
			self.emulator_file_path = self.ui.lineEdit_emulator_file_path.text()
			self.wnd_width = self.get_int_value(self.ui.lineEdit_wnd_width)
			self.wnd_height = self.get_int_value(self.ui.lineEdit_wnd_height)
			self.wnd_column_spacing = self.get_int_value(
				self.ui.lineEdit_wnd_column_spacing)
			self.wnd_row_spacing = self.get_int_value(
				self.ui.lineEdit_wnd_row_spacing)
			# 保存选择框 checkBox_ws 是否勾选
			self.Display_ws = self.ui.checkBox_ws.isChecked()
			
			##########  定制区配置保存 #############################
			self.node_client = self.ui.lineEdit_node_client.text()
			self.uninstall_app = self.ui.checkBox_is_uninstall_app.isChecked()
			self.is_update_node = self.ui.checkBox_update_node.isChecked()
			self.last_device = self.get_int_value(self.ui.lineEdit_last_device)
			self.timeout_total = self.get_int_value(self.ui.lineEdit_total_timeout)
			self.timeout_v2ray = self.get_int_value(self.ui.lineEdit_v2ray_timeout)
			self.timeout_ins_shouye = self.get_int_value(self.ui.lineEdit_INS_timeout1)
			self.timeout_ins_zhuangtai = self.get_int_value(self.ui.lineEdit_INS_timeout2)
			self.change_title = self.ui.checkBox_change_title.isChecked()
			self.update_ins = self.ui.checkBox_update_ins.isChecked()
			self.default_font_size = self.ui.checkBox_default_font_size.isChecked()
			
		
			# 保存配置文件
			config = {
				'模拟器安装路径': self.ins_path,
				'模拟器共享文件夹路径': self.share_path,
				'新建模拟器延迟时间': self.new_emulator_delay,
				'模拟器启动延迟时间': self.emulator_start_delay,
				'任务启动间隔时间': self.task_interval,
				'最大并发线程数': self.max_concurrent_threads,
				'电脑文件路径': self.host_file_path,
				'模拟器文件路径': self.emulator_file_path,
				'apk路径': self.apk_path,
				'apk_包名': self.apk_package_name,
				'任务超时时间': self.task_out,
				'窗口宽度': self.wnd_width,
				'窗口高度': self.wnd_height,
				'窗口列间距': self.wnd_column_spacing,
				'窗口行间距': self.wnd_row_spacing,
				'是否自动开启ws服务': self.Display_ws,
				"订阅连接": self.node_client,
				"是否卸载重装APP": self.uninstall_app,
				"是否更新订阅节点": self.is_update_node,
				"最后一个运行设备": self.last_device,
				"总任务超时时间": self.timeout_total,
				"v2ray任务超时时间": self.timeout_v2ray,
				"ins首页超时时间": self.timeout_ins_shouye,
				"ins状态超时时间": self.timeout_ins_zhuangtai,
				"是否修改标题": self.change_title,
				"是否更新ins": self.update_ins,
				"是否设置默认字体大小": self.default_font_size,
				
				"直接用户关注数量": self.ui.lineEdit_follow_Num_1.text(),
				"用户粉丝关注数量": self.ui.lineEdit_follow_Num_2.text(),
				"私信任务数量": self.ui.lineEdit_send_msg_Num.text(),
				"最小粉丝数量": self.ui.lineEdit_min_followers.text(),
				"切换用户延迟1": self.ui.lineEdit_delay_1.text(),
				"切换用户延迟2": self.ui.lineEdit_delay_2.text(),
				"关注延迟1": self.ui.lineEdit_delay_3.text(),
				"关注延迟2": self.ui.lineEdit_delay_4.text(),
				"通用延迟1": self.ui.lineEdit_delay_5.text(),
				"通用延迟2": self.ui.lineEdit_delay_6.text(),
				"关注该数量后休息": self.ui.lineEdit_follow_xiuxi1.text(),
				"关注休息1": self.ui.lineEdit_follow_xiuxi2.text(),
				"关注休息2": self.ui.lineEdit_follow_xiuxi3.text(),
				"用户资料页加载超时": self.ui.lineEdit_page_load_timeout.text(),
				"粉丝列表页滑动超时": self.ui.lineEdit_scroll_timeout.text(),
				"选择地区_中国": self.ui.checkBox_regions_1.isChecked(),
				"选择地区_日本": self.ui.checkBox_regions_2.isChecked(),
				"选择地区_韩国": self.ui.checkBox_regions_3.isChecked(),
				"选择地区_泰国": self.ui.checkBox_regions_4.isChecked(),
				"选择地区_通用": self.ui.checkBox_regions_5.isChecked(),
				"目标用户文本路径": self.ui.lineEdit_target_user_path.text(),
				
				"私信话术": self.ui.plainTextEdit_send_messages.toPlainText(),
				
			}
		with open('config.json', 'w', encoding='utf-8') as f:
			json.dump(config, f, ensure_ascii=False, indent=4)
		logger.info('配置文件保存成功')
	
	def load_config(self):
		# 读取配置文件
		try:
			with open('config.json', 'r', encoding='utf-8') as f:
				config_data = json.load(f)
				
				logger.info(f'config_data: {config_data}')
			
				self.ld_base_path = config_data.get('模拟器安装路径', '')
				self.ld_share_path = config_data.get('模拟器共享文件夹路径', '')
				self.new_emulator_delay = config_data.get('新建模拟器延迟时间',
														  0)
				self.emulator_start_delay = config_data.get(
					'模拟器启动延迟时间', 10)
				self.apk_path = config_data.get('apk路径', '')
				self.apk_package_name = config_data.get('apk_包名', '')
				self.host_file_path = config_data.get('电脑文件路径', '')
				self.emulator_file_path = config_data.get('模拟器文件路径', '')
				self.task_out = config_data.get('任务超时时间', 60)
				# self.task_out2 = config_data.get['超时检测频率']
				self.task_interval = config_data.get('任务启动间隔时间', 0)
				self.max_concurrent_threads = config_data.get('最大并发线程数',
															  10)
				self.wnd_width = config_data.get('窗口宽度', 360)
				self.wnd_height = config_data.get('窗口高度', 540)
				self.wnd_column_spacing = config_data.get('窗口列间距', 100)
				self.wnd_row_spacing = config_data.get('窗口行间距', 100)

				self.Display_ws = config_data.get('是否自动开启ws服务',False)
				self.node_client = config_data.get('订阅连接', '')
				self.uninstall_app = config_data.get('是否卸载重装APP', False)
				self.is_update_node = config_data.get('是否更新订阅节点', False)
				self.last_device = config_data.get('最后一个运行设备', 0)
				self.timeout_total = config_data.get('总任务超时时间', 900)
				self.timeout_v2ray = config_data.get('v2ray任务超时时间', 300)
				self.timeout_ins_shouye = config_data.get('ins首页超时时间', 300)
				self.timeout_ins_zhuangtai = config_data.get('ins状态超时时间',300)
				self.change_title = config_data.get('是否修改标题', True)
				self.update_ins = config_data.get('是否更新ins', True)
				self.set_default_font_size = config_data.get('是否设置默认字体大小', True)

				
				self.ui.lineEdit_last_device.setText(str(self.last_device))
				self.ui.lineEdit_ospath.setText(self.ld_base_path)
				self.ui.lineEdit_share_path.setText(self.ld_share_path)
				self.ui.lineEdit_emulator_delay.setText(
					str(self.new_emulator_delay))
				self.ui.lineEdit_start_delay.setText(
					str(self.emulator_start_delay))
				self.ui.lineEdit_task_delay.setText(str(self.task_interval))
				self.ui.lineEdit_max_concurrent_threads.setText(
					str(self.max_concurrent_threads))
				self.ui.lineEdit_App_Path.setText(self.apk_path)
				self.ui.lineEdit_App_pkname.setText(self.apk_package_name)
				self.ui.lineEdit_task_out.setText(str(self.task_out))
				# self.ui.lineEdit_task_out2.setText(str(self.task_out2))
				self.ui.lineEdit_emulator_file_path.setText(
					self.emulator_file_path)
				self.ui.lineEdit_host_file_path.setText(self.host_file_path)
				self.ui.lineEdit_wnd_width.setText(str(self.wnd_width))
				self.ui.lineEdit_wnd_height.setText(str(self.wnd_height))
				self.ui.lineEdit_wnd_column_spacing.setText(
					str(self.wnd_column_spacing))
				self.ui.lineEdit_wnd_row_spacing.setText(
					str(self.wnd_row_spacing))
				self.ui.checkBox_ws.setChecked(self.Display_ws)
				self.ui.lineEdit_node_client.setText(self.node_client)
				self.ui.checkBox_is_uninstall_app.setChecked(self.uninstall_app)
				self.ui.checkBox_update_node.setChecked(self.is_update_node)
				
				self.ui.lineEdit_total_timeout.setText(str(self.timeout_total))
				self.ui.lineEdit_v2ray_timeout.setText(str(self.timeout_v2ray))
				self.ui.lineEdit_INS_timeout1.setText(str(self.timeout_ins_shouye))
				self.ui.lineEdit_INS_timeout2.setText(str(self.timeout_ins_zhuangtai))
				self.ui.checkBox_update_ins.setChecked(self.update_ins)
				self.ui.checkBox_change_title.setChecked(self.change_title)
				self.ui.checkBox_default_font_size.setChecked(self.set_default_font_size)
				
				
				self.ui.lineEdit_follow_Num_1.setText(config_data.get('直接用户关注数量', ''))
				self.ui.lineEdit_follow_Num_2.setText(config_data.get('用户粉丝关注数量', ''))
				self.ui.lineEdit_send_msg_Num.setText(config_data.get('私信任务数量', ''))
				self.ui.lineEdit_min_followers.setText(config_data.get('最小粉丝数量', ''))
				self.ui.lineEdit_delay_1.setText(config_data.get('切换用户延迟1', ''))
				self.ui.lineEdit_delay_2.setText(config_data.get('切换用户延迟2', ''))
				self.ui.lineEdit_delay_3.setText(config_data.get('关注延迟1', ''))
				self.ui.lineEdit_delay_4.setText(config_data.get('关注延迟2', ''))
				self.ui.lineEdit_delay_5.setText(config_data.get('通用延迟1', ''))
				self.ui.lineEdit_delay_6.setText(config_data.get('通用延迟2', ''))
				self.ui.lineEdit_follow_xiuxi1.setText(config_data.get('关注该数量后休息', ''))
				self.ui.lineEdit_follow_xiuxi2.setText(config_data.get('关注休息1', ''))
				self.ui.lineEdit_follow_xiuxi3.setText(config_data.get('关注休息2', ''))
				self.ui.lineEdit_page_load_timeout.setText(config_data.get('用户资料页加载超时', ''))
				self.ui.lineEdit_scroll_timeout.setText(config_data.get('粉丝列表页滑动超时', ''))
				self.ui.checkBox_regions_1.setChecked(config_data.get('选择地区_中国', False))
				self.ui.checkBox_regions_2.setChecked(config_data.get('选择地区_日本', False))
				self.ui.checkBox_regions_3.setChecked(config_data.get('选择地区_韩国', False))
				self.ui.checkBox_regions_4.setChecked(config_data.get('选择地区_泰国', False))
				self.ui.checkBox_regions_5.setChecked(config_data.get('选择地区_通用', False))
				self.ui.lineEdit_target_user_path.setText(config_data.get('目标用户文本路径', ''))
				
				self.ui.plainTextEdit_send_messages.setPlainText(config_data.get('私信话术', ''))
				
				logger.info('配置文件加载成功')
				self.config_data = config_data
		except FileNotFoundError:
			logger.info("配置文件不存在")
			return None
		except json.JSONDecodeError:
			logger.info("配置文件格式错误")

	def load_data_from_json(self):
		""" 从 zhanghao.json 文件中加载数据 """
		try:
			with open("zhanghao.json", "r", encoding="utf-8") as f:
				data = json.load(f)
		except FileNotFoundError:
			logger.info("zhanghao.json 文件不存在，请先创建该文件")

	# 保存列表框数据到 zhanghao.json 文件
	def save_table_widget_data(self):
		# 获取表头信息
		headers = [self.table_widget.horizontalHeaderItem(i).text() for i in
				   range(self.table_widget.columnCount())]
		# 准备存储数据的列表
		data = []

		# 遍历表格的每一行（从第二列开始，不保存第一列）
		for row in range(self.table_widget.rowCount()):
			row_data = {}
			for column in range(1, self.table_widget.columnCount()):  # 从第二列开始
				# 如果第2列 内容为空,则不保存
				if self.table_widget.item(row, 1).text() == "":
					continue
				item = self.table_widget.item(row, column)
				if item:
					row_data[headers[column]] = item.text()
				else:
					row_data[headers[column]] = ""  # 如果某个单元格为空，填充空字符串
			data.append(row_data)

		# 保存数据到 zhanghao.json
		with open("zhanghao.json", "w", encoding="utf-8") as f:
			json.dump(data, f, ensure_ascii=False, indent=4)

		logger.info("数据已保存到 zhanghao.json")

	# 加载表格数据
	def load_table_widget_data(self):
		# 从 zhanghao.json 读取配置
		try:
			with open('zhanghao.json', 'r', encoding='utf-8') as f:
				config_data = json.load(f)
		except FileNotFoundError:
			logger.info("zhanghao.json 文件不存在")
			return
		except json.JSONDecodeError:
			logger.info("zhanghao.json 文件格式错误")
			return
		
		# 更新表格数据
		for config_item in config_data:
			# 查找匹配的行
			for row in range(self.ui.tableWidget.rowCount()):
				index_item = self.ui.tableWidget.item(row, 1)  # 假设索引在第一列
				title_item = self.ui.tableWidget.item(row, 2)  # 假设标题在第二列
				
				if index_item and title_item:
					# logger.info(
					# 	f"Table index: {index_item.text()}, Table title: {title_item.text()}")
					# logger.info(
					# 	f"Config index: {config_item['索引']}, Config title: {config_item['标题']}")
					
					if index_item.text() == config_item[
						'索引'] and title_item.text() == config_item['标题']:
						# 更新账号信息
						account_column = 7  # 假设账号在第七列，索引为6
						self.ui.tableWidget.setItem(row, account_column,
													QTableWidgetItem(
														config_item['状态']))
						# logger.info(f'config_item: {config_item}')
						break  # 找到匹配行后退出内部循环

		logger.info("数据加载完成")

	# 加载模拟器列表
	def load_emulator_list(self):
		""" 加载模拟器列表 """
		self.emulator_path = self.ui.lineEdit_ospath.text()
		self.share_path = self.ui.lineEdit_share_path.text()

		if self.emulator_path == "":
			self.log("请先设置模拟器安装路径")
			return
		self.LD = Dnconsole(self.ld_base_path, self.ld_share_path)  # 实例化 LD 类
		if not self.emulator_list:
			self.emulator_list = self.LD.get_list()
		self.emulator_count = len(self.emulator_list)
		# logger.info(f'本地模拟器列表: {self.emulator_list}')
		# 格式 本地模拟器列表: ['0,雷电模拟器,0,0,0,-1,-1,720,1280,320']
		# 对应 索引、标题、顶层句柄、绑定句柄、是否进入android、进程PID、VBox进程PID
		for emulator_id in self.emulator_list:
			# 在表格中添加一行
			row_count = self.table_widget.rowCount()
			self.table_widget.insertRow(row_count)  # 在表格最后插入新的一行
			# 在新行的第一列添加一个复选框
			checkbox = QCheckBox()
			self.table_widget.setCellWidget(row_count, 0, checkbox)
			# 将emulator_list中的数据填充到表格中
			emulator_info = emulator_id.split(',')
			emulator_index = emulator_info[0]  # 索引
			emulator_name = emulator_info[1]  # 名称
			emulator_hwnd = emulator_info[2]  # 顶层句柄
			emulator_bind_hwnd = emulator_info[3]  # 绑定句柄
			emulator_is_android = emulator_info[4]  # 是否进入android
			emulator_pid = emulator_info[5]  # 进程PID
			emulator_vbox_pid = emulator_info[6]  # VBox进程PID
			self.update_list_widget(row_count, 1, emulator_index)  # 第二列模拟器索引
			self.update_list_widget(row_count, 2, emulator_name)  # 第三列模拟器名称
			self.update_list_widget(row_count, 3, emulator_hwnd)  # 第四列模拟器顶层句柄
			self.update_list_widget(row_count, 4,
									emulator_bind_hwnd)  # 第五列模拟器绑定窗口句柄
			self.update_list_widget(row_count, 5,
									emulator_vbox_pid)  # 第六列模拟器 VBox 进程 PID

			# logger.info(
			# 	f'emulator_is_android: {emulator_is_android},当前行: {row_count}')
			if emulator_is_android == '1':
				self.update_list_widget(row_count, 6,
										"已启动")  # 第七列模拟器是否进入android
			else:
				self.update_list_widget(row_count, 6, "未启动")

		#加载完模拟器列表之后,加载账号数据
		self.load_table_widget_data()
		# 初始化模拟器线程
		# self.init_emulator_threads(None)

	# 在表格新增一行
	def add_new_line(self):
		""" 双击单元格时添加新行 """
		# 在这里可以执行添加新行的逻辑
		# 例如：添加一行并填充内容
		logger.info('//////')
		row_count = self.table_widget.rowCount()
		self.table_widget.insertRow(row_count)  # 在表格最后插入新的一行

		# 在新行的第一列添加一个复选框
		checkbox = QCheckBox()
		self.table_widget.setCellWidget(row_count, 0, checkbox)
	# UI 按钮新建模拟器
	def add_new_emulator_ui(self):
		""" UI 按钮新建模拟器 """
		# 获取模拟器数量
		num = int(self.ui.lineEdit_emulator_num.text())
		# 获取模拟器启动延迟
		delay = int(self.ui.lineEdit_emulator_delay.text())
		# 调用新增模拟器函数 需要使用线程
		self.log(f'开始新增模拟器,数量: {num},启动延迟: {delay} 秒,稍后刷新模拟器列表')

		# 新增模拟器
		def add_new_emulator(Num: int = 1, name: str = '',
							 delay: int = 10):
			"""
			增加模拟器
			param:Num:新增模拟器数量
			param:name:模拟器名称
			param:delay:模拟器启动延迟 单位秒
			name可以为空,默认名称为'雷电模拟器',序号自增
			"""
			# 在这里可以执行增加模拟器的逻辑
			# 这里使用一个for循环来增加,适用于指定新增的数量
			result = self.LD.add(name, Num, delay)
		thread = threading.Thread(target=add_new_emulator,
								  args=(num, '', delay))
		thread.start()

	def copy_emulator_ui(self):
		"""
		UI 按钮复制模拟器
		"""
		def copy_emulator():
			"""
			复制模拟器的执行逻辑
			"""
			try:
				# 获取用户输入
				emulator_index_text = self.ui.lineEdit_emulator_index.text()
				emulator_num_text = self.ui.lineEdit_emulator_num2.text()
				emulator_delay_text = self.ui.lineEdit_emulator_delay.text()
				if not emulator_index_text or not emulator_num_text:
					QMessageBox.warning(self, '警告', '请输入模拟器索引和数量')
					return


				emulator_index = int(emulator_index_text)
				num = int(emulator_num_text) or 1  # 数量默认1个
				delay = int(emulator_delay_text) or 10  # 延迟时间默认10秒
				name = ''
				self.log(f'开始复制模拟器 {emulator_index} 数量 {num} 延迟 {delay} 秒,稍后刷新模拟器列表')
				for i in range(num):
					try:
						self.LD.copy(name, emulator_index)  # 假设这里是模拟器复制的逻辑
						self.log(f'已复制 {i + 1}/{num} 个模拟器')  # 更新日志

						if i+1 == num:
							self.log(f'模拟器复制完毕,数量: {num},请刷新模拟器列表')
							# # 在主线程中刷新模拟器列表
							# self.load_emulator_list()
							break
						else:
							time.sleep(delay)  # 延迟执行

					except Exception as e:
						self.log(f'复制模拟器失败: {e}')
						break

				# # 在主线程中刷新模拟器列表
				# self.load_emulator_list()

			except Exception as e:
				self.log(f'复制模拟器时出错: {e}')

		# 启动一个新线程来执行复制模拟器
		thread = threading.Thread(target=copy_emulator, daemon=True)
		thread.start()

	# 重命名模拟器
	def rename_emulator(self, index: int, newname: str):
		""" 重命名模拟器 """
		self.LD.rename(index, newname)

	# 删除模拟器
	def delete_emulator(self):
		""" 删除模拟器 """
		logger.info('删除当前模拟器/////')
		# 获取当前选中的行
		current_row = self.table_widget.currentRow()
		emulator_id = int(
			self.table_widget.item(current_row, 1).text())  # 假设第二列是模拟器ID
		# 在这里可以执行删除模拟器的逻辑
		result = self.LD.remove(emulator_id)
		# 从列表框删除改行
		self.table_widget.removeRow(current_row)

		self.log(f'模拟器 {emulator_id} 删除成功', current_row)

	# 修改模拟器分辨率
	def modifyResolution(self):
		""" 修改模拟器配置 """
		# logger.info('修改模拟器分辨率')
		# 获取当前选中的行
		
		current_row = self.table_widget.currentRow()
		index = int(
			self.table_widget.item(current_row, 1).text())  # 假设第二列是模拟器ID
		weight = self.ui.lineEdit_Resolution_weight.text()
		height = self.ui.lineEdit_Resolution_height.text()
		dpi = self.ui.comboBox_Dpi.currentText()
		# 在这里可以执行修改模拟器配置的逻辑
		result = self.LD.modifyResolution(index, weight, height, dpi)
		# result = LD.set_screen_size(0)
		# 这里应该用一个循环等待来接收result
		self.log(f'模拟器 {index} 分辨率修改成功',{'weight': weight, 'height': height, 'dpi': dpi} )

	# 修改模拟器CPU和内存
	def modifyCPU(self):
		""" 修改模拟器CPU """
		logger.info('修改模拟器CPU')
		# 获取当前选中的行
		current_row = self.table_widget.currentRow()
		index = int(
			self.table_widget.item(current_row, 1).text())  # 假设第二列是模拟器ID
		cpu = int(self.ui.comboBox_Cpu.currentText())
		memory = int(self.ui.comboBox_Memory.currentText())
		# 在这里可以执行修改模拟器CPU的逻辑
		result = self.LD.modifyCPU(index, cpu, memory)
		# LD.change_cpu_count(0,6)
		self.log(f'模拟器 {index} Cpu-Memory 修改成功', current_row)

	def modifyPhone(self):
		""" 修改模拟器厂商信息 """
		logger.info('修改模拟器厂商等信息')
		phone_Manufacturer = self.ui.lineEdit_phone_Manufacturer.text()
		phone_Model = self.ui.lineEdit_phone_Model.text()
		phone_Number = self.ui.lineEdit_phone_Number.text()
		phone_IMEI = self.ui.lineEdit_phone_IMEI.text()
		phone_IMSI = self.ui.lineEdit_phone_IMSI.text()
		phone_Sim = self.ui.lineEdit_phone_Sim.text()
		phone_Android_ID = self.ui.lineEdit_phone_Android_ID.text()
		phone_Mac = self.ui.lineEdit_phone_Mac.text()

		# 获取当前选中的行
		current_row = self.table_widget.currentRow()
		index = int(
			self.table_widget.item(current_row, 1).text())  # 假设第二列是模拟器ID
		# 在这里可以执行修改模拟器厂商信息的逻辑
		result = self.LD.modifyPhone(index, phone_Manufacturer, phone_Model,
									 phone_Number, phone_IMEI, phone_IMSI,
									 phone_Sim,
									 phone_Android_ID, phone_Mac)
		self.log(f'模拟器 {index} 机型配置 修改成功', current_row)

	def generate_random_phone_data(self):
		"""
		生成随机的手机数据并填充到编辑框
		"""
		# 手机品牌和对应型号的字典
		phone_models = {
			'Samsung': ['Galaxy_S21', 'Galaxy_S21+', 'Galaxy_S21_Ultra',
						'Galaxy_S20', 'Galaxy_S20+', 'Galaxy_S20_Ultra',
						'Galaxy_Note_20', 'Galaxy_Note_20_Ultra', 'Galaxy_A52',
						'Galaxy_A72'],
			'Huawei': ['P40_Pro', 'P40', 'P30_Pro', 'Mate_40_Pro',
					   'Mate_30_Pro',
					   'Nova_8'],
			'Xiaomi': ['Mi_11', 'Mi_11_Pro', 'Mi_11_Ultra', 'Redmi_Note_10_Pro',
					   'Poco_X3_NFC', 'Mi_10T_Pro'],
			'OPPO': ['Find_X3_Pro', 'Find_X3', 'Reno5_Pro', 'Reno5', 'A74',
					 'A54'],
			'Vivo': ['X60_Pro', 'X60', 'V21', 'Y72_5G', 'Y52_5G', 'Y20s'],
			'OnePlus': ['9_Pro', '9', '8T', 'Nord', 'Nord_N10_5G', 'Nord_N100'],
			'Realme': ['GT', '8_Pro', '8', '7_5G', '7_Pro', 'C25'],
			'Apple': ['iPhone_12_Pro_Max', 'iPhone_12_Pro', 'iPhone_12',
					  'iPhone_12_Mini', 'iPhone_SE_2nd_generation',
					  'iPhone_11'],
			'Sony': ['Xperia_1_III', 'Xperia_5_III', 'Xperia_10_III',
					 'Xperia_L4'],
			'LG': ['Velvet', 'Wing', 'K92_5G', 'K62', 'K52', 'K42'],
			'Motorola': ['Edge_S', 'Moto_G100', 'Moto_G30', 'Moto_G10',
						 'Moto_E7_Power'],
			'Nokia': ['8.3_5G', '5.4', '3.4', '2.4', '1.4'],
			'Google': ['Pixel_5', 'Pixel_4a_5G', 'Pixel_4a', 'Pixel_4_XL',
					   'Pixel_4'],
			'ASUS': ['ROG_Phone_5', 'ZenFone_7_Pro', 'ZenFone_7',
					 'ROG_Phone_3'],
			'Lenovo': ['Legion_Phone_Duel', 'K12_Pro', 'K12',
					   'Legion_Phone_Duel_2'],
			'ZTE': ['Axon_30_Ultra', 'Blade_V2020', 'Blade_A7s', 'Axon_20_5G'],
			'HTC': ['Desire_21_Pro_5G', 'U20_5G', 'Desire_20_Pro',
					'Wildfire_E2'],
			'Meizu': ['18_Pro', '18', '17_Pro', '17'],
			'BlackBerry': ['Key2', 'Key2_LE', 'Motion'],
			'Honor': ['V40_5G', '30_Pro+', '30_Pro', 'X10_5G']
		}

		# 随机选择厂商
		manufacturer = random.choice(list(phone_models.keys()))

		# 从选定厂商的型号列表中随机选择一个型号
		model = random.choice(phone_models[manufacturer])

		# 生成随机手机号码
		phone_number = '1' + random.choice(['3', '5', '7', '8', '9']) + ''.join(
			random.choices(string.digits, k=9))

		# 生成随机IMEI（15位数字）
		imei = ''.join(random.choices(string.digits, k=15))

		# 生成随机IMSI（15位数字）
		imsi = ''.join(random.choices(string.digits, k=15))

		# 生成随机SIM卡序列号（20位数字）
		sim_serial = ''.join(random.choices(string.digits, k=20))

		# 生成随机Android ID（16位十六进制）
		android_id = ''.join(random.choices(string.hexdigits, k=16)).lower()

		# 生成随机MAC地址
		mac = ':'.join(
			['{:02x}'.format(random.randint(0, 255)) for _ in range(6)])

		# 填充到编辑框
		self.ui.lineEdit_phone_Manufacturer.setText(manufacturer)
		self.ui.lineEdit_phone_Model.setText(model)
		self.ui.lineEdit_phone_Number.setText(phone_number)
		self.ui.lineEdit_phone_IMEI.setText(imei)
		self.ui.lineEdit_phone_IMSI.setText(imsi)
		self.ui.lineEdit_phone_Sim.setText(sim_serial)
		self.ui.lineEdit_phone_Android_ID.setText(android_id)
		self.ui.lineEdit_phone_Mac.setText(mac)

		logger.info("随机数据已生成并填充到编辑框")

	"""
	////////////////////////////
	
	"""
	def thread_start(self, current_row):
		""" 启动线程 """
	
		if current_row is not None:
			index = int(
				self.table_widget.item(current_row, 1).text())  # 假设第二列是模拟器ID
			# 创建并启动线程
			thread = threading.Thread(target=self.modifyBridge_emulators,
									  args=(current_row, index))
			thread.start()
		else:
			# 如果 current_row 为 None，处理所有勾选的模拟器
			selected_rows = []
			for row in range(self.table_widget.rowCount()):
				checkbox = self.table_widget.cellWidget(row, 0)
				if checkbox and checkbox.isChecked():
					selected_rows.append(row)
			# 如果没有勾选,提示一下
			if len(selected_rows) == 0:
				ctypes.windll.user32.MessageBoxW(None, "请勾选模拟器", "错误",
												 0x10)  # 0x10: 警告图标
				return
			for row in selected_rows:
				index = int(self.table_widget.item(row, 1).text())
				# 创建并启动线程
				thread = threading.Thread(target=self.modifyBridge_emulators,
										  args=(row, index))
				thread.start()
	
	
	# 批量执行任务======需要启动模拟器的任务用这个 start_tasks
	def start_tasks(self, current_row, task_name, **task_args):
		# selected_task = self.task_select.currentText()  # 获取选择的任务
		selected_rows = []
		# logger.info(f'current_row: {current_row}')
		if current_row is not None and current_row >= 0:
			selected_rows.append(current_row)
		else:
			for row in range(self.table_widget.rowCount()):
				checkbox = self.table_widget.cellWidget(row, 0)
				if checkbox and checkbox.isChecked():
					selected_rows.append(row)
		# 这里 如果选中的行为空,则不执行任何操作
		if len(selected_rows) == 0:
			# 弹出警告框
			QMessageBox.warning(self, '警告', '请选择模拟器')
			return
		
		self.emulator_list = self.get_all_list()
		checked_emulators = []
		for row in selected_rows:
			if row < len(self.emulator_list):
				emulator = self.emulator_list[row]
				checked_emulators.append((emulator, row))  # 同时保存模拟器信息和实际行号
			else:
				logger.info(f"行号 {row} 超出了 self.emulator_list 的范围，跳过此勾选")
		# logger.info(f"selected_rows: {selected_rows}")
		# logger.info(f"checked_emulators: {checked_emulators}")
		self.task_total_Num = len(selected_rows)
		
		for emulator, row in checked_emulators:
			try:
				if emulator and isinstance(emulator, str):
					emulator_info = emulator.split(',')
					emulator_id = int(emulator_info[0])
					
					# 添加当前循环的 row 和 emulator_id 到 task_args
					task_args['row'] = row
					task_args['emulator_id'] = emulator_id
					
					
					if emulator_id not in self.thread_pool:
						# 实例化模拟器线程
						emulator_thread = EmulatorThread(emulator_id, row, self)
						self.thread_pool[emulator_id] = emulator_thread
						
						# 连接任务完成的信号
						emulator_thread.task_finished.connect(
							self.on_task_finished)
						# 连接日志信号
						emulator_thread.task_log.connect(self.log)
						# 连接窗口状态更新信号
						emulator_thread.emulator_info_updated.connect(
							self.on_update_emulator_info)
						# 连接记录最后一个设备的信号
						emulator_thread.save_last_device.connect(
							self.on_save_last_device)
						emulator_thread.sortWdnd.connect(self.sortWnd)
						
						# app账号状态更新信号
						emulator_thread.user_status_updated.connect(
							self.on_update_user_status)
						# 连接日志窗口创建信号
						emulator_thread.log_window_created.connect(
							self.handle_log_window)
						try:
							# 处理获取模拟器状态时可能出现的 None 对象
							item = self.table_widget.item(row, 6)
							if item:
								emulator_status = item.text()
								if '已启动' in emulator_status:
									emulator_thread.emulator_is_running = True
						except Exception as e:
							logger.info(f'获取模拟器{emulator_id}状态失败: {e}')
				else:
					logger.info(f"跳过非字符串元素: {emulator}")
			except Exception as e:
				logger.info(f'初始化模拟器线程失败: {e}')
		
		logger.info(f'模拟器线程创建完毕')
		
		# 遍历所有选中的行，获取模拟器并将它们添加到 selected_threads 中
		for row in selected_rows:
			emulator_id = int(
				self.ui.tableWidget.item(row, 1).text())  # 获取模拟器 ID
			emulator_thread = self.thread_pool[emulator_id]
			
			# 确保线程未在运行中
			if emulator_thread.is_running:
				logger.info(f"模拟器 {emulator_id} 当前正在运行任务，跳过此模拟器")
				continue
			
			# 添加线程到 selected_threads 字典中
			self.selected_threads[emulator_id] = emulator_thread
		# 调用 run_next_task 并传递参数
		logger.info(f'开始执行 {task_name} 共 {len(selected_rows)} 个模拟器')
		# logger.info(f'self.selected_threads: {self.selected_threads}')
		self.run_next_task(task_name, **task_args)
	def run_next_task(self, task_name, **task_args):
		"""
		执行下一个任务
		:param task_name: 任务名称
		:param task_args: 任务参数
		"""
		if self.stop_flag:
			self.log(f'停止任务')
			return  # 如果停止标志为 True，直接返回，不再继续执行任务
		if len(self.selected_threads) == 0:
			# self.log(f'所有任务已完成11')
			return

		# 获取 delay_time 参数
		delay_time = self.task_interval
		
		try:
			# 如果并发数未满且有待执行的任务
			if len(self.running_threads) < self.max_concurrent_threads:
				# 这里再加一层判断,比如CPU占用率过高,则暂停执行
				# if 1 < 0:
				# 	self.log(f"当前CPU占用率过高,暂停执行,等待中...")
				# 	QTimer.singleShot(int(delay_time) * 1000,
				# 	                  lambda: self.run_next_task(task_name,
				# 	                                             **task_args))
				# 	return
				
				with self.lock:
					# 查找下一个未运行的线程，直接选择第一个未运行的线程
					current_thread = None
	
					# 遍历线程字典时使用临时变量名避免覆盖
					for temp_id, thread in self.selected_threads.items():
						if not thread.is_running:
							current_thread = (temp_id, thread)
							current_row = thread.row  # 直接从线程对象获取行号
							break
					if current_thread:
						if self.stop_flag:
							return  # 如果停止标志为 True，直接返回，不再启动新线程
						emulator_id, thread = current_thread
						self.running_threads[emulator_id] = thread
						# 设置任务和参数
						thread.set_task(task_name, **task_args)
						self.log(f"模拟器 {emulator_id} 开始执行 {task_name}",
								 current_row)
					
						
						if not thread.is_running:
							thread.start()
						
						# 启动线程后立即从 selected_threads 中移除该线程
						del self.selected_threads[emulator_id]
						
						# 取一下下一个任务的emulatord_id
						next_emulator_id = None
						next_row = None
						for emulator_id, thread in self.selected_threads.items():
							if not thread.is_running:
								next_emulator_id = emulator_id
								next_row = thread.row
								break
						if next_emulator_id:
							# 这里想修改一下 task_args 把row和index都传进去
							task_args['row'] = next_row
							task_args['index'] = next_emulator_id
							# 这里得先对线程数量控制进行判断.
							if len(self.running_threads) < self.max_concurrent_threads:
								self.log(
									f"任务已启动, {delay_time} 秒后执行下一个模拟器ID: {next_emulator_id}")
								self.update_list_widget(next_row, 8,
														f"{delay_time}秒后执行任务 {task_name}")
								# 这里监听一下停止按钮的状态,如果停止按钮被按下,则停止当前线程的执行

								QTimer.singleShot(int(delay_time) * 1000,
												  lambda: self.run_next_task(
													  task_name,
													  **task_args))
							else:
								self.log(
									f"当前并发数 {len(self.running_threads)} 已达到最大值 {self.max_concurrent_threads}, 等待中...")
								self.update_list_widget(next_row, 8,
														f"等待并发数")
								QTimer.singleShot(2 * 1000,
												  lambda: self.run_next_task(
													  task_name,
													  **task_args))
						else:
							# 所有线程都已经完成，等待中...
							self.log(f"所有任务已完成，开始等待新的任务")
							QTimer.singleShot(2 * 1000,
											  lambda: self.run_next_task(
												  task_name,
												  **task_args)
											  )
					
					else:
						# 没有找到可以启动的线程，说明所有线程都在执行，任务已经完成
						self.log("所有任务已完成，开始等待新的任务")
			
			else:
				# 线程数达到最大并发数，继续检查
				self.log(
					f"当前并发数 {len(self.running_threads)} 已达到最大值 {self.max_concurrent_threads}, 继续等待")
				QTimer.singleShot(2 * 1000,
								  lambda: self.run_next_task(task_name,
															 **task_args))
		except Exception as e:
			logger.info(f'获取 delay_time 参数失败: {e}')
			delay_time = 10  # 默认为 10 秒

	def start_emulators(self, current_row=None):
		"""
		启动选中的模拟器
		:param current_row: 当前行
		:return:
		# 如果current_row 存在并且!=-1 说明是指定current_row启动,就不需要遍历选择行了
		"""
		self.start_tasks(current_row, "start_emulator", delay_time=self.emulator_start_delay)

	def stop_emulators(self, current_row=None):
		""" 停止选中的模拟器 """
		"""停止选中的模拟器"""
		self.start_tasks(current_row, "stop_emulator", delay_time=self.task_interval)

	def pause_emulators(self, current_row=None):
		"""暂停选中的模拟器"""
		self.start_tasks(current_row, "pause_emulator", delay_time=self.task_interval)

	def resume_emulators(self, current_row=None):
		"""恢复选中的模拟器"""
		self.start_tasks(current_row, "resume_emulator", delay_time=self.task_interval)
	def restart_emulators(self, current_row=None):
		"""重启选中的模拟器"""
		self.start_tasks(current_row, "restart_emulator", delay_time=self.task_interval)
	
	def modifyResolution_emulators(self, current_row=None):
		"""修改模拟器分辨率"""
		
		def modify_resolution_in_thread(row,index, weight, height, dpi):
			try:
				self.LD.modifyResolution(index, weight, height, dpi)
				self.log(f'模拟器 {index} 分辨率修改成功', current_row)
				self.update_list_widget(row, 8, f"模拟器 {index} 分辨率修改成功")
			except Exception as e:
				self.log(f'模拟器 {index} 分辨率修改失败: {e}', current_row)
		
		if current_row is not None:
			index = int(
				self.table_widget.item(current_row, 1).text())  # 假设第二列是模拟器ID
			weight = self.ui.lineEdit_Resolution_weight.text()
			height = self.ui.lineEdit_Resolution_height.text()
			dpi = self.ui.comboBox_Dpi.currentText()
			# 创建并启动线程
			thread = threading.Thread(target=modify_resolution_in_thread,
									  args=(index, weight, height, dpi))
			thread.start()
		else:
			# 如果 current_row 为 None，处理所有勾选的模拟器
			selected_rows = []
			for row in range(self.table_widget.rowCount()):
				checkbox = self.table_widget.cellWidget(row, 0)
				if checkbox and checkbox.isChecked():
					selected_rows.append(row)
			# 如果没有勾选,提示一下
			if len(selected_rows) == 0:
				ctypes.windll.user32.MessageBoxW(None, "请勾选模拟器", "错误",
												 0x10)  # 0x10: 警告图标
				return
			for row in selected_rows:
				index = int(self.table_widget.item(row, 1).text())
				weight = self.ui.lineEdit_Resolution_weight.text()
				height = self.ui.lineEdit_Resolution_height.text()
				dpi = self.ui.comboBox_Dpi.currentText()
				time.sleep(0.2)
				# 创建并启动线程
				thread = threading.Thread(target=modify_resolution_in_thread,
										  args=(row,index, weight, height, dpi))
				thread.start()
	
	# 批量修改标题
	def modifyTitle_emulators(self, current_row=None):
		"""修改模拟器标题"""
		def modify_title_in_thread(row, index, title):
			try:
				self.LD.rename(index, title)
				self.log(f'模拟器 {index} 标题修改成功 : {title}')
				self.update_list_widget(row, 2, title)
				self.update_list_widget(row, 8, f"模拟器 {index} 标题修改成功")
			except Exception as e:
				self.log(f'模拟器 {index} 标题修改失败: {e}', current_row)
		if current_row is not None:
			index = int(
				self.table_widget.item(current_row, 1).text())  # 假设第二列是模拟器ID
			old_title =self.table_widget.item(current_row, 2).text()
			状态 = self.table_widget.item(current_row, 7).text()
			if 状态 and 状态 != "":
				new_title = f"{old_title} {状态}"
			else:
				new_title = old_title
			self.LD.rename(index, new_title)
			self.log(f'模拟器 {index} 标题修改成功 : {new_title}')
			self.update_list_widget(current_row, 8, f"模拟器 {index} 标题修改成功")
		else:
			# 如果 current_row 为 None，处理所有勾选的模拟器
			selected_rows = []
			for row in range(self.table_widget.rowCount()):
				checkbox = self.table_widget.cellWidget(row, 0)
				if checkbox and checkbox.isChecked():
					selected_rows.append(row)
			# 如果没有勾选,提示一下
			if len(selected_rows) == 0:
				ctypes.windll.user32.MessageBoxW(None, "请勾选模拟器", "错误",
												 0x10)  # 0x10: 警告图标
				return
			for row in selected_rows:
				index = int(self.table_widget.item(row, 1).text())
				old_title = self.table_widget.item(row, 2).text()
				状态 = self.table_widget.item(row, 7).text()
				if 状态 and 状态 != "":
					new_title = f"{old_title}-{状态}"
				else:
					new_title = old_title
				# logger.info(f"修改模拟器 {index} 标题为 {new_title}")
				# 创建并启动线程
				thread = threading.Thread(target=modify_title_in_thread,
										  args=(row, index, new_title))
				thread.start()
				
				
			# 创建并启动线程
	
	# 批量修改桥接模式
	def modifyBridge_emulators(self, current_row=None,model=None):
		"""修改模拟器桥接模式"""
		if model is None:
			# 弹出警告框,必须选择model True or False
			reply = QMessageBox.question(self, '警告', '请选择桥接模式',
										 QMessageBox.Yes | QMessageBox.No,
										 QMessageBox.No)
			if reply == QMessageBox.No:
				return

		self.start_tasks(current_row, "修改桥接模式",
						 model=model,
						 delay_time=self.task_interval)
	
	# 安装app
	def installApk_emulators(self, current_row=None):
		""" 安装选中的模拟器 """
		filepath = self.ui.lineEdit_App_Path.text()
		package_name = self.ui.lineEdit_App_pkname.text()
		if filepath == '':
			ctypes.windll.user32.MessageBoxW(None, "apk路径不能为空", "错误",
											 0x10)  # 0x10: 警告图标
			return
		# 检验一下apk文件和包名数量是否一致
		package_names = filepath.split('|')
		filepaths = package_name.split('|')
		if len(package_names) != len(filepaths):
			ctypes.windll.user32.MessageBoxW(None, "apk数量和包名数量不一致", "错误",
											 0x10)  # 0x10: 警告图标
			return


		self.start_tasks(current_row, "apk_install",
						 delay_time=self.task_interval,
						 filepath=filepath,
						 package_name=package_name)

	# 卸载app
	def uninstallApk_emulators(self, current_row=None):
		""" 卸载选中的模拟器 """
		package_name = self.ui.lineEdit_App_pkname.text()
		if not package_name:
			ctypes.windll.user32.MessageBoxW(None, "包名不能为空", "错误",
											 0x10)  # 0x10: 警告图标
			return
		self.start_tasks(current_row, "apk_uninstall",
						 delay_time=self.task_interval,
						 package_name=package_name)

	# 启动app
	def startApp_emulators(self, current_row=None):
		""" 启动选中的模拟器 """
		package_name = self.ui.lineEdit_App_pkname.text()
		if not package_name:
			ctypes.windll.user32.MessageBoxW(None, "包名不能为空", "错误",
											 0x10)  # 0x10: 警告图标
			return
		self.start_tasks(current_row, "app_run",
						 delay_time=self.task_interval,
						 package_name=package_name)

	# 停止app
	def killApp_emulators(self, current_row=None):
		""" 杀死选中的模拟器 """
		package_name = self.ui.lineEdit_App_pkname.text()
		if not package_name:
			ctypes.windll.user32.MessageBoxW(None, "包名不能为空", "错误",
											 0x10)  # 0x10: 警告图标
			return
		self.start_tasks(current_row, "app_kill",
						 delay_time=self.task_interval,
						 package_name=package_name)

	# 开启旋转/锁定窗口/root权限
	def rootOpen_emulators(self, current_row=None):
		""" 开启选中的模拟器的旋转/锁定窗口/root权限 """
		self.start_tasks(current_row, "root_open",
						 delay_time=self.task_interval)

	def rootClose_emulators(self, current_row=None):
		""" 开启选中的模拟器的旋转/锁定窗口/root权限 """
		self.start_tasks(current_row, "root_close",
						 delay_time=self.task_interval)

	def delete_emulators(self, current_row=None):
		"""
		删除选中的模拟器
		:param current_row: 当前行
		:return:
		因为删除模拟器需要先停止模拟器,所以这里先停止模拟器,再删除模拟器
		"""
		# 需要弹出一个警告框确认是否删除
		reply = QMessageBox.warning(
			self,
			"警告",
			"确认删除选中的模拟器?",
			buttons=QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
			defaultButton=QMessageBox.StandardButton.No
		)

		if reply == QMessageBox.StandardButton.No:
			return
		# 先停止模拟器
		# 再删除模拟器\
		self.start_tasks(current_row, "delete_emulator",
						 delay_time=1)


	# 传送文件到模拟器
	def push_file_emulators(self, current_row=None):
		""" 传送文件到模拟器 """
		host_path = self.ui.lineEdit_host_file_path.text()
		emulator_path = self.ui.lineEdit_emulator_file_path.text()
		if not host_path:
			ctypes.windll.user32.MessageBoxW(None, "本地文件路径不能为空",
											 "错误",
											 0x10)  # 0x10: 警告图标
			return
		if not host_path:
			ctypes.windll.user32.MessageBoxW(None, "模拟器路径不能为空", "错误",
											 0x10)  # 0x10: 警告图标
			return
		self.start_tasks(current_row, "push_file",
						 delay_time=self.task_interval,
						 host_path=host_path,
						 emulator_path=emulator_path)

	def pull_file_emulators(self, current_row=None):
		""" 传送文件到模拟器 """
		host_path = self.ui.lineEdit_host_file_path.text()
		emulator_path = self.ui.lineEdit_emulator_file_path.text()
		if not host_path:
			ctypes.windll.user32.MessageBoxW(None, "本地文件路径不能为空",
											 "错误",
											 0x10)  # 0x10: 警告图标
			return
		if not host_path:
			ctypes.windll.user32.MessageBoxW(None, "模拟器路径不能为空", "错误",
											 0x10)  # 0x10: 警告图标
			return
		# current_row = None
		self.start_tasks(current_row, "pull_file",
						 delay_time=self.task_interval,
						 host_path=host_path,
						 emulator_path=emulator_path)

	# 浏览App 获取路径
	def get_App_path(self):
		""" 浏览App 获取路径 """
		file_path, _ = QFileDialog.getOpenFileName(self, '选择文件', '',
												   'Apk 文件 (*.apk)')
		if file_path:
			self.ui.lineEdit_App_Path.setText(file_path)
			self.App_Path = file_path
			self.log(f"App 路径已更新: {file_path}")
	# 取随机名
	def get_random_name(self):
		pass
	# 浏览模拟器安装路径
	def get_emulator_path(self):
		""" 浏览模拟器安装路径 """
		file_path = QFileDialog.getExistingDirectory(self, '选择文件夹', '')
		if file_path:
			self.ui.lineEdit_ospath.setText(file_path)
			self.emulator_path = file_path
			self.log(f"模拟器安装路径已更新: {file_path}")

	# 浏览共享文件夹路径
	def get_share_path(self):
		""" 浏览共享文件夹路径 """
		file_path = QFileDialog.getExistingDirectory(self, '选择文件夹', '')
		if file_path:
			self.ui.lineEdit_share_path.setText(file_path)
			self.share_path = file_path
			self.log(f"共享文件夹路径已更新: {file_path}")


	def update_status(self, status):
		""" 更新模拟器状态 """
		logger.info(status)  # 你可以根据需要在 UI 中显示状态

	def get_all_list(self):
		""" 获取所有模拟器列表 """
		all_list = []
		all_list = self.LD.get_list()
		# self.log(f'所有模拟器列表：{all_list}')
		return all_list

	def get_running_list(self):
		""" 获取正在运行的模拟器列表 """
		running_list = []
		running_list = self.LD.list_running()
		self.log(f'正在运行的模拟器列表：{running_list}')
	
	def on_update_emulator_info(self, emulator_id, row, info_list,
							 specific_columns=None):
		"""
		更新模拟器信息到UI
		:param emulator_id: 模拟器ID
		:param row: 行号
		:param info_list: 包含信息的列表
		:param specific_columns: 指定要更新的列（可选）。是一个列表，包含要更新的列索引
		"""
		# logger.info(f'row: {row}, emulator_id: {emulator_id}, info_list: {info_list}')
		updated_columns = []
		
		if specific_columns is None:
			# 如果 specific_columns 为 None，则更新所有列
			specific_columns = list(range(len(info_list)))
		
		try:
			for i, col in enumerate(specific_columns):
				if i < len(info_list) and info_list[i]:  # 确保 info_list 中有对应的数据
					self.update_list_widget(row, col, info_list[i])
					updated_columns.append(col)
		
		except Exception as e:
			logger.info(f'更新模拟器信息失败: {e}')


	# 回调函数
	async def task_callback(self, task_name, success, emulator_id):
		"""任务执行完成后的回调函数"""
		status = "成功" if success else "失败"
		logger.info(f"模拟器 {emulator_id} 的任务 {task_name} 执行{status}")

	# 在这里可以更新 UI 或执行其他操作

	# 调用测试
	def tiaoshi(self):
		# pass
		# 4582 KB/s (153418031 bytes in 32.691s)
		# result = self.LD.pull_file(0, "/sdcard/111.apk", "D:/222.apk")
		# # result = self.LD.push_file(0, "D:/iBiliPlayer-bili.apk", "/sdcard/111.apk")
		# logger.info(result)
		# self.execute_task_on_selected_emulators(0,
		#                                         "game_start")
		# game = Game(0, 0)
		# # 连接信号槽
		# game.signals.log.connect(self.log)

		while True:
			# 获取 CPU 使用时间
			cpu_times = psutil.cpu_times()
			idle_time = cpu_times.idle
			total_time = sum(cpu_times)  # 用户时间 + 系统时间 + 空闲时间 + 等等
			cpu_usage = 100 * (
						total_time - idle_time) / total_time  # 计算 CPU 占用率

			logger.info(f"当前 CPU 占用率: {cpu_usage:.2f}%")

			time.sleep(1)  # 每秒更新一次
	
	def change_window_size(self,hwnd, width, height):
		# 获取窗口的当前位置和大小
		left, top, right, bottom = win32gui.GetWindowRect(hwnd)

		# 计算新的窗口位置，保持窗口在屏幕上的相对位置不变
		new_left = left
		new_top = top
		new_right = left + width
		new_bottom = top + height

		# 移动并调整窗口大小
		win32gui.MoveWindow(hwnd, new_left, new_top, new_right - new_left,
							new_bottom - new_top, True)
	

	# 排列窗口
	def sortWnd(self):
		# 在这里启动窗口队列处理线程
		if not self.window_arrange_thread_isrunning:
			self.window_arrange_thread_isrunning = True
			self.window_arrange_thread.start()
			time.sleep(1)
		
		# 排列窗口
		with self.lock:
			logger.info("排列窗口")
			width = int(self.ui.lineEdit_wnd_width.text())
			height = int(self.ui.lineEdit_wnd_height.text())
			column_spacing = int(self.ui.lineEdit_wnd_column_spacing.text())
			row_spacing = int(self.ui.lineEdit_wnd_row_spacing.text())
			
			# 过滤掉无效的窗口句柄
			valid_hwnd_list = []
			for hwnd in self.hwnd_list:
				if win32gui.IsWindow(hwnd):
					valid_hwnd_list.append(hwnd)
			self.hwnd_list = valid_hwnd_list
			
			if not self.hwnd_list:
				return
			
			# 将任务添加到队列中
			self.window_arrange_queue.put(
				(self.hwnd_list, width, height, column_spacing, row_spacing))
			
	
	def process_window_arrange_queue(self):
		while not self.window_arrange_stop_event.is_set():
			try:
				hwnd_list, width, height, column_spacing, row_spacing = self.window_arrange_queue.get()
				self.arrange_windows(hwnd_list, width, height, column_spacing,
									 row_spacing)
				self.window_arrange_queue.task_done()
			except Exception as e:
				logger.info(f"处理窗口排列任务时出错: {e}")
	
	
	def arrange_windows(self, hwnd_list, width, height, column_spacing,
						row_spacing):
		# 获取屏幕分辨率
		user32 = ctypes.windll.user32
		screen_width = user32.GetSystemMetrics(0)
		screen_height = user32.GetSystemMetrics(1)
		
		# 计算每行可以放置的窗口数量
		windows_per_row = screen_width // (width + column_spacing)
		
		# 遍历窗口句柄列表，逐个排列窗口
		for i, hwnd in enumerate(hwnd_list):
			# 计算当前窗口的行号和列号
			row = i // windows_per_row
			column = i % windows_per_row
			
			# 计算当前窗口的左上角坐标
			left = column * (width + column_spacing)
			top = row * (height + row_spacing)
			
			# 检查窗口是否超出屏幕底部，如果超出则换行
			if top + height > screen_height:
				# 可以添加更多逻辑，例如缩小窗口或者提示用户调整参数
				logger.info("窗口超出屏幕底部，可能需要调整参数")
			
			try:
				# 移动并调整窗口大小
				self.change_window_size(hwnd, width, height)
				win32gui.MoveWindow(hwnd, left, top, width, height, True)
				# 并且窗口置顶
				win32gui.SetForegroundWindow(hwnd)
			except Exception as e:
				logger.info(f"移动窗口失败: {e}")
		
		self.log(f"窗口已排列完成")
	
	
	def game_start(self, current_row=None):
		current_row = None
	
	def on_task_finished(self, emulator_id, row, task_name, task_args):
		"""
		任务完成后的处理函数，用于更新 UI。
		:param emulator_id: 模拟器 ID
		:param row: 表格中对应的行号
		:param task_name: 完成的任务名称
		"""
		# 加锁，确保操作是线程安全的
		with self.lock:
			# logger.info(
			# 	f"模拟器 {emulator_id} 任务完成//: {task_name}/////,准备移除线程")
			self.log(f"模拟器 {emulator_id} 任务完成: {task_name}")
			# 剩余任务数-1
			self.task_finished_Num += 1
			
			# 确保线程已经完全完成后再从字典中移除
			if emulator_id in self.running_threads:
				del self.running_threads[emulator_id]  # 直接删除指定的线程
			
			# 自动导出账号状态信息  保存到当前目录,分为 正常和异常2个文件
			self.export_user_data()

			# 获取额外的参数
			# 从字典中获取额外参数
			result = task_args.get('result')
			extra_info = task_args.get('extra_info')
			
			if result is not None:
				self.log(f"任务结果: {result}")
			if extra_info is not None:
				self.log(f"额外信息: {extra_info}")
			
			if len(self.selected_threads) == 0:
				self.log(f'所有任务已完成')
				return
	# 记录最后一个设备
	def on_save_last_device(self, emulator_id,row_index):
		with self.lock:
			logger.info(f'保存最后一个设备: {emulator_id}')
			if emulator_id is None:
				return
			self.ui.lineEdit_last_device.setText(str(emulator_id))
			# 单独构造包含 last_device 的配置字典
			self.config_data["最后一个设备"] = emulator_id
			# # 保存配置文件
			self.save_config(self.config_data)
			self.save_table_widget_data()
			
			"""
			根据行号将指定行滚动到可见区域
			:param row_index: 要显示的行号
			"""
			if 0 <= row_index < self.table_widget.rowCount():
				# 获取指定行的第一个单元格
				item = self.table_widget.item(row_index, 0)
				if item:
					# 滚动到该行
					self.table_widget.scrollToItem(item)
					# 可以选择同时选中该行
					self.table_widget.selectRow(row_index)
					
	def on_update_user_status(self, emulator_id,row,user_status):
		"""
		更新用户状态
		:param emulator_id: 模拟器ID
		:param row: 行号
		:param user_status: 用户状态
		:return:
		"""
		self.update_list_widget(row, 7, user_status)
		# 这里同时修改一下模拟器标题
		# 先取一下原标题
		if self.change_title and "异常" in user_status:
			old_title = self.table_widget.item(row, 2).text()
			new_title = f"{old_title}+_{user_status}"
			with self.lock:
				self.LD.rename(emulator_id, new_title)
			self.update_list_widget(row, 2, new_title)
	
	# 跳转至指定序列号
	def tiaozhuan_emulators(self):
		# 获取输入的模拟器 ID
		last_device_text = self.ui.lineEdit_last_device.text()
		if last_device_text == "":
			return
		try:
			# 尝试将输入转换为整数
			emulator_id = int(last_device_text)
			logger.info(f'跳转至指定序列号: {emulator_id}')
		except ValueError:
			# 输入不是有效的整数，给出提示
			logger.info(f"输入的模拟器 ID '{last_device_text}' 不是有效的整数。")
			return
		
		found = False
		for i in range(self.table_widget.rowCount()):
			item = self.table_widget.item(i, 1)
			if item is not None:
				try:
					# 尝试将表格中的 ID 转换为整数
					table_emulator_id = int(item.text())
					if table_emulator_id == emulator_id:
						# 找到对应行，使用 QTimer 延迟选中该行
						QtCore.QTimer.singleShot(0,
												 lambda: self.select_row_with_delay(
													 i))
						found = True
						break
				except ValueError:
					# 表格中的 ID 不是有效的整数，跳过该行
					continue
		
		if not found:
			# 未找到对应行，给出提示
			logger.info(f"未找到序列号为 {emulator_id} 的模拟器。")
	
	def select_row_with_delay(self, row):
		self.table_widget.setCurrentCell(row, 0)
		self.table_widget.setFocus()
		self.table_widget.selectRow(row)

	
	def check_checkbox_status(self):
		"""
		检查设备列表的勾选状态，索引 0 如果没有勾选，警告提示并返回
		"""
		row_count = self.table_widget.rowCount()
		if row_count > 0:
			checkbox = self.table_widget.cellWidget(0, 0)
			if isinstance(checkbox, QCheckBox) and not checkbox.isChecked():
				QMessageBox.warning(self, '警告',
									'索引 0 的设备未勾选，请勾选后再操作。')
				return False
		return True
	
	def select_target_user_path(self,*args):
		"""
		选择目标用户路径
		:param args:
		:return:
		"""
		
		# 打开文件对话框，选择一个文件
		file_path, _ = QFileDialog.getOpenFileName(self, "选择文件", "",
		                                           "文本文件 (*.txt)")
		if file_path:
			# 填充到输入框
			self.ui.lineEdit_target_user_path.setText(file_path)

	def handle_log_window(self, emulator_id, log_win):
		"""处理日志窗口创建"""
		self.log_windows[emulator_id] = log_win
		
		# 延迟显示确保窗口初始化完成
		QTimer.singleShot(100, lambda: (
			log_win.show(),
			log_win.raise_(),
			log_win.activateWindow()
		))

	def append_log(self, message, row):
		"""统一日志处理方法"""
		if row in self.log_windows:
			self.log_windows[row].append_log(message)
			

		
		
		
#################定制功能$$$$$$$$$$###
	
		
		
		
		
	
	def work(self, current_row=None,task_name=None):
		""" 安装选中的模拟器 """
		def _load_tasks(TASK_FILE):
			if os.path.exists(TASK_FILE):
				with open(TASK_FILE, "r", encoding="utf-8") as f:
					return [line.strip() for line in f if line.strip()]
			return []
		# 强制停止所有可能运行的线程
		# self.stop_work()
		# # 等待线程池完全清空
		# while self.thread_pool:
		# 	time.sleep(1)  # 避免CPU占用过高
		# 	self.log("等待线程池清空...")
		# 重置这几个变量
		self.task_total_Num = 0  # 任务总数
		self.task_finished_Num = 0  # 任务完成数
		self.task_abnormal_Num = 0  # 任务异常数
		# 初始化任务停止标识
		if self.stop_flag:
			self.stop_flag = False
		# 重置选中线程和运行线程
		self.selected_threads = {}
		self.running_threads = {}
		# 重新初始化窗口排列相关状态和线程
		self.window_arrange_stop_event.clear()
		self.window_arrange_stop_flag = False
		if not self.window_arrange_thread_isrunning:
			self.window_arrange_thread = threading.Thread(
				target=self.process_window_arrange_queue)
			self.window_arrange_thread.daemon = True
			self.window_arrange_thread.start()
			self.window_arrange_thread_isrunning = True
		
		# 获取一下启动间隔,以及APP路径,包名,节点客户端,是否卸载APP,是否更新节点
		filepath = self.ui.lineEdit_App_Path.text()
		package_name = self.ui.lineEdit_App_pkname.text()
		node_client = self.ui.lineEdit_node_client.text()
		uninstall_app = self.ui.checkBox_is_uninstall_app.isChecked()
		update_ins = self.ui.checkBox_update_ins.isChecked()
		update_node = self.ui.checkBox_update_node.isChecked()
		set_default_font_size = self.ui.checkBox_default_font_size.isChecked()
		activity = ""
		
		if self.ui.lineEdit_task_delay.text() == "":
			self.ui.lineEdit_task_delay.setText("10")
		self.task_interval = int(self.ui.lineEdit_task_delay.text())
		if self.ui.lineEdit_max_concurrent_threads.text() == "":
			self.ui.lineEdit_max_concurrent_threads.setText("5")
		self.change_title = self.ui.checkBox_change_title.isChecked()
		self.max_concurrent_threads = int(self.ui.lineEdit_max_concurrent_threads.text())
		self.timeout_total = int(self.ui.lineEdit_total_timeout.text())  # 总的任务执行时间
		self.timeout_v2ray = int(self.ui.lineEdit_v2ray_timeout.text())  # v2ray总任务超时时间
		self.timeout_ins_shouye = int(self.ui.lineEdit_INS_timeout1.text())  # INS首页检测超时时间
		self.timeout_ins_zhuangtai = int(self.ui.lineEdit_INS_timeout2.text())  # INS状态检测超时时间
		self.msg_data = ""

		
		直接用户关注数量 = int(self.ui.lineEdit_follow_Num_1.text())
		用户粉丝关注数量 = int(self.ui.lineEdit_follow_Num_2.text())
		私信任务数量 = int(self.ui.lineEdit_send_msg_Num.text())
		最小粉丝数量 = int(self.ui.lineEdit_min_followers.text())
		切换用户延迟1 = int(self.ui.lineEdit_delay_1.text())
		切换用户延迟2 = int(self.ui.lineEdit_delay_2.text())
		关注延迟1 = int(self.ui.lineEdit_delay_3.text())
		关注延迟2 = int(self.ui.lineEdit_delay_4.text())
		通用延迟1 = int(self.ui.lineEdit_delay_5.text())
		通用延迟2 = int(self.ui.lineEdit_delay_6.text())
		关注该数量后休息 = int(self.ui.lineEdit_follow_xiuxi1.text())
		关注休息1 = int(self.ui.lineEdit_follow_xiuxi2.text())
		关注休息2 = int(self.ui.lineEdit_follow_xiuxi3.text())
		用户资料页加载超时 = int(self.ui.lineEdit_page_load_timeout.text())
		粉丝列表页滑动超时 = int(self.ui.lineEdit_scroll_timeout.text())
		私密用户跳过 = self.ui.checkBox_is_private.isChecked()
		蓝v用户跳过 = self.ui.checkBox_is_blue.isChecked()
		地区列表 = []
		if self.ui.checkBox_regions_1.isChecked():
			地区列表.append("中国")
		if self.ui.checkBox_regions_2.isChecked():
			地区列表.append("日本")
		if self.ui.checkBox_regions_3.isChecked():
			地区列表.append("韩国")
		if self.ui.checkBox_regions_4.isChecked():
			地区列表.append("泰国")
		if self.ui.checkBox_regions_5.isChecked():
			地区列表.append("通用")
		目标用户文本路径 = self.ui.lineEdit_target_user_path.text()
		self.user_path = 目标用户文本路径
		if package_name == "com.v2ray.ang":
			activity = "com.v2ray.ang.ui.MainActivity"
		elif package_name == "com.instagram.android":
			activity = "com.instagram.modal.ModalActivity"
		if not package_name:
			ctypes.windll.user32.MessageBoxW(None, "包名不能为空", "错误",
											 0x10)  # 0x10: 警告图标
			return
		current_task = ""
		if self.ui.radioButton_task_1.isChecked():
			current_task = "check_app_task"
		elif self.ui.radioButton_task_2.isChecked():
			current_task = "check_app_task_2"
		elif self.ui.radioButton_task_follow_1.isChecked():
			current_task = "直接关注用户"
		elif self.ui.radioButton_task_follow_2.isChecked():
			current_task = "关注用户粉丝"
		elif self.ui.radioButton_task_follow_3.isChecked():
			current_task = "私信"
			# 加载私信话术
			self.msg_data = self.ui.plainTextEdit_send_messages.toPlainText()
			print(f'msg_data :  {self.msg_data}')
			if not self.msg_data:
				ctypes.windll.user32.MessageBoxW(None, "私信话术不能为空", "错误",
												 0x10)  # 0x10: 警告图标
				return
			
		if not current_task:
			ctypes.windll.user32.MessageBoxW(None, "任务不能为空", "错误",
											 0x10)  # 0x10: 警告图标
			return
		task_name = current_task
	
		# 加载用户数据
		self.user_data = _load_tasks(self.user_path)
		
		# 程序目录检测 sent_users.txt 没有的话创建,用来保存已发送过的用户
		if not os.path.exists("sent_users.txt"):
			with open("sent_users.txt", "w", encoding="utf-8") as f:
				f.write("")
		
		# # 只初始化勾选的模拟器线程
		# self.init_emulator_threads(checked_emulators)
		
		self.start_tasks(current_row, task_name,
		                 current_task=current_task,
						 delay_time=self.emulator_start_delay,
						 filepath=filepath,
						 package_name=package_name,
						 activity=activity,
						 node_client=node_client,
						 uninstall_app=uninstall_app,
						 update_node=update_node,
						 update_ins=update_ins,
						 update_title=self.change_title,
						 timeout_total=self.timeout_total,
						 timeout_v2ray=self.timeout_v2ray,
						 timeout_v2ray_node=self.timeout_v2ray,
						 timeout_ins_shouye=self.timeout_ins_shouye,
						 timeout_ins_zhuangtai=self.timeout_ins_zhuangtai,
						 set_default_font_size=set_default_font_size,
						 直接用户关注数量=直接用户关注数量,
						 用户粉丝关注数量=用户粉丝关注数量,
						 私信任务数量=私信任务数量,
						 最小粉丝数量=最小粉丝数量,
						 切换用户延迟1=切换用户延迟1,
						 切换用户延迟2=切换用户延迟2,
						 关注延迟1=关注延迟1,
						 关注延迟2=关注延迟2,
		                 关注该数量后休息=关注该数量后休息,
						 关注休息1=关注休息1,
						 关注休息2=关注休息2,
						 用户资料页加载超时=用户资料页加载超时,
						 粉丝列表页滑动超时=粉丝列表页滑动超时,
						 私密用户跳过=私密用户跳过,
						 蓝v用户跳过=蓝v用户跳过,
						 目标用户文本路径=目标用户文本路径,
						 通用延迟1=通用延迟1,
						 通用延迟2=通用延迟2,
						 地区列表=地区列表,
		                 msg_data=self.msg_data
						 )
	
	def _load_tasks(self,TASK_FILE):
		if os.path.exists(TASK_FILE):
			with open(TASK_FILE, "r", encoding="utf-8") as f:
				return [line.strip() for line in f if line.strip()]
		return []
	def get_username(self):
		"""线程安全获取并删除用户名"""
		with self.user_lock:
			user_data = self._load_tasks(self.user_path)
			if not user_data:
				self.log("用户列表已耗尽")
				return None
			
			username = user_data.pop(0)
			self._save_user_data(user_data)  # 立即保存剩余数据
			return username
	
	def _save_user_data(self, user_data):
		"""保存剩余用户数据到文件（类方法）"""
		with open(self.user_path, "w", encoding="utf-8") as f:
			f.write("\n".join(user_data))
	
	def get_sent_users(self):
		"""线程安全获取已发送用户"""
		SENT_RECORD_FILE = "sent_users.txt"
		with self.user_lock:
			try:
				with open(SENT_RECORD_FILE, 'r') as f:
					return set(line.strip() for line in f if line.strip())
			except FileNotFoundError:
				return set()
	
	def record_sent_user(self, username):
		"""线程安全记录已发送用户"""
		SENT_RECORD_FILE = "sent_users.txt"
		with self.user_lock:
			with open(SENT_RECORD_FILE, 'a') as f:
				f.write(f"{username}\n")
			
	
	def stop_work(self):
		self.stop_flag = True
		self.task_stop_signal.emit(True)
		# 把模拟器线程清空
		self.active_thread_count = len(self.thread_pool)  # 记录活动线程数量
		if self.thread_pool:
			for thread in self.thread_pool.values():
				if isinstance(thread, EmulatorThread):
					thread.stop_task()
					thread.thread_stopped.connect(self.on_thread_stopped)

		# 停止窗口排列线程
		self.window_arrange_stop_event.set()  # 设置停止标志
		self.window_arrange_stop_flag = True  # 设置停止标志
		if self.window_arrange_thread.is_alive():
			try:
				# self.window_arrange_thread.join()  # 等待线程结束
				self.window_arrange_thread_isrunning = False
				logger.info("窗口排列线程已停止")
			except Exception as e:
				logger.info(f"停止窗口排列线程时出现异常: {e}")
	
	def on_thread_stopped(self):
		self.active_thread_count -= 1
		if self.active_thread_count == 0:
			self.thread_pool.clear()
			logger.info("线程池已清空")
	# 导出异常号码
	def export_user_data(self):
		""" 导出异常号码 """
		# 获取表格中的数据
		data = []
		for row in range(self.table_widget.rowCount()):
			row_data = []
			for col in range(self.table_widget.columnCount()):
				item = self.table_widget.item(row, col)
				if item:
					row_data.append(item.text())
			data.append(row_data)
	
		# 分离正常和异常数据
		normal_data = []
		error_data = []
		for row in data:
			if len(row) >= 7:  # 确保 row 有足够的元素
				index = row[0]
				status = row[6]
				formatted_data = f"{index}----{status}"
				# logger.info(f"导出数据: {formatted_data}")
				if status == "正常":
					normal_data.append(formatted_data)
				elif "异常" in status:
					error_data.append(formatted_data)
		
		# 保存正常数据到文件
		normal_filename = "正常.txt"
		with open(normal_filename, 'w', encoding='utf-8') as f:
			for row in normal_data:
				f.write(row + '\n')
		logger.info(f"导出正常号码成功，文件名: {normal_filename}")
	
		# 保存异常数据到文件
		with open('异常.txt', 'w', encoding='utf-8') as f:
			for row in error_data:
				f.write(row + '\n')
		logger.info(f"导出异常号码成功，文件名: 异常.txt")
	
	# 新增通用勾选函数
	def toggle_rows_by_condition(self, condition: Callable[[int, str], bool]):
		"""通用勾选函数
		condition: 判断函数，参数为行号和状态文本，返回布尔值表示是否需要勾选
		"""
		try:
			# 清除所有行的勾选状态
			for row in range(self.table_widget.rowCount()):
				checkbox = self.table_widget.cellWidget(row, 0)
				if isinstance(checkbox, QCheckBox):
					checkbox.setChecked(False)
		except Exception as e:
			QMessageBox.warning(self, "清除勾选失败", f"清除勾选失败: {e}")
			return
		
		try:
			# 根据条件勾选行
			for row in range(self.table_widget.rowCount()):
				item = self.table_widget.item(row, 7)
				if item is not None and condition(row, item.text()):
					checkbox = self.table_widget.cellWidget(row, 0)
					if isinstance(checkbox, QCheckBox):
						checkbox.setChecked(True)
		except Exception as e:
			QMessageBox.warning(self, "勾选失败", f"勾选失败: {e}")
	
	# 选择标题文本,把文本路径填充到输入框 self.ui.lineEdit_title_text
	def select_title_text(self):
		# 打开文件对话框，选择一个文件
		file_path, _ = QFileDialog.getOpenFileName(self, "选择文件", "",
												   "文本文件 (*.txt)")
		if file_path:
		
			# 填充到输入框
			self.ui.lineEdit_title_text.setText(file_path)
	# 根据文本修改模拟器标题
	def update_title_by_text(self):
		def 修改标题():
			try:
				text_path = self.ui.lineEdit_title_text.text()
				with open(text_path, 'r', encoding='utf-8') as file:
					title_dict = {line.split('----')[0]: line.split('----')[1]
								  for line in file.read().splitlines() if
								  '----' in line}
				
				# 遍历表格匹配索引
				for row in range(self.table_widget.rowCount()):
					# 获取模拟器真实索引（第1列）
					index_item = self.table_widget.item(row, 1)
					if not index_item:
						continue
					
					emulator_id = index_item.text()
					if emulator_id in title_dict:
						# 获取旧标题（第2列）和新状态
						# 获取新状态
						new_status = title_dict[emulator_id].strip()
						if not new_status:  # 如果状态为空则不修改
							continue
						
						# 生成基础标题（雷电模拟器-索引）
						base_title = f"雷电模拟器-{emulator_id}"
						
						# 生成新标题：旧标题 + 新状态
						new_title = f"{base_title}-{new_status}"
						
						# 修改模拟器标题
						self.LD.rename(int(emulator_id), new_title)
						
						# 更新表格（第2列）
						self.update_list_widget(row, 2, new_title)
						
						# # 同时更新状态列（第7列）
						# self.update_list_widget(row, 7, new_status)
						self.log(
							f"更新成功: 模拟器{emulator_id} 标题改为 {new_title}")
				
				self.log("标题批量修改完成")
				# QMessageBox.information(self, "完成", "标题修改任务已完成")
			
			except Exception as e:
				self.log(f"修改标题出错: {str(e)}")
				# QMessageBox.critical(self, "错误",
				#                      f"修改标题时发生错误: {str(e)}")

		# 先根据文本路径,读取文本内容, 文本格式为: 序号----状态,每行一个
		text_path = self.ui.lineEdit_title_text.text()
		if not text_path:
			QMessageBox.warning(self, "警告", "请先选择文本文件")
			return
		# 创建线程
		self.title_thread = threading.Thread(target=修改标题)
		self.title_thread.start()
	def update_remark_by_text(self):
		def 修改备注():
			"""修改备注(第8列)"""
			try:
				text_path = self.ui.lineEdit_title_text.text()
				with open(text_path, 'r', encoding='utf-8') as file:
					remark_dict = {
						line.split('----')[0]: line.split('----')[1].strip()
						for line in file.read().splitlines() if '----' in line}
				
				for row in range(self.table_widget.rowCount()):
					index_item = self.table_widget.item(row, 1)
					if index_item and (
							emulator_id := index_item.text()) in remark_dict:
						# 直接更新备注列
						new_remark = remark_dict[emulator_id]
						self.update_list_widget(row, 7, new_remark)
						self.log(f"备注更新成功: {emulator_id} -> {new_remark}")
				
				self.log("备注批量修改完成")
			except Exception as e:
				self.log(f"修改备注出错: {str(e)}")
		
		# 先根据文本路径,读取文本内容, 文本格式为: 序号----状态,每行一个
		text_path = self.ui.lineEdit_title_text.text()
		if not text_path:
			QMessageBox.warning(self, "警告", "请先选择文本文件")
			return
		# 创建线程
		self.title_thread = threading.Thread(target=修改备注)
		self.title_thread.start()

		
		##  "d:/zhanghu.png"
	

if __name__ == "__main__":
	try:
		app = QApplication(sys.argv)
		# 必须在QApplication实例化之前调用。
		# qdarktheme.enable_hi_dpi()
		# # 应用完整的暗色主题到你的Qt应用上。
		# qdarktheme.setup_theme(
		# 	"auto")  # 自动适配深色主题 qdarktheme.setup_theme("light")  'auto'
	
		window = MainWindow()
		window.show()
		sys.exit(app.exec())
	except Exception as e:
		logging.critical(f"启动失败: {str(e)}", exc_info=True)
# _*_coding: utf_8*_
