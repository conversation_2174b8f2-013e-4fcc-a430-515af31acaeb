#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 UI样式管理器 - 统一样式定义
========================================
功能描述: 统一管理UI组件样式，避免重复定义
主要方法: StyleManager
调用关系: 被UI组件调用
注意事项: 消除重复样式代码，提高维护性
========================================
"""

from PyQt6.QtWidgets import QPushButton
from .styled_widgets import StyledButton


# ========================================
# 🎯 样式管理器
# ========================================

class StyleManager:
    """UI样式管理器 - 统一样式定义，消除重复代码"""
    
    # 按钮样式定义
    BUTTON_STYLES = {
        'primary': {
            'background_color': '#4CAF50',
            'hover_color': '#388E3C',
            'pressed_color': '#2E7D32',
            'text_color': 'white'
        },
        'secondary': {
            'background_color': '#2196F3',
            'hover_color': '#1976D2',
            'pressed_color': '#0D47A1',
            'text_color': 'white'
        },
        'warning': {
            'background_color': '#FF9800',
            'hover_color': '#F57C00',
            'pressed_color': '#E65100',
            'text_color': 'white'
        },
        'danger': {
            'background_color': '#f44336',
            'hover_color': '#d32f2f',
            'pressed_color': '#b71c1c',
            'text_color': 'white'
        }
    }
    
    # 通用样式模板
    BUTTON_TEMPLATE = """
        QPushButton {{
            background-color: {background_color};
            color: {text_color};
            border: none;
            border-radius: 4px;
            padding: {padding};
            font-size: {font_size};
            font-weight: bold;
            min-height: {min_height};
        }}
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        QPushButton:pressed {{
            background-color: {pressed_color};
        }}
        QPushButton:disabled {{
            background-color: #cccccc;
            color: #999999;
        }}
    """
    
    # 标签样式
    LABEL_STYLES = {
        'form_label': "font-size: 13px; color: #333; font-weight: 500;",
        'title_label': "font-size: 16px; font-weight: bold; color: #333;",
        'subtitle_label': "font-size: 14px; font-weight: bold; color: #333;"
    }
    
    # 分组框样式
    GROUPBOX_STYLES = {
        'primary': """
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #f3f9ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #f3f9ff;
                color: #1976D2;
            }
        """,
        'success': """
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e8f5e8;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #f8fff8;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #f8fff8;
                color: #4CAF50;
            }
        """,
        'warning': """
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #fff3e0;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #fffbf5;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #fffbf5;
                color: #FF9800;
            }
        """,
        'danger': """
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #ffebee;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #fef7f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #fef7f9;
                color: #e91e63;
            }
        """
    }
    
    @classmethod
    def create_button(cls, text: str, style_type: str = 'primary', 
                     padding: str = "6px 12px", font_size: str = "11px", 
                     min_height: str = "24px") -> StyledButton:
        """创建统一样式的按钮 - 消除重复代码"""
        if style_type not in cls.BUTTON_STYLES:
            raise ValueError(f"未知的按钮样式类型: {style_type}")
        
        button = StyledButton(text)
        style_config = cls.BUTTON_STYLES[style_type]
        
        # 使用模板生成样式
        style_sheet = cls.BUTTON_TEMPLATE.format(
            background_color=style_config['background_color'],
            hover_color=style_config['hover_color'],
            pressed_color=style_config['pressed_color'],
            text_color=style_config['text_color'],
            padding=padding,
            font_size=font_size,
            min_height=min_height
        )
        
        button.setStyleSheet(style_sheet)
        return button
    
    @classmethod
    def get_label_style(cls, style_type: str = 'form_label') -> str:
        """获取标签样式"""
        return cls.LABEL_STYLES.get(style_type, cls.LABEL_STYLES['form_label'])
    
    @classmethod
    def get_groupbox_style(cls, style_type: str = 'primary') -> str:
        """获取分组框样式"""
        return cls.GROUPBOX_STYLES.get(style_type, cls.GROUPBOX_STYLES['primary'])


# ========================================
# 🎯 样式工具函数
# ========================================

def create_primary_button(text: str, size: tuple = None) -> StyledButton:
    """创建主要按钮（绿色）"""
    button = StyleManager.create_button(text, 'primary')
    if size:
        button.setFixedSize(*size)
    return button

def create_secondary_button(text: str, size: tuple = None) -> StyledButton:
    """创建次要按钮（蓝色）"""
    button = StyleManager.create_button(text, 'secondary')
    if size:
        button.setFixedSize(*size)
    return button

def create_warning_button(text: str, size: tuple = None) -> StyledButton:
    """创建警告按钮（橙色）"""
    button = StyleManager.create_button(text, 'warning')
    if size:
        button.setFixedSize(*size)
    return button

def create_danger_button(text: str, size: tuple = None) -> StyledButton:
    """创建危险按钮（红色）"""
    button = StyleManager.create_button(text, 'danger')
    if size:
        button.setFixedSize(*size)
    return button


# ========================================
# 🎯 复选框样式常量
# ========================================

CHECKBOX_STYLE = """
    QCheckBox {
        font-size: 12px;
        color: #333;
        spacing: 5px;
    }
    QCheckBox::indicator {
        width: 16px;
        height: 16px;
        border: 2px solid #8a56ac;
        border-radius: 3px;
        background-color: white;
    }
    QCheckBox::indicator:checked {
        background-color: #8a56ac;
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggM0w0IDdMMiA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
    }
    QCheckBox::indicator:hover {
        border-color: #9c6bb8;
    }
"""
