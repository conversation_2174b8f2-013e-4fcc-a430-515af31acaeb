# Instagram 私信流程步骤记录

## 流程概述
基于雷电模拟器原生API实现Instagram粉丝私信功能，从模拟器启动、V2Ray节点连接到私信发送的完整自动化流程。

## 完整步骤流程

### 阶段一：模拟器启动和环境准备

#### 1. 启动雷电模拟器
- **API调用**: `unified_emulator_manager.start_emulator(emulator_id)`
- **验证启动**: 使用 `native_api.is_running(emulator_id)` 检查模拟器进程状态
- **等待桌面**: 等待Android桌面完全加载
- **实现状态**: ✅ 已完成（由统一模拟器管理器处理）

#### 2. 验证模拟器桌面稳定性
- **目的**: 确保模拟器桌面完全稳定
- **验证方法**: 使用窗口状态检测（Android启动状态 + 窗口可见性）
- **元素定义**: `com.android.launcher3:id/folder_icon_name` (备用方案)
- **API调用**: `native_api.is_desktop_stable(emulator_id, timeout=30)`
- **技术实现**: 使用 `win32gui.IsWindowVisible()` + 雷电原生API
- **检测速度**: 0.2秒（快速检测）
- **实现状态**: ✅ 已完成（完全不使用ADB）

#### 3. 检测应用安装状态 ✅ 已优化
- **V2Ray检测**: 通过图色识别检查 ‘V2.png'
- **Instagram检测**:通过图色识别检查 ‘ins.png'
- **API调用**: 
- **检测方法**: 
- **返回结果**: 
- **实现状态**: 

### 阶段二：V2Ray节点连接

#### 4. 启动V2Ray应用
- **包名**: `com.v2ray.ang`
- **启动API**: `native_api.run_app(emulator_id, package_name)`
- **验证启动**: `native_api.is_app_running(emulator_id, package_name)`
- **等待时间**: 3秒等待应用启动
- **验证策略**: 多重验证（进程检测 + 延迟重试 + 宽松验证）
- **实现状态**: ✅ 已完成（包含完整的启动验证机制）
#### 5. 检查节点列表状态，如果有节点则进行####8. 连接V2Ray节点#### 9. 测试节点延迟
- **查找节点**: `ld.find_node(resource_id="com.v2ray.ang:id/info_container")`
- **如果列表不为空**: 直接进行节点连接流程，8. 连接V2Ray节点#### 9. 测试节点延迟
- **如果列表为空**: 执行节点导入流程

#### 6. 导入V2Ray节点订阅
- **点击添加按钮**: `ld.wait_and_click(template="img/jiahao.png")`
- **复制订阅链接**: `pyperclip.copy(node_client)`
- **点击导入**: `ld.wait_and_click(template="img/daoru.png")`
- **更新订阅**: 点击右上角三点 → 更新订阅

#### 7. 随机选择节点
- **随机滑动**: `ld.random_swipe(swipe_actions, duration)`
- **查找节点**: `ld.find_nodes(resource_id="com.v2ray.ang:id/tv_name")`
- **随机点击**: `random.choice(nodes)` → `ld.click_node(selected)`

#### 8. 连接V2Ray节点
- **检查连接状态**: `ld.find_node(resource_id="com.v2ray.ang:id/tv_test_state")`
- **点击连接按钮**: `ld.click_node(fab_button)` (如果未连接)
- **等待连接**: 检查状态变为"已连接"

#### 9. 测试节点延迟
- **点击测试**: `ld.click_node(test_state_node)`
- **验证结果**: 等待状态显示"成功"
- **重试机制**: 最多5次ping测试，失败则更换节点

### 阶段三：Instagram应用启动和私信任务

#### 10. 启动Instagram应用
- **包名**: `com.instagram.android`
- **启动API**: `ld.runApp(emulator_id, "com.instagram.android")`
- **验证启动**: 检查应用界面加载完成

#### 11. 导航到个人主页
- **查找个人主页标识**: `ld.find_node(resource_id="com.instagram.android:id/profile_tab")`
- **点击进入**: `ld.click_node(profile_tab)`
- **等待加载**: 等待个人主页完全显示

#### 12. 获取粉丝数量信息
- **查找粉丝数标识**: `ld.find_node(resource_id="com.instagram.android:id/row_profile_header_textview_followers_count")`
- **解析数量**: 处理K、M等单位转换
- **验证有效性**: 确保有足够粉丝进行私信

#### 13. 打开粉丝列表
- **点击粉丝数**: `ld.click_node(followers_count_node)`
- **等待列表加载**: 确认粉丝列表界面显示
- **验证列表**: 检查是否有可见粉丝项目

### 阶段四：批量私信执行循环

#### 14. 初始化私信任务
- **加载去重记录**: 读取任务管理，粉丝私信，记录文件路径，中的用户设置的路径。。如果没有设置自动读取 `sent_users.txt` 文件
- **初始化计数器**: `sent_count = 0`
- **设置任务参数**: 目标数量等

#### 15. 主循环：批量私信发送
```python
while sent_count < 私信任务数量 and not stop_flag:
    # 15.1 获取当前屏幕可见粉丝
    followers = ld.find_nodes(resource_id="com.instagram.android:id/follow_list_container")

    # 15.2 遍历每个粉丝
    for follower in followers:
        # 15.3 提取粉丝信息
        username_node = ld.find_children(follower, resource_id="com.instagram.android:id/follow_list_username")
        username = username_node[0].get('text', '')

        # 15.4 检查去重（避免重复发送）
        if username in sent_users:
            continue

        # 15.5 点击粉丝头像进入主页
        position = ld.get_node_bounds(username_node[0])
        ld.click(position[0], position[1])

        # 15.5.1 是否有开启撤回功能，如果开启发送前撤回功能需要先撤回

        # 15.6 等待并点击"发消息"按钮
        self.em.wait_for(text="发消息", timeout=10, click=True)

        # 15.7 点击输入框
        self.em.wait_for(resource_id="com.instagram.android:id/row_thread_composer_edittext",
                        timeout=10, click=True)

        # 15.8 输入私信内容
        message = get_random_message()
        self.ld.input_text(emulator_id, message)

        # 15.9 点击发送按钮
        self.em.wait_for(resource_id="com.instagram.android:id/row_thread_composer_button_send",
                        click=True, timeout=5)

        # 15.10 记录已发送用户（去重）
        sent_users.add(username)
        with open("sent_users.txt", 'a') as f:
            f.write(f"{username}\n")

        # 15.11 返回粉丝列表
        self.ld.touch(emulator_id, 39, 78)  # 返回按钮坐标
        safe_sleep(1)
        self.ld.touch(emulator_id, 39, 78)  # 再次点击确保返回

        # 15.12 更新计数并检查完成状态
        sent_count += 1
        if sent_count >= 私信任务数量:
            break

    # 15.13 滚动加载更多粉丝
    list_node = self.em.find_node(resource_id="android:id/list")
    self.ld.scroll_list_enhanced(list_node, 'down')

    # 15.14 检查是否到达底部
    if self.em.find_node(text="为你推荐"):
        break

    # 15.15 检查"查看更多"按钮
    view_more = self.em.wait_for(text="查看更多", click=True, timeout=5)
    if view_more:
        continue
```

## 需要用户提供的参数

### 基础参数
1. **模拟器ID** (emulator_id): 目标模拟器的ID编号
2. **私信任务数量** (私信任务数量): 要发送的私信总数
3. **私信话术** (msg_data): 私信内容，用"|"分隔多个话术
4. **V2Ray订阅链接** (node_client): V2Ray节点订阅地址

### 延迟参数
5. **通用延迟1** (通用延迟1): 操作间最小延迟时间(秒)
6. **通用延迟2** (通用延迟2): 操作间最大延迟时间(秒)

### 超时参数
7. **总任务超时时间** (timeout_total): 整个任务的最大执行时间(秒)
8. **V2Ray超时时间** (timeout_v2ray): V2Ray连接超时时间(秒)
9. **节点检测超时** (timeout_v2ray_node): 单个节点测试超时时间(秒)

### 应用路径参数
10. **V2Ray APK路径** (v2ray_apk_path): V2Ray安装包路径
11. **Instagram APK路径** (instagram_apk_path): Instagram安装包路径

### 可选参数
12. **雷电模拟器安装路径** (ld_base_path): 默认 "D:/LDPlayer9.0.66"
13. **共享文件夹路径** (ld_share_path): 默认 "C:/Users/<USER>/Documents/leidian64"
14. **是否卸载重装APP** (uninstall_app): 是否重新安装V2Ray
15. **是否更新节点订阅** (is_update_node): 是否更新V2Ray订阅

## 循环调用的关键参数

### V2Ray节点连接循环参数：
- **node_positions**: 可用节点位置列表
- **selected_node**: 当前选中的节点
- **connection_status**: 连接状态 ("connected"/"disconnected"/"timeout")
- **ping_result**: ping测试结果
- **retry_count**: 节点重试次数
- **swipe_actions**: 随机滑动动作配置

### 私信发送循环参数：
- **followers**: 当前屏幕可见粉丝列表
- **follower['username']**: 当前粉丝的用户名
- **follower['position']**: 粉丝头像的点击坐标
- **sent_users**: 已发送用户的去重集合
- **sent_count**: 当前已发送私信数量
- **私信任务数量**: 目标发送数量
- **msg_data**: 私信话术内容
- **list_node**: 粉丝列表滚动节点

### 状态检查参数：
- **stop_flag**: 任务停止标志
- **timeout_total**: 总任务超时检查
- **timeout_v2ray**: V2Ray连接超时检查
- **timeout_v2ray_node**: 节点测试超时检查
- **retry_count**: 重试次数计数

## 关键资源ID标识

### V2Ray应用标识：
- 节点名称: "com.v2ray.ang:id/tv_name"
- 连接状态: "com.v2ray.ang:id/tv_test_state"
- 连接按钮: "com.v2ray.ang:id/fab"
- 节点列表: "com.v2ray.ang:id/recycler_view"

### Instagram应用标识：
- 个人主页标识: "com.instagram.android:id/profile_tab"
- 粉丝数标识: "com.instagram.android:id/row_profile_header_textview_followers_count"
- 粉丝列表容器: "com.instagram.android:id/follow_list_container"
- 粉丝用户名: "com.instagram.android:id/follow_list_username"
- 粉丝操作按钮: "com.instagram.android:id/follow_list_right_follow_button"
- 私信输入框: "com.instagram.android:id/row_thread_composer_edittext"
- 发送按钮: "com.instagram.android:id/row_thread_composer_button_send"
- 返回按钮: "com.instagram.android:id/action_bar_button_back"
- 滚动列表: "android:id/list"

### 图像模板标识：
- V2Ray图标: "img/v2ray.png"
- Instagram图标: "img/ins.png"
- 节点项目: "img/s.png"
- 已连接状态: "img/l1.png"
- 未连接状态: "img/l2.png"
- 加号按钮: "img/jiahao.png"
- 导入按钮: "img/daoru.png"
- 三点菜单: "img/three_point.png"
- 更新订阅: "img/update.png"

## 错误处理和异常情况

### V2Ray连接异常处理：
1. **节点列表为空**: 自动导入订阅链接，重试最多3次
2. **连接失败**: 自动切换节点，重试最多5次
3. **ping测试失败**: 更换节点重新测试，最多5次
4. **V2Ray应用崩溃**: 重启应用，重新连接
5. **订阅更新失败**: 检查网络连接，重试导入

### Instagram私信异常处理：
1. **粉丝列表为空**: 检查账号是否有粉丝
2. **连续3次未找到粉丝**: 停止任务
3. **操作频繁提示**: 检测"请稍后重试"文本，终止任务
4. **请求待审核**: 检测相关文本，终止任务
5. **应用无响应**: 重启Instagram应用
6. **任务超时**: 检查总执行时间是否超过限制

### 去重机制：
- 使用 `sent_users.txt` 文件记录已发送用户
- 每次发送前检查用户名是否已存在
- 发送成功后立即写入记录文件

### 重试机制：
- 模拟器启动失败：最多重试3次
- V2Ray连接失败：最多重试3次
- Instagram启动失败：最多重试3次
- 节点ping测试：每个节点最多5次
- 私信发送失败：跳过当前用户，继续下一个

## 实现要点

### 雷电模拟器原生API使用方式

参考代码中通过以下方式使用雷电模拟器原生API：

#### 1. 初始化Dnconsole实例
```python
from LeiDian import Dnconsole
self.ld = Dnconsole(self.ld_base_path, self.ld_share_path, em=self)
```

#### 2. 核心API调用方法
```python
# 模拟器控制
self.ld.launch(emulator_id)                    # 启动模拟器
self.ld.quit(emulator_id)                      # 关闭模拟器
self.ld.reboot(emulator_id)                    # 重启模拟器
self.ld.is_running(emulator_id)                # 检查运行状态

# 应用管理
self.ld.runApp(emulator_id, package_name)      # 启动应用
self.ld.killApp(emulator_id, package_name)     # 终止应用
self.ld.installappOfFile(emulator_id, apk_path) # 安装APK
self.ld.uninstallapp(emulator_id, package_name) # 卸载应用
self.ld.appVersion(emulator_id, package_name)   # 获取应用版本
self.ld.appIsrunning(emulator_id, package_name) # 检查应用运行状态

# 界面操作
self.ld.touch(emulator_id, x, y)               # 坐标点击
self.ld.swipe(emulator_id, start, end, duration) # 滑动操作
self.ld.input_text(emulator_id, text)          # 输入文本
self.ld.get_activity_name()                    # 获取当前Activity

# 元素查找和操作（通过EmulatorThread封装）
self.em.find_node(resource_id="xxx")           # 查找单个元素
self.em.find_nodes(resource_id="xxx")          # 查找多个元素
self.em.click_node(node)                       # 点击元素节点

# 图像识别和等待
self.ld.wait_for_image(hwnd, templates, timeout) # 等待图像出现
self.ld.wait_and_click(template, timeout)      # 等待并点击图像
self.ld.wait_for(resource_id, timeout, click)  # 等待元素出现
self.ld.capture_window_back(hwnd)              # 截图
self.ld.find_color(screenshot, color, threshold) # 颜色识别

# 高级功能
self.ld.random_swipe(actions, duration)        # 随机滑动
self.ld.background_mouse_click(hwnd, x, y)     # 后台点击
self.ld.window_to_screen(window_coords)        # 坐标转换
```

#### 3. 关键特性
1. **双重定位机制**: 结合resource_id和图像模板定位
2. **智能等待**: wait_for系列方法支持超时和条件等待
3. **后台操作**: 支持后台窗口操作，不影响前台使用
4. **坐标转换**: 自动处理窗口坐标到屏幕坐标的转换
5. **多重匹配**: 支持单个和多个元素匹配
6. **状态监控**: 实时获取应用和模拟器状态
7. **异常恢复**: 内置重试和错误处理机制
