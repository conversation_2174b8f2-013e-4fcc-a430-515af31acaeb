#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
右边界预判断功能测试工具
========================================
功能描述: 测试新增的右边界预判断功能
创建时间: 2025-07-23
作者: AI Assistant

使用方法:
1. 确保模拟器运行并且在Instagram私信界面
2. 运行: python test_boundary_judgment.py
3. 程序会测试右边界预判断功能的准确性

注意: 此程序只测试判断逻辑，不会执行实际的撤回操作
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.instagram_task import InstagramDMTask
from core.simple_config import get_config_manager
from core.logger_manager import log_info, log_error


class BoundaryJudgmentTester:
    """右边界预判断测试器"""

    def __init__(self, emulator_id: int = 2):
        """初始化测试器"""
        self.emulator_id = emulator_id
        self.config_manager = get_config_manager()
        self.instagram_task = None
        
    async def setup_tester(self):
        """设置测试器"""
        try:
            print(f"🔧 设置测试器 (模拟器 {self.emulator_id})")
            
            # 创建Instagram任务实例
            self.instagram_task = InstagramDMTask(self.emulator_id, self.config_manager)
            
            # 验证模拟器状态
            if not self.instagram_task.ld.is_running(self.emulator_id):
                print(f"❌ 模拟器 {self.emulator_id} 未运行")
                return False
            
            screen_size = self.instagram_task.ld.get_screen_size(self.emulator_id)
            print(f"✅ 模拟器 {self.emulator_id} 运行中 (分辨率: {screen_size[0]}×{screen_size[1]})")
            return True
            
        except Exception as e:
            print(f"❌ 设置测试器失败: {e}")
            return False

    async def test_boundary_judgment(self):
        """测试右边界预判断功能"""
        try:
            print("🔍 测试右边界预判断功能...")
            
            # 强制刷新UI状态
            screen_width, screen_height = self.instagram_task.ld.get_screen_size(self.emulator_id)
            center_x = screen_width // 2
            center_y = screen_height // 2
            
            # 轻微滑动来刷新UI
            swipe_command = f"input swipe {center_x} {center_y} {center_x} {center_y + 5} 50"
            self.instagram_task.ld.execute_ld(self.emulator_id, swipe_command)
            await asyncio.sleep(0.1)
            
            swipe_command = f"input swipe {center_x} {center_y + 5} {center_x} {center_y} 50"
            self.instagram_task.ld.execute_ld(self.emulator_id, swipe_command)
            await asyncio.sleep(0.5)
            
            # 获取消息元素
            message_elements = await self.instagram_task._find_all_message_elements()
            
            if not message_elements:
                print("📭 当前界面无消息")
                return True
            
            print(f"\n📨 找到 {len(message_elements)} 条消息，开始测试预判断:")
            print("=" * 80)
            
            threshold = screen_width * 0.92
            print(f"📱 屏幕宽度: {screen_width}px")
            print(f"🎯 判断阈值: {threshold:.0f}px (92%)")
            print("=" * 80)
            
            # 测试每条消息的预判断
            own_count = 0
            other_count = 0
            failed_count = 0
            
            for i, element in enumerate(message_elements):
                print(f"\n消息 {i+1}:")
                
                # 使用新的预判断方法
                boundary_result = self.instagram_task._is_own_message_by_boundary(element)
                
                # 获取详细信息用于验证
                bounds = element.get('bounds', '')
                message_type = element.get('message_type', 'unknown')
                text = element.get('text', '')[:25] + '...' if len(element.get('text', '')) > 25 else element.get('text', '')
                
                print(f"  类型: {message_type}")
                if text:
                    print(f"  文本: '{text}'")
                print(f"  边界: {bounds}")
                
                if boundary_result is True:
                    own_count += 1
                    print(f"  🟦 预判断结果: 自己的消息")
                    
                    # 显示判断详情
                    if bounds:
                        import re
                        coords = re.findall(r'\d+', bounds)
                        if len(coords) >= 4:
                            right = int(coords[2])
                            print(f"  📊 判断依据: 右边界={right} >= {threshold:.0f} ✅")
                        
                elif boundary_result is False:
                    other_count += 1
                    print(f"  🟨 预判断结果: 对方的消息")
                    
                    # 显示判断详情
                    if bounds:
                        import re
                        coords = re.findall(r'\d+', bounds)
                        if len(coords) >= 4:
                            right = int(coords[2])
                            print(f"  📊 判断依据: 右边界={right} < {threshold:.0f} ✅")
                            
                else:
                    failed_count += 1
                    print(f"  ❓ 预判断结果: 无法判断")
                    print(f"  📊 原因: 边界信息缺失或解析失败")
                
                print("-" * 60)
            
            # 显示统计结果
            print(f"\n📊 预判断统计结果:")
            print(f"  🟦 自己的消息: {own_count} 条")
            print(f"  🟨 对方的消息: {other_count} 条")
            print(f"  ❓ 无法判断: {failed_count} 条")
            print(f"  📱 判断成功率: {((own_count + other_count) / len(message_elements) * 100):.1f}%")
            
            # 显示预期效果
            print(f"\n🚀 预期撤回效果:")
            if own_count > 0:
                print(f"  ✅ 将对 {own_count} 条自己的消息执行长按撤回")
            if other_count > 0:
                print(f"  ⏭️  将跳过 {other_count} 条对方的消息 (节省 {other_count * 2} 秒长按时间)")
            if failed_count > 0:
                print(f"  🔄 将对 {failed_count} 条无法判断的消息使用原有流程")
            
            return True
                
        except Exception as e:
            print(f"❌ 测试预判断功能异常: {e}")
            return False

    async def run_test(self):
        """运行完整的测试流程"""
        try:
            print("🔍 右边界预判断功能测试工具")
            print("=" * 60)
            print("此工具会测试新增的右边界预判断功能")
            print("=" * 60)
            
            # 1. 设置测试器
            if not await self.setup_tester():
                return
            
            # 2. 测试预判断功能
            success = await self.test_boundary_judgment()
            
            # 3. 显示测试结果
            print("\n" + "=" * 60)
            print("测试结果:")
            print("=" * 60)
            
            if success:
                print("✅ 右边界预判断功能测试成功!")
                print("✅ 预判断逻辑工作正常")
                print("✅ 可以有效提高撤回效率")
                print("\n💡 功能说明:")
                print("  1. 预判断会在长按前识别消息归属")
                print("  2. 对方的消息将被直接跳过，节省时间")
                print("  3. 自己的消息将继续执行长按撤回")
                print("  4. 无法判断的消息将使用原有流程作为兜底")
            else:
                print("❌ 右边界预判断功能测试失败")
                print("❌ 可能的问题:")
                print("  1. 界面不在正确的Instagram私信页面")
                print("  2. 消息元素获取失败")
                print("  3. 边界信息解析异常")
            
        except Exception as e:
            print(f"❌ 运行测试异常: {e}")


async def main():
    """主函数"""
    try:
        print("右边界预判断功能测试工具")
        print("此工具会测试新增的预判断功能，不会执行实际撤回")
        
        # 询问模拟器ID
        emulator_input = input("请输入模拟器ID (默认2): ").strip()
        emulator_id = 2 if not emulator_input else int(emulator_input)
        
        confirm = input(f"确认测试模拟器 {emulator_id} 的预判断功能？(y/N): ").strip().lower()
        if confirm != 'y':
            print("测试已取消")
            return
        
        tester = BoundaryJudgmentTester(emulator_id=emulator_id)
        await tester.run_test()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
