#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 雷电模拟器中控系统 - 设置界面模块
========================================
功能描述: 系统设置界面的创建、配置管理和用户交互
主要方法: __init__(), init_ui(), setup_connections(), load_settings()
调用关系: 被MainWindowV2调用，通过事件总线与业务层通信
注意事项:
- UI层只负责界面展示，业务逻辑通过事件总线处理
- 支持模拟器操作、心跳监控、窗口排列三个配置页面
- 使用统一配置管理器进行设置保存和加载
- 所有配置变更通过信号通知其他组件
========================================
"""

# ============================================================================
# 🎯 设置界面主类功能组
# ============================================================================
# 功能描述: 设置界面主类，负责界面创建和事件处理
# 调用关系: 被MainWindowV2调用，通过事件总线与业务层通信
# 注意事项: UI层只负责界面展示，业务逻辑通过事件总线处理
# ============================================================================

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                           QLabel, QGroupBox, QCheckBox, QScrollArea, QGridLayout)
from PyQt6.QtCore import pyqtSignal, QTimer
from PyQt6.QtGui import QColor
import logging

# 导入自定义UI组件
from .styled_widgets import (StyledButton, ModernSpinBox, ModernToggleSwitch,
                           StatusCard, HeartbeatIndicator, StyledLineEdit)
from .style_manager import (StyleManager, create_secondary_button, create_warning_button,
                          create_danger_button, CHECKBOX_STYLE)


class SettingsUI(QWidget):
    """
    系统设置界面
    ========================================
    功能描述: 雷电模拟器中控系统的设置配置界面
    主要功能:
    - 模拟器操作设置：并发数、启动间隔、超时时间等
    - 心跳监控设置：监控间隔、超时时间、自动切换等
    - 窗口排列设置：窗口布局和排列参数
    - 与统一配置系统集成，支持实时保存和加载
    ========================================
    """

    # 🎯 信号定义 - 设置变更通知
    setting_changed = pyqtSignal(str, object)  # 设置变更信号：参数名, 新值

    def __init__(self, parent=None, config_manager=None):
        """
        设置界面初始化
        ========================================
        功能描述: 初始化设置界面，创建UI和连接信号
        参数: parent=父窗口, config_manager=统一配置管理器
        ========================================
        """
        super().__init__(parent)

        # 🎯 配置管理器初始化 - 与统一配置系统集成
        if config_manager is not None:
            self.config_manager = config_manager
        else:
            # 兼容性：如果没有传入配置管理器，创建新实例
            from core.simple_config import get_config_manager
            self.config_manager = get_config_manager()

        # 🎯 简化版配置辅助 - 不使用复杂的配置辅助类
        self.config_helper = None
        self.settings_loader = None

        # 🎯 日志管理器初始化
        self.logger = logging.getLogger(self.__class__.__name__)

        # 正确的初始化顺序
        self.init_ui()           # 1. 创建控件
        self.load_settings()     # 2. 加载设置值
        self.setup_connections() # 3. 最后连接信号

    # ============================================================================
    # 🎯 1. UI界面创建和布局模块
    # ============================================================================
    # 功能描述: 创建设置界面的标签页布局，包含模拟器操作、心跳监控等页面
    # 调用关系: 被__init__方法调用，创建完整的设置界面
    # 注意事项: 使用标签页设计，便于分类管理不同类型的设置
    # ============================================================================

    def init_ui(self):
        """创建界面布局"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("系统设置")
        title_label.setStyleSheet("font-size: 22px; font-weight: bold; color: #333; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
                margin-top: 5px;
            }
            QTabBar::tab {
                background-color: #f5f5f5;
                color: #666;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: white;
                color: #8a56ac;
                border-bottom: 2px solid #8a56ac;
            }
            QTabBar::tab:hover {
                background-color: #f0f0f0;
                color: #333;
            }
        """)
        
        # 创建配置页面
        self.emulator_operations_page = self.create_emulator_operations_page()
        self.monitor_settings_page = self.create_monitor_settings_page()
        self.window_arrangement_page = self.create_window_arrangement_page()

        self.tab_widget.addTab(self.emulator_operations_page, "模拟器操作")
        self.tab_widget.addTab(self.monitor_settings_page, "心跳监控")
        self.tab_widget.addTab(self.window_arrangement_page, "窗口排列")

        layout.addWidget(self.tab_widget)

    # ============================================================================
    # 🎯 2. 模拟器操作设置页面模块
    # ============================================================================
    # 功能描述: 创建模拟器操作相关的配置界面，包含并发控制、启动参数等
    # 调用关系: 被标签页调用，提供模拟器操作的各种参数设置
    # 注意事项: 集成统一配置管理，支持实时保存和验证
    # ============================================================================

    def create_emulator_operations_page(self):
        """创建模拟器操作配置页面 - 并发数、启动间隔、超时等参数设置"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # 创建网格布局
        grid_layout = QGridLayout()
        grid_layout.setVerticalSpacing(15)
        grid_layout.setHorizontalSpacing(10)
        grid_layout.setColumnMinimumWidth(0, 200)
        grid_layout.setColumnStretch(1, 0)
        grid_layout.setColumnStretch(2, 1)

        row = 0

        # 最大并发任务数
        label = QLabel("最大并发任务数:")
        label.setStyleSheet("font-size: 14px; font-weight: bold; color: #333;")
        self.max_concurrent_tasks_spinbox = ModernSpinBox(suffix=" 个")
        self.max_concurrent_tasks_spinbox.setFixedWidth(200)
        grid_layout.addWidget(label, row, 0)
        grid_layout.addWidget(self.max_concurrent_tasks_spinbox, row, 1)
        row += 1

        # 模拟器启动间隔
        label = QLabel("模拟器启动间隔:")
        label.setStyleSheet("font-size: 14px; font-weight: bold; color: #333;")
        self.start_interval_spinbox = ModernSpinBox(suffix=" 秒")
        self.start_interval_spinbox.setFixedWidth(200)
        grid_layout.addWidget(label, row, 0)
        grid_layout.addWidget(self.start_interval_spinbox, row, 1)
        row += 1

        # 模拟器启动超时
        label = QLabel("模拟器启动超时:")
        label.setStyleSheet("font-size: 14px; font-weight: bold; color: #333;")
        self.start_timeout_spinbox = ModernSpinBox(suffix=" 秒")
        self.start_timeout_spinbox.setFixedWidth(200)
        grid_layout.addWidget(label, row, 0)
        grid_layout.addWidget(self.start_timeout_spinbox, row, 1)
        row += 1

        # 启动超时重试次数
        label = QLabel("模拟器启动超时重试次数:")
        label.setStyleSheet("font-size: 14px; font-weight: bold; color: #333;")
        self.start_fail_limit_spinbox = ModernSpinBox(suffix=" 次")
        self.start_fail_limit_spinbox.setFixedWidth(200)
        grid_layout.addWidget(label, row, 0)
        grid_layout.addWidget(self.start_fail_limit_spinbox, row, 1)
        row += 1

        # 任务接力间隔
        label = QLabel("任务接力间隔:")
        label.setStyleSheet("font-size: 14px; font-weight: bold; color: #333;")
        self.task_relay_delay_spinbox = ModernSpinBox(suffix=" 秒")
        self.task_relay_delay_spinbox.setFixedWidth(200)
        grid_layout.addWidget(label, row, 0)
        grid_layout.addWidget(self.task_relay_delay_spinbox, row, 1)

        # 添加到主布局
        layout.addLayout(grid_layout)
        layout.addStretch()

        return page

    # 异常设置页面
    def create_exception_settings_page(self):
        """创建异常处理设置页面"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")

        # 创建内容容器
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(30)

        # 异常处理配置区域
        exception_section = self.create_exception_handling_section()
        layout.addWidget(exception_section)

        # 故障处理配置区域
        failure_section = self.create_failure_time_section()
        layout.addWidget(failure_section)

        layout.addStretch()
        scroll_area.setWidget(content_widget)
        return scroll_area

    # 监控设置页面
    def create_monitor_settings_page(self):
        """创建监控设置页面"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")

        # 创建内容容器
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(30)

        # 实时监控状态区域
        status_section = self.create_monitoring_status_section()
        layout.addWidget(status_section)

        # 心跳监控配置区域
        heartbeat_section = self.create_heartbeat_monitoring_section()
        layout.addWidget(heartbeat_section)

        # 异常处理配置区域
        exception_section = self.create_exception_handling_section()
        layout.addWidget(exception_section)

        # 熔断机制配置区域
        failure_section = self.create_failure_time_section()
        layout.addWidget(failure_section)

        layout.addStretch()

        scroll_area.setWidget(content_widget)
        return scroll_area

    # ============================================================================
    # 🎯 3. 信号连接模块
    # ============================================================================
    # 功能描述: 在所有控件创建和设置加载完成后连接信号
    # 调用关系: 被__init__()最后调用，连接UI控件信号到配置保存方法
    # 注意事项: 避免初始化时触发信号，确保信号连接时机正确
    # ============================================================================

    def setup_connections(self):
        """设置信号连接"""
        # 模拟器操作设置信号连接
        if hasattr(self, 'max_concurrent_tasks_spinbox'):
            self.max_concurrent_tasks_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("max_concurrent_tasks", v)
            )

        if hasattr(self, 'start_interval_spinbox'):
            self.start_interval_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("start_interval", v)
            )

        if hasattr(self, 'start_timeout_spinbox'):
            self.start_timeout_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("start_timeout", v)
            )

        if hasattr(self, 'start_fail_limit_spinbox'):
            self.start_fail_limit_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("start_fail_limit", v)
            )

        if hasattr(self, 'task_relay_delay_spinbox'):
            self.task_relay_delay_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("task_relay_delay", v)
            )

        # 任务活动检测设置信号连接
        if hasattr(self, 'task_activity_enabled_switch'):  # 任务活动检测开关
            self.task_activity_enabled_switch.toggled.connect(
                lambda v: self._on_setting_changed("monitoring.task_activity_enabled", v)
            )

        if hasattr(self, 'task_check_interval_spinbox'):  # 检测间隔
            self.task_check_interval_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("monitoring.task_check_interval", v)
            )

        if hasattr(self, 'task_response_timeout_spinbox'):  # 响应超时
            self.task_response_timeout_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("monitoring.task_response_timeout", v)
            )

        # 异常处理设置信号连接
        if hasattr(self, 'screenshot_path_input'):  # 截图保存路径
            self.screenshot_path_input.textChanged.connect(
                lambda v: self._on_setting_changed("monitoring.screenshot_path", v)
            )

        if hasattr(self, 'auto_cleanup_switch'):  # 自动清理开关
            self.auto_cleanup_switch.toggled.connect(
                lambda v: self._on_setting_changed("monitoring.auto_cleanup", v)
            )

        if hasattr(self, 'retention_days_spinbox'):  # 保留天数
            self.retention_days_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("monitoring.retention_days", v)
            )

        # 故障机制设置信号连接
        if hasattr(self, 'failure_count_spinbox'):  # 失败触发次数
            self.failure_count_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("monitoring.failure_count", v)
            )

        if hasattr(self, 'auto_switch_switch'):  # 自动切换模拟器
            self.auto_switch_switch.toggled.connect(
                lambda v: self._on_setting_changed("monitoring.auto_switch", v)
            )

        if hasattr(self, 'auto_reset_on_startup_cb'):  # 自动重置选项
            self.auto_reset_on_startup_cb.stateChanged.connect(
                lambda _: self._on_setting_changed("monitoring.auto_reset_on_startup", self.auto_reset_on_startup_cb.isChecked())
            )

        # 截图叠加选项信号连接
        if hasattr(self, 'overlay_timestamp_cb'):  # 时间戳叠加
            self.overlay_timestamp_cb.stateChanged.connect(
                lambda: self._on_setting_changed("monitoring.overlay_timestamp", self.overlay_timestamp_cb.isChecked())
            )

        if hasattr(self, 'overlay_emulator_id_cb'):  # 模拟器ID叠加
            self.overlay_emulator_id_cb.stateChanged.connect(
                lambda: self._on_setting_changed("monitoring.overlay_emulator_id", self.overlay_emulator_id_cb.isChecked())
            )

        if hasattr(self, 'overlay_error_type_cb'):  # 错误类型叠加
            self.overlay_error_type_cb.stateChanged.connect(
                lambda: self._on_setting_changed("monitoring.overlay_error_type", self.overlay_error_type_cb.isChecked())
            )

        if hasattr(self, 'overlay_failure_count_cb'):  # 失败次数叠加
            self.overlay_failure_count_cb.stateChanged.connect(
                lambda: self._on_setting_changed("monitoring.overlay_failure_count", self.overlay_failure_count_cb.isChecked())
            )

        # 故障处理设置信号连接
        if hasattr(self, 'failure_count_spinbox'):  # 失败触发次数
            self.failure_count_spinbox.valueChanged.connect(
                lambda v: self._on_setting_changed("failure_count", v)
            )

        if hasattr(self, 'auto_switch_switch'):  # 自动切换模拟器
            self.auto_switch_switch.toggled.connect(
                lambda v: self._on_setting_changed("auto_switch_emulator", v)
            )

        # 窗口排列设置信号连接
        if hasattr(self, 'auto_arrange_switch'):  # 自动排列开关
            self.auto_arrange_switch.toggled.connect(
                lambda v: self._on_window_arrangement_setting_changed("auto_arrange_windows", v)
            )

        if hasattr(self, 'window_width_spinbox'):  # 窗口宽度
            self.window_width_spinbox.valueChanged.connect(
                lambda v: self._on_window_arrangement_setting_changed("window_width", v if v > 0 else None)
            )

        if hasattr(self, 'window_height_spinbox'):  # 窗口高度
            self.window_height_spinbox.valueChanged.connect(
                lambda v: self._on_window_arrangement_setting_changed("window_height", v if v > 0 else None)
            )

        if hasattr(self, 'column_spacing_spinbox'):  # 列间距
            self.column_spacing_spinbox.valueChanged.connect(
                lambda v: self._on_window_arrangement_setting_changed("column_spacing", v)
            )

        if hasattr(self, 'row_spacing_spinbox'):  # 行间距
            self.row_spacing_spinbox.valueChanged.connect(
                lambda v: self._on_window_arrangement_setting_changed("row_spacing", v)
            )

        if hasattr(self, 'windows_per_row_spinbox'):  # 每行数量
            self.windows_per_row_spinbox.valueChanged.connect(
                lambda v: self._on_window_arrangement_setting_changed("windows_per_row", v)
            )

        if hasattr(self, 'windows_per_column_spinbox'):  # 每列数量
            self.windows_per_column_spinbox.valueChanged.connect(
                lambda v: self._on_window_arrangement_setting_changed("windows_per_column", v)
            )

        if hasattr(self, 'arrange_delay_spinbox'):  # 排列延迟
            self.arrange_delay_spinbox.valueChanged.connect(
                lambda v: self._on_window_arrangement_setting_changed("arrange_delay", v)
            )

        # 按钮事件连接
        if hasattr(self, 'refresh_btn'):  # 刷新按钮
            self.refresh_btn.clicked.connect(self._on_refresh_clicked)

        if hasattr(self, 'test_screenshot_btn'):  # 测试截图按钮
            self.test_screenshot_btn.clicked.connect(self._on_test_screenshot_clicked)

        if hasattr(self, 'browse_button'):  # 浏览按钮
            self.browse_button.clicked.connect(self._on_browse_path_clicked)

        if hasattr(self, 'view_folder_button'):  # 查看文件夹按钮
            self.view_folder_button.clicked.connect(self._on_view_folder_clicked)

        if hasattr(self, 'manual_reset_button'):  # 手动重置按钮
            self.manual_reset_button.clicked.connect(self._on_manual_reset_clicked)

    # ============================================================================
    # 🎯 4. 配置管理模块
    # ============================================================================
    # 功能描述: 处理设置的加载、保存、验证等配置管理功能
    # 调用关系: 被UI控件调用，与统一配置系统交互
    # 注意事项: 确保配置的实时保存和错误处理
    # ============================================================================

    def load_settings(self):
        """加载所有设置 - 从统一配置系统加载各模块参数到UI控件"""
        self._load_emulator_operations_settings()
        self._load_monitor_settings()
        self._load_exception_settings()
        self._load_overlay_settings()
        self._load_window_arrangement_settings()

    def _load_emulator_operations_settings(self):
        """🎯 加载模拟器操作设置 - 使用统一配置"""
        if not self.config_manager:
            return

        try:
            # 🎯 模拟器操作设置列表 - 使用扁平化配置
            emulator_settings = [
                ('max_concurrent_tasks_spinbox', 'max_concurrent_tasks', 2),
                ('start_interval_spinbox', 'start_interval', 2),
                ('start_timeout_spinbox', 'start_timeout', 12),
                ('start_fail_limit_spinbox', 'start_fail_limit', 2),
                ('task_relay_delay_spinbox', 'task_relay_delay', 2)
            ]

            for widget_name, config_key, default_value in emulator_settings:
                if hasattr(self, widget_name):
                    widget = getattr(self, widget_name)
                    value = self.config_manager.get(config_key, default_value)
                    if hasattr(widget, 'setValue'):
                        widget.setValue(value)
                        self.logger.debug(f"加载模拟器操作设置: {config_key} = {value}")
        except Exception as e:
            self.logger.error(f"加载模拟器操作设置失败: {e}")

    def _on_setting_changed(self, key: str, value):
        """处理设置变更 - 保存到统一配置系统并发送变更信号"""
        try:
            if not self.config_manager:
                self.logger.warning(f"配置管理器未初始化，设置更新失败: {key} = {value}")
                return

            # 🎯 根据设置类型保存到正确的分类 - 统一配置路径
            success = False

            # 🎯 直接使用传入的配置键 - 支持完整路径
            if key.startswith("monitoring.") or key.startswith("window_arrangement."):
                # 已经包含完整路径的配置键，直接使用
                success = self.config_manager.set(key, value)
            elif key in ["max_concurrent_tasks", "start_interval", "start_timeout", "start_fail_limit", "task_relay_delay"]:
                # 模拟器操作设置，直接使用键名
                success = self.config_manager.set(key, value)
            else:
                # 其他设置，添加到emulator_operations分组
                success = self.config_manager.set(f"emulator_operations.{key}", value)

            if success:
                # 🎯 延迟保存配置 - 避免频繁保存，使用QTimer延迟执行
                if hasattr(self, '_save_timer'):
                    self._save_timer.stop()
                else:
                    self._save_timer = QTimer()
                    self._save_timer.setSingleShot(True)
                    self._save_timer.timeout.connect(self._delayed_save_config)

                # 延迟500ms保存，避免用户快速输入时频繁保存
                self._save_timer.start(500)

                # 🎯 观察者模式会自动通知相关组件，无需手动发射信号
                self.logger.debug(f"设置已更新: {key} = {value}（观察者模式自动通知）")
            else:
                self.logger.warning(f"设置更新失败: {key} = {value}")

        except Exception as e:
            self.logger.error(f"设置更新失败: {key}, 错误: {e}")

    def _on_window_arrangement_setting_changed(self, key: str, value):
        """🎯 处理窗口排列设置变更"""
        try:
            if not self.config_manager:
                self.logger.warning(f"配置管理器未初始化，窗口排列设置更新失败: {key} = {value}")
                return

            # 保存到window_arrangement分组
            success = self.config_manager.set(f"window_arrangement.{key}", value)

            if success:
                # 延迟保存配置
                QTimer.singleShot(500, self._delayed_save_config)
                self.logger.debug(f"窗口排列设置已更新: {key} = {value}")
            else:
                self.logger.warning(f"窗口排列设置更新失败: {key} = {value}")

        except Exception as e:
            self.logger.error(f"窗口排列设置更新失败: {key}, 错误: {e}")

    def _delayed_save_config(self):
        """🎯 延迟保存配置 - 在Qt主线程中安全执行"""
        try:
            self.config_manager.save()
            self.logger.debug("配置延迟保存完成")
        except Exception as e:
            self.logger.error(f"延迟保存配置失败: {e}")
            # 回退到同步保存
            self.config_manager.save()

    def _load_monitor_settings(self):
        """加载监控设置 - 使用统一配置键名"""
        if not self.config_manager:
            return

        try:
            # 🎯 任务活动检测设置 - 使用monitoring前缀
            if hasattr(self, 'task_activity_enabled_switch'):  # 启用任务活动检测开关
                value = self.config_manager.get('monitoring.task_activity_enabled', True)
                self.task_activity_enabled_switch.set_checked(value)

            if hasattr(self, 'task_check_interval_spinbox'):  # 检测间隔（秒）
                value = self.config_manager.get('monitoring.task_check_interval', 10)
                self.task_check_interval_spinbox.setValue(value)

            if hasattr(self, 'task_response_timeout_spinbox'):  # 响应超时（秒）
                value = self.config_manager.get('monitoring.task_response_timeout', 11)
                self.task_response_timeout_spinbox.setValue(value)

            # 🎯 故障机制设置
            if hasattr(self, 'failure_count_spinbox'):  # 失败触发次数
                value = self.config_manager.get('monitoring.failure_count', 3)
                self.failure_count_spinbox.setValue(value)

            if hasattr(self, 'auto_switch_switch'):  # 自动切换模拟器开关
                value = self.config_manager.get('monitoring.auto_switch', True)
                self.auto_switch_switch.set_checked(value)

            if hasattr(self, 'auto_reset_on_startup_cb'):  # 启动时自动重置失败计数
                value = self.config_manager.get('monitoring.auto_reset_on_startup', True)
                self.auto_reset_on_startup_cb.setChecked(value)
        except Exception as e:
            self.logger.error(f"加载监控设置失败: {e}")

    def _load_exception_settings(self):
        """加载异常处理设置 - 使用统一配置键名"""
        if not self.config_manager:
            return

        try:
            # 🎯 异常处理设置 - 使用monitoring前缀
            if hasattr(self, 'screenshot_path_input'):  # 异常截图保存路径
                value = self.config_manager.get('monitoring.screenshot_path', './screenshots/')
                self.screenshot_path_input.setText(value)

            if hasattr(self, 'auto_cleanup_switch'):  # 自动清理过期截图开关
                value = self.config_manager.get('monitoring.auto_cleanup', True)
                self.auto_cleanup_switch.set_checked(value)

            if hasattr(self, 'retention_days_spinbox'):  # 截图保留天数
                value = self.config_manager.get('monitoring.retention_days', 7)
                self.retention_days_spinbox.setValue(value)
        except Exception as e:
            self.logger.error(f"加载异常设置失败: {e}")



    def _load_overlay_settings(self):
        """加载截图信息叠加设置 - 使用统一配置键名"""
        if not self.config_manager:
            return

        try:
            # 🎯 截图信息叠加设置列表 - (控件名, 配置键, 默认值)
            overlay_settings = [
                ('overlay_timestamp_cb', 'overlay_timestamp', True),      # 截图叠加时间戳
                ('overlay_emulator_id_cb', 'overlay_emulator_id', False), # 截图叠加模拟器ID
                ('overlay_error_type_cb', 'overlay_error_type', True),    # 截图叠加错误类型
                ('overlay_failure_count_cb', 'overlay_failure_count', False), # 截图叠加失败次数
            ]

            for widget_name, config_key, default_value in overlay_settings:
                if hasattr(self, widget_name):
                    widget = getattr(self, widget_name)
                    value = self.config_manager.get(f'monitoring.{config_key}', default_value)
                    # 根据控件类型使用正确的方法
                    if hasattr(widget, 'set_checked'):
                        widget.set_checked(value)  # ModernToggleSwitch
                    elif hasattr(widget, 'setChecked'):
                        widget.setChecked(value)  # QCheckBox
        except Exception as e:
            self.logger.error(f"加载叠加设置失败: {e}")

    def _load_window_arrangement_settings(self):
        """🎯 加载窗口排列设置"""
        if not self.config_manager:
            return

        try:
            # 🎯 窗口排列设置列表 - (控件名, 配置键, 默认值)
            arrangement_settings = [
                ('auto_arrange_switch', 'auto_arrange_windows', True),      # 自动排列窗口开关
                ('window_width_spinbox', 'window_width', 0),               # 窗口宽度
                ('window_height_spinbox', 'window_height', 0),             # 窗口高度
                ('column_spacing_spinbox', 'column_spacing', 0),           # 列间距
                ('row_spacing_spinbox', 'row_spacing', 0),                 # 行间距
                ('windows_per_row_spinbox', 'windows_per_row', 7),         # 每行窗口数
                ('windows_per_column_spinbox', 'windows_per_column', 2),   # 每列窗口数
                ('arrange_delay_spinbox', 'arrange_delay', 2000),          # 排列延迟（毫秒）
            ]

            for widget_name, config_key, default_value in arrangement_settings:
                if hasattr(self, widget_name):
                    widget = getattr(self, widget_name)
                    value = self.config_manager.get(f'window_arrangement.{config_key}', default_value)

                    # 根据控件类型使用正确的方法
                    if hasattr(widget, 'set_checked'):
                        widget.set_checked(value)  # ModernToggleSwitch
                    elif hasattr(widget, 'setValue'):
                        # 处理None值的情况
                        if value is not None:
                            widget.setValue(value)  # ModernSpinBox

        except Exception as e:
            self.logger.error(f"加载窗口排列设置失败: {e}")

    # closeEvent已删除 - 配置保存由main.py统一处理，避免重复保存

    # ============================================================================
    # 🎯 6. UI组件创建模块
    # ============================================================================
    # 功能描述: 创建各种UI组件，使用统一样式管理器
    # 调用关系: 被create_monitor_settings_page()等页面创建方法调用
    # 注意事项: 使用StyleManager统一样式，避免重复代码
    # ============================================================================

    def create_monitoring_status_section(self):
        """创建实时监控状态区域"""
        status_group = QGroupBox("实时监控状态")
        status_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e3f2fd;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 15px;
                background-color: #f3f9ff;
                min-height: 200px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 12px 0 12px;
                background-color: #f3f9ff;
                color: #1976D2;
            }
        """)

        layout = QVBoxLayout(status_group)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 状态卡片行
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(15)

        self.monitoring_status_card = StatusCard("监控状态", "已启用", "🔄")
        self.active_monitors_card = StatusCard("活跃监测", "0", "💓")
        self.failed_emulators_card = StatusCard("异常模拟器", "0", "⚠️")
        self.total_screenshots_card = StatusCard("异常截图", "0", "📸")

        cards_layout.addWidget(self.monitoring_status_card)
        cards_layout.addWidget(self.active_monitors_card)
        cards_layout.addWidget(self.failed_emulators_card)
        cards_layout.addWidget(self.total_screenshots_card)

        layout.addLayout(cards_layout)

        # 心跳指示器和控制按钮区域
        heartbeat_layout = QHBoxLayout()
        heartbeat_layout.setSpacing(15)

        heartbeat_label = QLabel("系统心跳:")
        heartbeat_label.setStyleSheet("font-weight: bold; color: #333; font-size: 14px;")

        self.system_heartbeat_indicator = HeartbeatIndicator()
        self.system_heartbeat_indicator.set_active(True)
        self.system_heartbeat_indicator.set_color(QColor("#4CAF50"))

        # 控制按钮 - 使用样式管理器
        self.refresh_btn = create_secondary_button("🔄 刷新", (80, 30))
        self.test_screenshot_btn = create_warning_button("📸 测试截图", (100, 30))

        heartbeat_layout.addWidget(heartbeat_label)
        heartbeat_layout.addWidget(self.system_heartbeat_indicator)
        heartbeat_layout.addWidget(self.refresh_btn)
        heartbeat_layout.addWidget(self.test_screenshot_btn)
        heartbeat_layout.addStretch()

        layout.addLayout(heartbeat_layout)

        return status_group

    # ============================================================================
    # 🎯 3. 心跳监控设置模块
    # ============================================================================
    # 功能描述: 创建任务活动检测和心跳监控的配置界面
    # 调用关系: 被心跳监控页面调用，提供监控参数和控制选项
    # 注意事项: 集成实时状态显示和手动控制功能
    # ============================================================================

    def create_heartbeat_monitoring_section(self):
        """创建任务活动检测配置区域 - 监控间隔、超时时间、自动切换等设置"""
        heartbeat_group = QGroupBox("🎯 任务活动检测配置")
        heartbeat_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e8f5e8;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #f8fff8;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #f8fff8;
                color: #4CAF50;
            }
        """)

        # 使用QGridLayout替代QFormLayout，确保控件显示一致性
        layout = QGridLayout(heartbeat_group)
        layout.setContentsMargins(25, 25, 25, 25)  # 设置边距
        layout.setVerticalSpacing(15)  # 行间距
        layout.setHorizontalSpacing(5)  # 列间距
        layout.setColumnMinimumWidth(0, 120)  # 标签列固定宽度
        layout.setColumnStretch(1, 0)  # 控件列不伸缩
        layout.setColumnStretch(2, 1)  # 第三列用于填充剩余空间

        row = 0  # 初始化行计数器

        # 启用任务活动检测开关
        label = QLabel("启用心跳监测:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.task_activity_enabled_switch = ModernToggleSwitch()
        # 从配置加载初始值
        enabled = self.config_manager.get("monitoring.task_activity_enabled", True)
        self.task_activity_enabled_switch.set_checked(enabled)
        layout.addWidget(label, row, 0)
        layout.addWidget(self.task_activity_enabled_switch, row, 1)
        row += 1

        # 检测间隔
        label = QLabel("检测间隔:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.task_check_interval_spinbox = ModernSpinBox(suffix=" 秒")
        # 从配置加载初始值
        interval = self.config_manager.get("monitoring.task_check_interval", 10)
        self.task_check_interval_spinbox.setValue(interval)
        self.task_check_interval_spinbox.setFixedWidth(200)  # 统一宽度200px
        layout.addWidget(label, row, 0)
        layout.addWidget(self.task_check_interval_spinbox, row, 1)
        row += 1

        # 响应超时
        label = QLabel("响应超时:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.task_response_timeout_spinbox = ModernSpinBox(suffix=" 秒")
        # 从配置加载初始值
        timeout = self.config_manager.get("monitoring.task_response_timeout", 11)
        self.task_response_timeout_spinbox.setValue(timeout)
        self.task_response_timeout_spinbox.setFixedWidth(200)  # 统一宽度200px
        layout.addWidget(label, row, 0)
        layout.addWidget(self.task_response_timeout_spinbox, row, 1)

        return heartbeat_group

    # 异常处理配置
    def create_exception_handling_section(self):
        """创建异常处理配置区域"""
        exception_group = QGroupBox("异常处理配置")
        exception_group.setStyleSheet(StyleManager.get_groupbox_style('warning'))

        # 使用QGridLayout替代QFormLayout，确保控件显示一致性
        layout = QGridLayout(exception_group)
        layout.setContentsMargins(25, 25, 25, 25)  # 设置边距
        layout.setVerticalSpacing(15)  # 行间距
        layout.setHorizontalSpacing(5)  # 列间距
        layout.setColumnMinimumWidth(0, 120)  # 标签列固定宽度
        layout.setColumnStretch(1, 0)  # 控件列不伸缩
        layout.setColumnStretch(2, 1)  # 第三列用于填充剩余空间

        row = 0  # 初始化行计数器

        # 添加各个子部分
        row = self._add_screenshot_path_section(layout, row)  # 添加截图路径设置
        row = self._add_cleanup_settings_section(layout, row)  # 添加清理设置
        row = self._add_overlay_settings_section(layout, row)  # 添加叠加设置

        return exception_group

    def _add_screenshot_path_section(self, layout, row):
        """添加截图路径设置部分 - 拆分长方法"""
        from .styled_widgets import StyledLineEdit

        label = QLabel("截图保存路径:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))

        path_widget = QWidget()
        path_layout = QHBoxLayout(path_widget)
        path_layout.setContentsMargins(0, 0, 0, 0)
        path_layout.setSpacing(10)

        self.screenshot_path_input = StyledLineEdit()
        # 从配置加载初始值
        screenshot_path = self.config_manager.get("monitoring.screenshot_path", "./screenshots/")
        self.screenshot_path_input.setText(screenshot_path)
        self.screenshot_path_input.setMinimumWidth(500)

        self.browse_button = create_warning_button("浏览...", (80, 36))
        self.view_folder_button = StyleManager.create_button("📁 查看", 'primary', "6px 12px")
        self.view_folder_button.setFixedSize(80, 36)

        path_layout.addWidget(self.screenshot_path_input)
        path_layout.addWidget(self.browse_button)
        path_layout.addWidget(self.view_folder_button)

        layout.addWidget(label, row, 0)
        layout.addWidget(path_widget, row, 1)
        return row + 1

    def _add_cleanup_settings_section(self, layout, row):
        """添加清理设置部分 - 拆分长方法"""
        # 自动清理旧截图
        label = QLabel("自动清理旧截图:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        self.auto_cleanup_switch = ModernToggleSwitch()
        # 从配置加载初始值
        auto_cleanup = self.config_manager.get("monitoring.auto_cleanup", True)
        self.auto_cleanup_switch.set_checked(auto_cleanup)
        layout.addWidget(label, row, 0)
        layout.addWidget(self.auto_cleanup_switch, row, 1)
        row += 1

        # 保留天数 - 移除数值限制
        label = QLabel("保留天数:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        self.retention_days_spinbox = ModernSpinBox(suffix=" 天")
        # 从配置加载初始值
        retention_days = self.config_manager.get("monitoring.retention_days", 7)
        self.retention_days_spinbox.setValue(retention_days)
        self.retention_days_spinbox.setFixedWidth(200)
        layout.addWidget(label, row, 0)
        layout.addWidget(self.retention_days_spinbox, row, 1)
        return row + 1

    def _add_overlay_settings_section(self, layout, row):
        """添加叠加设置部分 - 拆分长方法"""
        label = QLabel("截图信息叠加:")
        label.setStyleSheet(StyleManager.get_label_style('form_label'))
        overlay_widget = self.create_overlay_options_widget()
        layout.addWidget(label, row, 0)
        layout.addWidget(overlay_widget, row, 1)
        return row + 1

    def create_overlay_options_widget(self):
        """创建截图信息叠加选项"""
        overlay_widget = QWidget()
        overlay_layout = QHBoxLayout(overlay_widget)
        overlay_layout.setContentsMargins(0, 0, 0, 0)
        overlay_layout.setSpacing(15)

        # 创建复选框
        self.overlay_timestamp_cb = QCheckBox("时间戳")
        self.overlay_emulator_id_cb = QCheckBox("模拟器ID")
        self.overlay_error_type_cb = QCheckBox("错误类型")
        self.overlay_failure_count_cb = QCheckBox("失败次数")

        # 使用统一的复选框样式
        for cb in [self.overlay_timestamp_cb, self.overlay_emulator_id_cb,
                   self.overlay_error_type_cb, self.overlay_failure_count_cb]:
            cb.setStyleSheet(CHECKBOX_STYLE)

        # 从配置加载初始值
        self.overlay_timestamp_cb.setChecked(
            self.config_manager.get("monitoring.overlay_timestamp", True)
        )
        self.overlay_emulator_id_cb.setChecked(
            self.config_manager.get("monitoring.overlay_emulator_id", False)
        )
        self.overlay_error_type_cb.setChecked(
            self.config_manager.get("monitoring.overlay_error_type", True)
        )
        self.overlay_failure_count_cb.setChecked(
            self.config_manager.get("monitoring.overlay_failure_count", False)
        )

        # 信号连接已移至setup_connections方法，避免重复连接

        # 添加到布局
        overlay_layout.addWidget(self.overlay_timestamp_cb)
        overlay_layout.addWidget(self.overlay_emulator_id_cb)
        overlay_layout.addWidget(self.overlay_error_type_cb)
        overlay_layout.addWidget(self.overlay_failure_count_cb)
        overlay_layout.addStretch()

        return overlay_widget

    # 熔断机制配置
    def create_failure_time_section(self):
        """创建熔断机制配置区域"""
        failure_group = QGroupBox("熔断机制配置")
        failure_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #ffebee;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #fef7f9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #fef7f9;
                color: #e91e63;
            }
        """)

        # 使用QGridLayout替代QFormLayout，彻底解决输入框显示问题
        layout = QGridLayout(failure_group)
        layout.setContentsMargins(25, 25, 25, 25)  # 设置边距
        layout.setVerticalSpacing(15)  # 行间距
        layout.setHorizontalSpacing(5)  # 列间距
        layout.setColumnMinimumWidth(0, 120)  # 标签列固定宽度
        layout.setColumnStretch(1, 0)  # 控件列不伸缩
        layout.setColumnStretch(2, 1)  # 第三列用于填充剩余空间

        row = 0  # 初始化行计数器

        # 删除重复的启动超时定义 - 已在基础设置中定义

        # 失败触发次数 - 移除数值限制
        label = QLabel("失败触发次数:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.failure_count_spinbox = ModernSpinBox(suffix=" 次")
        # 从配置加载初始值
        failure_count = self.config_manager.get("monitoring.failure_count", 3)
        self.failure_count_spinbox.setValue(failure_count)
        self.failure_count_spinbox.setFixedWidth(200)  # 统一宽度200px，确保完整显示
        layout.addWidget(label, row, 0)
        layout.addWidget(self.failure_count_spinbox, row, 1)
        row += 1

        # 自动切换模拟器
        label = QLabel("自动切换模拟器:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")
        self.auto_switch_switch = ModernToggleSwitch()
        # 从配置加载初始值
        auto_switch = self.config_manager.get("monitoring.auto_switch", True)
        self.auto_switch_switch.set_checked(auto_switch)
        layout.addWidget(label, row, 0)
        layout.addWidget(self.auto_switch_switch, row, 1)
        row += 1

        # 失败计数重置设置
        label = QLabel("失败计数重置:")
        label.setStyleSheet("font-size: 13px; color: #333; font-weight: 500;")

        reset_widget = QWidget()
        reset_layout = QHBoxLayout(reset_widget)
        reset_layout.setContentsMargins(0, 0, 0, 0)
        reset_layout.setSpacing(15)

        # 手动重置按钮 - 使用样式管理器
        self.manual_reset_button = create_danger_button("手动重置所有失败计数", (180, 36))

        # 自动重置选项
        self.auto_reset_on_startup_cb = QCheckBox("重启程序时自动清零")
        # 从配置加载初始值
        auto_reset = self.config_manager.get("monitoring.auto_reset_on_startup", True)
        self.auto_reset_on_startup_cb.setChecked(auto_reset)

        reset_layout.addWidget(self.manual_reset_button)
        reset_layout.addWidget(self.auto_reset_on_startup_cb)
        reset_layout.addStretch()

        layout.addWidget(label, row, 0)
        layout.addWidget(reset_widget, row, 1)

        return failure_group

    def refresh_monitoring_status(self):
        """刷新监控状态 - 通过事件总线异步处理"""
        if self.event_bus:
            # 通过事件总线异步刷新监控状态
            self.event_bus.emit('monitoring.refresh_status', {
                'callback': self._on_monitoring_refresh_completed
            })
        else:
            # 降级处理：显示提示消息
            self._show_info_message("提示", "监控状态刷新功能需要事件总线支持")

    def test_exception_screenshot(self):
        """测试异常截图功能 - 通过事件总线异步处理"""
        if self.event_bus:
            # 通过事件总线异步执行测试截图
            screenshot_path = self.screenshot_path_input.text() if hasattr(self, 'screenshot_path_input') else "./screenshots/"
            self.event_bus.emit('screenshot.test_capture', {
                'path': screenshot_path,
                'callback': self._on_test_screenshot_completed
            })
        else:
            # 降级处理：显示提示消息
            self._show_info_message("提示", "测试截图功能需要事件总线支持")

    def _show_info_message(self, title, message):
        """显示信息消息 - 通过事件总线"""
        if self.event_bus:
            self.event_bus.emit('ui.show_message', {
                'type': 'info',
                'title': title,
                'message': message
            })

    def _on_monitoring_refresh_completed(self, result):
        """监控刷新完成回调"""
        if result.get('success', False):
            self._show_info_message("完成", "监控状态已刷新！")
        else:
            error_msg = result.get('error', '刷新失败')
            self._show_warning_message("错误", f"监控状态刷新失败: {error_msg}")

    def _on_test_screenshot_completed(self, result):
        """测试截图完成回调"""
        if result.get('success', False):
            screenshot_path = result.get('screenshot_path', '')
            self._show_info_message("完成", f"测试截图已保存: {screenshot_path}")
        else:
            error_msg = result.get('error', '截图失败')
            self._show_warning_message("错误", f"测试截图失败: {error_msg}")

    # 窗口排列页面
    def create_window_arrangement_page(self):
        """创建窗口排列配置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # 页面标题
        title_label = QLabel("窗口排列设置")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 自动排列开关
        auto_arrange_group = QGroupBox("自动排列")
        auto_arrange_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e8f5e8;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #f8fff8;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #f8fff8;
                color: #4CAF50;
            }
        """)
        auto_arrange_layout = QVBoxLayout(auto_arrange_group)
        auto_arrange_layout.setContentsMargins(20, 20, 20, 20)

        # 说明文字
        desc_label = QLabel("模拟器启动完成后自动排列窗口")
        desc_label.setStyleSheet("color: #666; font-size: 13px; margin-bottom: 10px;")
        auto_arrange_layout.addWidget(desc_label)

        # 开关控件
        switch_layout = QHBoxLayout()
        switch_layout.addWidget(QLabel("启用自动排列:"))
        self.auto_arrange_switch = ModernToggleSwitch()
        switch_layout.addWidget(self.auto_arrange_switch)
        switch_layout.addStretch()
        auto_arrange_layout.addLayout(switch_layout)

        layout.addWidget(auto_arrange_group)

        # 窗口参数设置
        params_group = QGroupBox("窗口参数")
        params_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #e3f2fd;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #f3f9ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #f3f9ff;
                color: #1976D2;
            }
        """)
        params_layout = QGridLayout(params_group)
        params_layout.setContentsMargins(20, 20, 20, 20)
        params_layout.setSpacing(15)

        # 说明文字
        desc_label = QLabel("配置窗口大小和排列参数")
        desc_label.setStyleSheet("color: #666; font-size: 13px; margin-bottom: 10px;")
        params_layout.addWidget(desc_label, 0, 0, 1, 2)

        # 窗口宽度
        params_layout.addWidget(QLabel("窗口宽度:"), 1, 0)
        self.window_width_spinbox = ModernSpinBox(min_val=0, max_val=3840, suffix=" px (0=默认)")
        self.window_width_spinbox.setValue(0)
        params_layout.addWidget(self.window_width_spinbox, 1, 1)

        # 窗口高度
        params_layout.addWidget(QLabel("窗口高度:"), 2, 0)
        self.window_height_spinbox = ModernSpinBox(min_val=0, max_val=2160, suffix=" px (0=默认)")
        self.window_height_spinbox.setValue(0)
        params_layout.addWidget(self.window_height_spinbox, 2, 1)

        # 列间距
        params_layout.addWidget(QLabel("列间距:"), 3, 0)
        self.column_spacing_spinbox = ModernSpinBox(min_val=0, max_val=100, suffix=" px")
        self.column_spacing_spinbox.setValue(0)
        params_layout.addWidget(self.column_spacing_spinbox, 3, 1)

        # 行间距
        params_layout.addWidget(QLabel("行间距:"), 4, 0)
        self.row_spacing_spinbox = ModernSpinBox(min_val=0, max_val=100, suffix=" px")
        self.row_spacing_spinbox.setValue(0)
        params_layout.addWidget(self.row_spacing_spinbox, 4, 1)

        # 每行数量
        params_layout.addWidget(QLabel("每行数量:"), 5, 0)
        self.windows_per_row_spinbox = ModernSpinBox(min_val=1, max_val=20, suffix=" 个")
        self.windows_per_row_spinbox.setValue(7)
        params_layout.addWidget(self.windows_per_row_spinbox, 5, 1)

        # 每列数量
        params_layout.addWidget(QLabel("每列数量:"), 6, 0)
        self.windows_per_column_spinbox = ModernSpinBox(min_val=1, max_val=10, suffix=" 个")
        self.windows_per_column_spinbox.setValue(2)
        params_layout.addWidget(self.windows_per_column_spinbox, 6, 1)

        # 排列延迟
        params_layout.addWidget(QLabel("排列延迟:"), 7, 0)
        self.arrange_delay_spinbox = ModernSpinBox(min_val=500, max_val=10000, suffix=" 毫秒")
        self.arrange_delay_spinbox.setValue(2000)
        params_layout.addWidget(self.arrange_delay_spinbox, 7, 1)
        layout.addWidget(params_group)

        # 🎯 使用说明信息区域
        info_group = QGroupBox("使用说明")
        info_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                border: 2px solid #fff3e0;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #fffbf5;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: #fffbf5;
                color: #ff9800;
            }
        """)
        info_layout = QVBoxLayout(info_group)
        info_layout.setContentsMargins(20, 20, 20, 20)

        info_text = QLabel("""• 窗口大小设为0将使用模拟器默认大小
• 排列延迟确保窗口完全加载后再排列
• 使用雷电官方sortWnd命令进行排列
• 支持单个和批量启动后自动排列""")
        info_text.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 13px;
                line-height: 1.6;
            }
        """)
        info_layout.addWidget(info_text)

        layout.addWidget(info_group)

        layout.addStretch()
        return page

    # ============================================================================
    # 🎯 5. 按钮事件处理模块
    # ============================================================================
    # 功能描述: 处理设置界面中各种按钮的点击事件和用户交互
    # 调用关系: 被UI控件的信号调用，处理用户操作和界面更新
    # 注意事项: 包含错误处理和用户反馈，确保操作的安全性和可靠性
    # ============================================================================

    def _on_refresh_clicked(self):
        """刷新按钮点击事件"""
        try:
            self.refresh_monitoring_status()
            # 更新状态卡片
            if hasattr(self, 'monitoring_status_card'):
                self.monitoring_status_card.update_value("刷新中...")

        except Exception as e:
            self.logger.error(f"刷新监控状态失败: {e}")

    def _on_test_screenshot_clicked(self):
        """测试截图按钮点击事件"""
        try:
            self.test_exception_screenshot()

        except Exception as e:
            self.logger.error(f"测试截图失败: {e}")
            self._show_info_message("错误", f"测试截图失败: {e}")

    def _on_browse_path_clicked(self):
        """浏览路径按钮点击事件"""
        try:
            from PyQt6.QtWidgets import QFileDialog

            # 获取当前路径
            current_path = ""
            if hasattr(self, 'screenshot_path_input'):
                current_path = self.screenshot_path_input.text()

            # 打开文件夹选择对话框
            folder_path = QFileDialog.getExistingDirectory(
                self,
                "选择截图保存路径",
                current_path,
                QFileDialog.Option.ShowDirsOnly
            )

            if folder_path:
                # 更新路径输入框
                if hasattr(self, 'screenshot_path_input'):
                    self.screenshot_path_input.setText(folder_path + "/")
                    # 触发设置变更
                    self.setting_changed.emit("screenshot_path", folder_path + "/")

        except Exception as e:
            self.logger.error(f"浏览路径失败: {e}")

    def _on_view_folder_clicked(self):
        """查看文件夹按钮点击事件"""
        try:
            import os
            import subprocess
            import sys

            # 获取当前截图路径
            screenshot_path = ""
            if hasattr(self, 'screenshot_path_input'):
                screenshot_path = self.screenshot_path_input.text()

            if not screenshot_path:
                screenshot_path = self.config_manager.get("monitoring.screenshot_path", "./screenshots/")

            # 确保路径存在
            from pathlib import Path
            path_obj = Path(screenshot_path)
            path_obj.mkdir(parents=True, exist_ok=True)

            # 在文件管理器中打开文件夹
            if os.name == 'nt':  # Windows
                os.startfile(str(path_obj))
            elif os.name == 'posix':  # macOS and Linux
                subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', str(path_obj)])

        except Exception as e:
            self.logger.error(f"打开文件夹失败: {e}")
            self._show_info_message("错误", f"打开文件夹失败: {e}")

    def _on_manual_reset_clicked(self):
        """手动重置按钮点击事件"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            # 确认对话框
            reply = QMessageBox.question(
                self,
                "确认重置",
                "确定要重置所有模拟器的失败计数吗？\n此操作不可撤销。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 重置所有失败计数
                from core.heartbeat_manager import get_simple_heartbeat_manager
                heartbeat_manager = get_simple_heartbeat_manager()

                # 重置所有模拟器的失败计数
                reset_count = 0
                for emulator_id, emulator_info in heartbeat_manager.monitored_emulators.items():
                    if emulator_info.get('failure_count', 0) > 0:
                        emulator_info['failure_count'] = 0
                        reset_count += 1

                self._show_info_message(
                    "重置完成",
                    f"已重置 {reset_count} 个模拟器的失败计数"
                )

                self.logger.info(f"手动重置了 {reset_count} 个模拟器的失败计数")

        except Exception as e:
            self.logger.error(f"手动重置失败计数失败: {e}")
            self._show_info_message("错误", f"重置失败: {e}")

    def _show_warning_message(self, title, message):
        """显示警告消息"""
        if self.event_bus:
            self.event_bus.emit('ui.show_message', {
                'type': 'warning',
                'title': title,
                'message': message
            })
        else:
            # 降级处理：直接显示消息框
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, title, message)

    def _on_window_arrangement_setting_changed(self, key: str, value):
        """窗口排列设置变更处理"""
        try:
            # 保存到配置
            success = self.config_manager.set(f"window_arrangement.{key}", value)

            if success:
                self.logger.debug(f"窗口排列设置已更新: {key} = {value}")

                # 延迟保存配置
                if hasattr(self, '_save_timer'):
                    self._save_timer.stop()
                else:
                    from PyQt6.QtCore import QTimer
                    self._save_timer = QTimer()
                    self._save_timer.setSingleShot(True)
                    self._save_timer.timeout.connect(self._delayed_save_config)

                # 延迟500ms保存，避免用户快速输入时频繁保存
                self._save_timer.start(500)

                # 发送信号通知其他组件
                self.setting_changed.emit(key, value)
            else:
                self.logger.error(f"保存窗口排列设置失败: {key} = {value}")

        except Exception as e:
            self.logger.error(f"处理窗口排列设置变更失败: {e}")

    def _delayed_save_config(self):
        """延迟保存配置"""
        try:
            self.config_manager.save()
            self.logger.debug("配置已保存")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
