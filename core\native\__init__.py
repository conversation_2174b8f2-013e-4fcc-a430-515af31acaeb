#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 雷电模拟器原生API统一封装模块
========================================
功能描述: 提供所有雷电模拟器原生操作的统一接口
模块结构: 基于参考代码重新实现，不依赖外部模块

主要组件:
1. LeiDianNativeAPI - 基础API统一封装
2. NativeScreenshotEngine - 专业截图功能
3. NativeVisionEngine - 图像识别功能  
4. NativeInteractionEngine - 交互操作功能

设计原则:
- 技术实现与业务逻辑分离
- 统一API接口，避免重复实现
- 基于ldconsole.exe命令行工具
- 完全不依赖参考代码，独立实现

调用关系: 被core层的各种管理器调用
注意事项: 所有实现基于参考代码逻辑，但完全独立编写
========================================
"""

# 导出主要API类
from .base_api import LeiDianNativeAPI
from .screenshot_engine import NativeScreenshotEngine

# 导出便捷函数
from .base_api import get_native_api

__all__ = [
    'LeiDianNativeAPI',
    'NativeScreenshotEngine',
    'get_native_api'
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'Enterprise Development Team'
__description__ = '雷电模拟器原生API统一封装模块'
