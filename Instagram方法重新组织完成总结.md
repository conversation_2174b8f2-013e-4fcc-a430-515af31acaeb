   # ------------------------------------------------------------------------
    # 🎯 4.3.2 Instagram页面状态检测步骤（三次重启机制）
    # ------------------------------------------------------------------------
    # 功能描述: Instagram页面状态检测，实现三次重启机制和异常状态直接关闭
    # 调用关系: 被阶段三Instagram启动流程调用，确保应用可用状态
    # 注意事项: 启动超时最多重试3次，异常状态直接关闭模拟器不重试
    # 技术实现: 基于TEXT文本检测，支持中英文双语识别
    #
    # 执行步骤:
    # 第1步: 循环检测Instagram启动状态 (最多3次尝试)
    #        - 每次尝试40秒超时检测
    #        - 调用_detect_with_timeout()进行单次检测
    # 第2步: 判断检测结果并分类处理
    #        - "正常-在主页面" → 启动成功，返回继续任务
    #        - "正常-启动加载中"/"异常-Instagram启动超时" → 重启Instagram重试
    #        - 其他异常状态 → 直接关闭模拟器，任务终止
    # 第3步: 重启Instagram (仅限启动超时情况)
    #        - 调用_restart_instagram()方法
    #        - 继续下一次尝试
    # 第4步: 处理最终失败情况
    #        - 3次尝试都失败 → 关闭模拟器
    #        - 返回"任务终止-Instagram启动失败"
    # ------------------------------------------------------------------------

    async def _step_detect_instagram_page_status(self, debug_mode: bool = False) -> str:
        """4.3.2 Instagram页面状态检测（三次重启机制）"""

        MAX_STARTUP_ATTEMPTS = 3  # 最多3次启动尝试
        STARTUP_TIMEOUT = 40      # 每次40秒超时

        if debug_mode:
            log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 开始Instagram页面状态检测", component="InstagramDMTask")
            log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 最大尝试次数: {MAX_STARTUP_ATTEMPTS}, 每次超时: {STARTUP_TIMEOUT}秒", component="InstagramDMTask")

        ##########  第1步：循环检测Instagram启动状态 (最多3次尝试)  ##########
        for attempt in range(1, MAX_STARTUP_ATTEMPTS + 1):
            log_info(f"[模拟器{self.emulator_id}] Instagram启动检测 第{attempt}/{MAX_STARTUP_ATTEMPTS}次", component="InstagramDMTask")
               # ========================================================================
    # 🎯 第1步相关方法：循环检测Instagram启动状态
    # ========================================================================

    async def _detect_with_timeout(self, timeout_seconds: int, debug_mode: bool = False) -> str:
        """第1步：循环检测Instagram启动状态 - 单次检测（带超时控制）"""

        CHECK_INTERVAL = 2  # 每2秒检测一次
        start_time = time.time()

        if debug_mode:
            log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 开始单次检测，超时时间: {timeout_seconds}秒", component="InstagramDMTask")

        # 循环检测页面状态（每2秒一次，直到超时或检测到结果）
        while time.time() - start_time < timeout_seconds:
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 开始页面状态检测", component="InstagramDMTask")

            status = await self._detect_page_by_text_final(debug_mode=debug_mode)

            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 页面状态检测结果: {status}", component="InstagramDMTask")

            if status != "正常-启动加载中":
                if debug_mode:
                    log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 检测到非加载状态，返回结果: {status}", component="InstagramDMTask")
                return status

            await asyncio.sleep(CHECK_INTERVAL)
            elapsed = time.time() - start_time
            log_info(f"[模拟器{self.emulator_id}] Instagram启动检测中... 已等待{elapsed:.1f}秒", component="InstagramDMTask")

        # 检测超时处理
        if debug_mode:
            log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 检测超时，返回超时状态", component="InstagramDMTask")
        return "异常-Instagram启动超时"

    async def _detect_page_by_text_final(self, debug_mode: bool = False) -> str:
        """第1步：循环检测Instagram启动状态 - 核心页面状态检测"""

        try:
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 开始基于TEXT的页面状态检测", component="InstagramDMTask")

            # 获取UI层次结构XML数据
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 开始获取UI层次结构", component="InstagramDMTask")

            success, _ = self.ld.execute_ld(self.emulator_id, "uiautomator dump /sdcard/ui.xml")
            if not success:
                if debug_mode:
                    log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - uiautomator dump失败，返回启动加载中", component="InstagramDMTask")
                return "正常-启动加载中"

            success, xml_content = self.ld.execute_ld(self.emulator_id, "cat /sdcard/ui.xml")
            if not success or not xml_content:
                if debug_mode:
                    log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 读取UI XML失败，返回启动加载中", component="InstagramDMTask")
                return "正常-启动加载中"

            # 解析XML并提取所有文本节点
            import xml.etree.ElementTree as ET
            try:
                root = ET.fromstring(xml_content)
                all_texts = []

                for elem in root.iter():
                    text = elem.get('text', '').strip()
                    if text:
                        all_texts.append(text)

                if debug_mode:
                    log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 解析XML成功，找到{len(all_texts)}个文本节点", component="InstagramDMTask")

                if not all_texts:
                    if debug_mode:
                        log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 未找到任何文本节点，返回启动加载中", component="InstagramDMTask")
                    return "正常-启动加载中"

                page_text = " ".join(all_texts)

            except Exception as e:
                if debug_mode:
                    log_error(f"[模拟器{self.emulator_id}] 🔍 调试信息 - XML解析异常: {e}", component="InstagramDMTask")
                return "正常-启动加载中"



            # 优先检测主页状态（正常状态）
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 找到{len(all_texts)}个文本节点", component="InstagramDMTask")
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 页面文本前100字符: {page_text[:100]}...", component="InstagramDMTask")

            homepage_texts = ["快拍", "动态", "搜索", "Reels", "个人资料"]
            found_homepage_texts = [text for text in homepage_texts if text in page_text]

            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 主页文本检测: 找到{len(found_homepage_texts)}个关键词: {found_homepage_texts}", component="InstagramDMTask")

            if found_homepage_texts:
                if debug_mode:
                    log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 开始验证主页元素", component="InstagramDMTask")

                if self._verify_homepage_elements(debug_mode=debug_mode):
                    if debug_mode:
                        log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 主页元素验证成功，返回正常-在主页面", component="InstagramDMTask")
                    return "正常-在主页面"
                else:
                    if debug_mode:
                        log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 主页元素验证失败，继续其他检测", component="InstagramDMTask")

            # 检测各种异常状态（一旦检测到立即返回）
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 开始检测异常状态", component="InstagramDMTask")

            # 检测登录异常
            login_texts = ["账号、邮箱或手机号", "密码", "登录", "忘记密码", "创建新账户"]
            found_login_texts = [text for text in login_texts if text in page_text]
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 登录异常检测: 找到{len(found_login_texts)}个关键词: {found_login_texts}", component="InstagramDMTask")
            if found_login_texts:
                return "异常-需要登录"

            # 检测注册引导异常
            signup_texts = ["加入Instagram", "立即开始", "我已有账户", "和志趣相投的人分享"]
            found_signup_texts = [text for text in signup_texts if text in page_text]
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 注册异常检测: 找到{len(found_signup_texts)}个关键词: {found_signup_texts}", component="InstagramDMTask")
            if found_signup_texts:
                return "异常-需要注册"

            # 检测账号申诉异常
            challenge_texts = ["我们已暂时停用", "申诉", "为什么会发生这种情况", "身份验证", "继续"]
            found_challenge_texts = [text for text in challenge_texts if text in page_text]
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 申诉异常检测: 找到{len(found_challenge_texts)}个关键词: {found_challenge_texts}", component="InstagramDMTask")
            if found_challenge_texts:
                return "异常-账号申诉"

            # 检测应用崩溃异常
            crash_texts = ["反复停止运行", "应用信息", "关闭应用", "强制停止"]
            found_crash_texts = [text for text in crash_texts if text in page_text]
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 崩溃异常检测: 找到{len(found_crash_texts)}个关键词: {found_crash_texts}", component="InstagramDMTask")
            if found_crash_texts:
                return "异常-应用崩溃"

            # 检测网络异常
            network_texts = ["网络连接", "无法连接", "重试", "网络错误"]
            found_network_texts = [text for text in network_texts if text in page_text]
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 网络异常检测: 找到{len(found_network_texts)}个关键词: {found_network_texts}", component="InstagramDMTask")
            if found_network_texts:
                return "异常-网络异常"

            # 检测更新异常
            update_texts = ["更新Instagram", "新版本", "立即更新"]
            found_update_texts = [text for text in update_texts if text in page_text]
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 更新异常检测: 找到{len(found_update_texts)}个关键词: {found_update_texts}", component="InstagramDMTask")
            if found_update_texts:
                return "异常-需要更新"

            # 检查Activity状态（判断是否在Instagram应用内）
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 开始Activity检测", component="InstagramDMTask")

            current_activity = self.ld.get_activity_name()
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 当前Activity: {current_activity}", component="InstagramDMTask")
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 是否包含instagram: {current_activity and 'instagram' in current_activity.lower()}", component="InstagramDMTask")

            if current_activity and "instagram" in current_activity.lower():
                if debug_mode:
                    log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 在Instagram应用内，返回启动加载中", component="InstagramDMTask")
                return "正常-启动加载中"
            else:
                if debug_mode:
                    log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 不在Instagram应用内，返回启动失败", component="InstagramDMTask")
                return "异常-Instagram启动失败"

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 页面检测异常: {e}", component="InstagramDMTask")
            return "异常-检测失败"

    def _verify_homepage_elements(self, debug_mode: bool = False) -> bool:
        """第1步：循环检测Instagram启动状态 - 主页元素验证"""
        try:
            if debug_mode:
                log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 开始验证主页元素", component="InstagramDMTask")

            # 检查是否有主页特有的resource_id
            homepage_ids = [
                "com.instagram.android:id/feed_tab",
                "com.instagram.android:id/tab_icon",
                "com.instagram.android:id/action_bar_title"
            ]

            for i, resource_id in enumerate(homepage_ids):
                element = self.ld.find_node(self.emulator_id, resource_id=resource_id)
                if debug_mode:
                    log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 主页元素{i+1}检测 ({resource_id}): {bool(element)}", component="InstagramDMTask")

                if element:
                    if debug_mode:
                        log_info(f"[模拟器{self.emulator_id}] 🔍 调试信息 - 找到主页元素，验证成功", component="InstagramDMTask")
                    return True

            return False
        except:
            return False

            status = await self._detect_with_timeout(STARTUP_TIMEOUT, debug_mode=debug_mode)

            ##########  第2步：判断检测结果并分类处理  ##########
            if status == "正常-在主页面":
                return status
            elif status == "正常-启动加载中" or status == "异常-Instagram启动超时":
                ##########  第3步：重启Instagram (仅限启动超时情况)  ##########
                if attempt < MAX_STARTUP_ATTEMPTS:
                    log_info(f"[模拟器{self.emulator_id}] 第{attempt}次启动超时，重启Instagram...", component="InstagramDMTask")
                    await self._restart_instagram()
                    continue
                        # ========================================================================
    # 🎯 第3步相关方法：重启Instagram (仅限启动超时情况)
    # ========================================================================

    async def _restart_instagram(self):
        """第3步：重启Instagram - 重启操作实现"""

        try:
            log_info(f"[模拟器{self.emulator_id}] 重启Instagram...", component="InstagramDMTask")

            # 1. 强制结束进程
            self.ld.killApp(self.emulator_id, "com.instagram.android")
            await asyncio.sleep(3)

            # 2. 清理可能的系统弹窗
            await self._clear_system_dialogs()

            # 3. 重新启动
            self.ld.launchApp(self.emulator_id, "com.instagram.android")
            await asyncio.sleep(5)

            log_info(f"[模拟器{self.emulator_id}] Instagram重启完成", component="InstagramDMTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] Instagram重启失败: {e}", component="InstagramDMTask")

    async def _clear_system_dialogs(self):
        """第3步：重启Instagram - 清理系统弹窗"""
        try:
            # 清理可能的崩溃弹窗
            dialog_buttons = ["确定", "关闭", "OK", "Close"]
            for button_text in dialog_buttons:
                button = self.ld.find_node(self.emulator_id, text=button_text)
                if button:
                    self.ld.click_node(button)
                    await asyncio.sleep(1)
        except:
            pass
                else:
                    ##########  第4步：处理最终失败情况  ##########
                    log_error(f"[模拟器{self.emulator_id}] Instagram三次启动均失败", component="InstagramDMTask")
                    await self._close_emulator("Instagram启动失败")
                    return "任务终止-Instagram启动失败"
            else:
                # 检测到异常状态，直接关闭模拟器
                log_error(f"[模拟器{self.emulator_id}] 检测到异常状态: {status}", component="InstagramDMTask")
                await self._close_emulator(status)
                return f"任务终止-{status}"

        # 理论上不会到这里
        await self._close_emulator("Instagram启动失败")
        return "任务终止-Instagram启动失败"

 



    # ========================================================================
    # 🎯 第4步相关方法：处理最终失败情况
    # ========================================================================

    async def _close_emulator(self, reason: str):
        """第4步：处理最终失败情况 - 关闭模拟器并记录原因"""

        try:
            log_info(f"[模拟器{self.emulator_id}] 任务终止原因: {reason}", component="InstagramDMTask")
            log_info(f"[模拟器{self.emulator_id}] 开始关闭模拟器...", component="InstagramDMTask")

            # 直接关闭模拟器
            self.ld.quit(self.emulator_id)
            log_info(f"[模拟器{self.emulator_id}] 模拟器已关闭", component="InstagramDMTask")

            # 更新任务状态
            self._update_task_status("失败", reason)

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 关闭模拟器失败: {e}", component="InstagramDMTask")

    def _update_task_status(self, status: str, reason: str):
        """第4步：处理最终失败情况 - 更新任务状态"""
        # 这里可以更新数据库或发送状态通知
        log_info(f"[模拟器{self.emulator_id}] 任务状态更新: {status} - {reason}", component="InstagramDMTask")