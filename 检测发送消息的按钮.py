#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟器1发送按钮测试脚本
========================================
功能描述: 专门测试模拟器1的Instagram发送按钮检测和功能
用途: 调试发送按钮相关的UI元素和点击功能
创建时间: 2025-01-18
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from test_emulator1_instagram_dm_flow import InstagramDMFlowTester
from core.logger_manager import log_info, log_error


async def main():
    """主测试函数 - 专门测试发送按钮"""
    try:
        log_info("=" * 60)
        log_info("模拟器1 Instagram发送按钮测试")
        log_info("=" * 60)
        
        # 创建测试器实例
        tester = InstagramDMFlowTester()
        
        # 设置测试环境
        log_info("[测试] 正在设置测试环境...")
        if not await tester.setup_test_environment():
            log_error("[测试] 测试环境设置失败")
            return
            
        log_info("[测试] ✅ 测试环境设置完成")
        
        # 执行发送按钮测试
        log_info("[测试] 开始执行发送按钮测试...")
        result = await tester.test_send_button_detection()
        
        # 打印测试结果
        print("\n" + "=" * 60)
        print("发送按钮测试结果")
        print("=" * 60)
        
        if result:
            print("✅ 发送按钮测试成功")
            print("   - 发送按钮可以正常检测")
            print("   - UI元素定位正确")
        else:
            print("❌ 发送按钮测试失败")
            print("   - 可能是resource-id已改变")
            print("   - 请查看日志中的调试信息")
            print("   - 检查生成的XML文件")
        
        # 清理测试环境
        await tester.cleanup_test_environment()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        # 提供后续建议
        print("\n📋 后续建议:")
        print("1. 查看生成的 debug_send_button_ui_emulator1.xml 文件")
        print("2. 检查日志中的发送相关元素列表")
        print("3. 如果发送按钮未找到，可能需要更新resource-id")
        print("4. 确保当前在Instagram私信输入界面")
        
    except Exception as e:
        log_error(f"发送按钮测试异常: {e}")
        print(f"\n❌ 测试执行异常: {e}")


if __name__ == "__main__":
    print("🚀 启动模拟器1发送按钮测试...")
    asyncio.run(main())
