#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 雷电模拟器专业截图引擎
========================================
功能描述: 专门负责雷电模拟器的截图功能
实现方式: 基于参考代码逻辑，使用多种截图技术

主要功能:
1. 绑定句柄截图 - 基于list2获取的绑定句柄
2. DWM API截图 - 使用Windows桌面窗口管理器
3. ImageGrab备选 - 屏幕区域截取备选方案
4. 多重容错机制 - 确保截图成功率

技术实现:
- 优先使用DWM API的PrintWindow方法
- 失败时自动降级到ImageGrab方法
- 支持后台截图，不需要窗口可见
- 完整的错误处理和资源管理

调用关系: 被screenshot_manager等管理器调用
注意事项: 基于参考代码逻辑但完全独立实现
========================================
"""

import asyncio
import ctypes
from ctypes import wintypes, windll
from datetime import datetime
from pathlib import Path
from typing import Optional
from PIL import Image, ImageGrab
import logging

from .base_api import LeiDianNativeAPI
from ..logger_manager import log_info, log_error


class NativeScreenshotEngine:
    """雷电模拟器专业截图引擎"""
    
    def __init__(self, emulator_path: str = None, screenshot_dir: str = None):
        """
        初始化截图引擎
        
        Args:
            emulator_path: 模拟器路径
            screenshot_dir: 截图保存目录
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化基础API
        self.base_api = LeiDianNativeAPI(emulator_path)
        
        # 设置截图保存目录
        self.screenshot_dir = Path(screenshot_dir) if screenshot_dir else Path("./screenshots")
        self.screenshot_dir.mkdir(exist_ok=True)
        
        log_info("专业截图引擎初始化成功", component="NativeScreenshotEngine")
    
    async def capture_emulator_screenshot(self, emulator_id: int, output_file: str = None) -> Optional[str]:
        """
        截取指定模拟器的截图 - 完整流程
        
        Args:
            emulator_id: 模拟器ID
            output_file: 输出文件路径，如果为None则自动生成
            
        Returns:
            截图文件路径，失败时返回None
        """
        try:
            log_info(f"开始截取模拟器 {emulator_id} 的截图", component="NativeScreenshotEngine")
            
            # 1. 获取绑定句柄
            hwnd_info = await self.base_api.get_hwnd(emulator_id)
            if not hwnd_info:
                log_error(f"无法获取模拟器 {emulator_id} 的窗口句柄", component="NativeScreenshotEngine")
                return None
            
            top_hwnd, bind_hwnd = hwnd_info
            
            if bind_hwnd <= 0:
                log_error(f"模拟器 {emulator_id} 绑定句柄无效: {bind_hwnd}", component="NativeScreenshotEngine")
                return None
            
            # 2. 生成输出文件路径
            if not output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"emulator_{emulator_id}_screenshot_{timestamp}.png"
                output_file = self.screenshot_dir / filename
            
            # 3. 执行截图
            result = self.capture_by_hwnd(bind_hwnd, str(output_file))
            
            if result:
                log_info(f"模拟器 {emulator_id} 截图成功: {result}", component="NativeScreenshotEngine")
                return result
            else:
                log_error(f"模拟器 {emulator_id} 截图失败", component="NativeScreenshotEngine")
                return None
                
        except Exception as e:
            log_error(f"截取模拟器 {emulator_id} 截图异常: {e}", component="NativeScreenshotEngine")
            return None
    
    def capture_by_hwnd(self, hwnd: int, output_file: str) -> Optional[str]:
        """
        通过窗口句柄截图 - 多重容错机制
        
        Args:
            hwnd: 窗口句柄
            output_file: 输出文件路径
            
        Returns:
            截图文件路径，失败时返回None
        """
        try:
            # 方法1: 尝试DWM API截图
            result = self._capture_with_dwm_api(hwnd, output_file)
            if result:
                return result
            
            # 方法2: 降级到ImageGrab截图
            result = self._capture_with_imagegrab(hwnd, output_file)
            if result:
                return result
            
            return None
            
        except Exception as e:
            log_error(f"窗口句柄 {hwnd} 截图异常: {e}", component="NativeScreenshotEngine")
            return None
    
    def _capture_with_dwm_api(self, hwnd: int, output_file: str) -> Optional[str]:
        """
        使用DWM API截图 - 主要方法
        
        Args:
            hwnd: 窗口句柄
            output_file: 输出文件路径
            
        Returns:
            截图文件路径，失败时返回None
        """
        try:
            user32 = windll.user32
            gdi32 = windll.gdi32
            
            # 获取窗口尺寸
            rect = wintypes.RECT()
            if not user32.GetWindowRect(hwnd, ctypes.byref(rect)):
                return None
            
            width = rect.right - rect.left
            height = rect.bottom - rect.top
            
            if width <= 0 or height <= 0:
                return None
            
            # 获取桌面DC
            desktop_dc = user32.GetDC(0)
            if not desktop_dc:
                return None
            
            # 创建兼容DC
            mem_dc = gdi32.CreateCompatibleDC(desktop_dc)
            if not mem_dc:
                user32.ReleaseDC(0, desktop_dc)
                return None
            
            # 创建位图
            bitmap = gdi32.CreateCompatibleBitmap(desktop_dc, width, height)
            if not bitmap:
                gdi32.DeleteDC(mem_dc)
                user32.ReleaseDC(0, desktop_dc)
                return None
            
            # 选择位图到DC
            old_bitmap = gdi32.SelectObject(mem_dc, bitmap)
            
            # 使用PrintWindow进行截图
            result = user32.PrintWindow(hwnd, mem_dc, 0x00000002)  # PW_RENDERFULLCONTENT
            
            if not result:
                # 清理资源
                gdi32.SelectObject(mem_dc, old_bitmap)
                gdi32.DeleteObject(bitmap)
                gdi32.DeleteDC(mem_dc)
                user32.ReleaseDC(0, desktop_dc)
                return None
            
            # 创建BITMAPINFO结构
            class BITMAPINFOHEADER(ctypes.Structure):
                _fields_ = [
                    ('biSize', wintypes.DWORD),
                    ('biWidth', wintypes.LONG),
                    ('biHeight', wintypes.LONG),
                    ('biPlanes', wintypes.WORD),
                    ('biBitCount', wintypes.WORD),
                    ('biCompression', wintypes.DWORD),
                    ('biSizeImage', wintypes.DWORD),
                    ('biXPelsPerMeter', wintypes.LONG),
                    ('biYPelsPerMeter', wintypes.LONG),
                    ('biClrUsed', wintypes.DWORD),
                    ('biClrImportant', wintypes.DWORD)
                ]
            
            bmi = BITMAPINFOHEADER()
            bmi.biSize = ctypes.sizeof(BITMAPINFOHEADER)
            bmi.biWidth = width
            bmi.biHeight = -height  # 负值表示从上到下
            bmi.biPlanes = 1
            bmi.biBitCount = 32
            bmi.biCompression = 0  # BI_RGB
            
            # 创建缓冲区
            buffer_size = width * height * 4
            buffer = ctypes.create_string_buffer(buffer_size)
            
            # 获取位图数据
            lines = gdi32.GetDIBits(mem_dc, bitmap, 0, height, buffer, ctypes.byref(bmi), 0)
            
            if not lines:
                # 清理资源
                gdi32.SelectObject(mem_dc, old_bitmap)
                gdi32.DeleteObject(bitmap)
                gdi32.DeleteDC(mem_dc)
                user32.ReleaseDC(0, desktop_dc)
                return None
            
            # 转换为PIL图像
            img = Image.frombuffer('RGBA', (width, height), buffer.raw, 'raw', 'BGRA', 0, 1)
            img = img.convert('RGB')  # 转换为RGB用于图色识别
            
            # 保存截图
            img.save(output_file)
            
            # 清理资源
            gdi32.SelectObject(mem_dc, old_bitmap)
            gdi32.DeleteObject(bitmap)
            gdi32.DeleteDC(mem_dc)
            user32.ReleaseDC(0, desktop_dc)
            
            return output_file
            
        except Exception:
            return None

    def _capture_with_imagegrab(self, hwnd: int, output_file: str) -> Optional[str]:
        """
        使用ImageGrab截图 - 备选方法

        Args:
            hwnd: 窗口句柄
            output_file: 输出文件路径

        Returns:
            截图文件路径，失败时返回None
        """
        try:
            user32 = windll.user32

            # 获取窗口位置
            rect = wintypes.RECT()
            if not user32.GetWindowRect(hwnd, ctypes.byref(rect)):
                return None

            # 截取屏幕区域
            bbox = (rect.left, rect.top, rect.right, rect.bottom)
            img = ImageGrab.grab(bbox=bbox)

            # 保存截图
            img.save(output_file)

            return output_file

        except Exception:
            return None

    def is_screenshot_available(self, emulator_id: int) -> bool:
        """
        检查模拟器是否可以截图

        Args:
            emulator_id: 模拟器ID

        Returns:
            是否可以截图
        """
        try:
            # 检查模拟器是否运行
            is_running, is_android, _ = asyncio.run(self.base_api.is_running(emulator_id))

            if not is_running or not is_android:
                return False

            # 检查是否能获取窗口句柄
            hwnd_info = asyncio.run(self.base_api.get_hwnd(emulator_id))
            if not hwnd_info:
                return False

            _, bind_hwnd = hwnd_info
            return bind_hwnd > 0

        except Exception:
            return False

    def capture_with_fallback(self, emulator_id: int, output_file: str = None, max_retries: int = 3) -> Optional[str]:
        """
        带重试机制的截图方法

        Args:
            emulator_id: 模拟器ID
            output_file: 输出文件路径
            max_retries: 最大重试次数

        Returns:
            截图文件路径，失败时返回None
        """
        for attempt in range(max_retries):
            try:
                result = asyncio.run(self.capture_emulator_screenshot(emulator_id, output_file))
                if result:
                    return result

                if attempt < max_retries - 1:
                    log_info(f"截图重试 {attempt + 1}/{max_retries}", component="NativeScreenshotEngine")
                    # 短暂等待后重试
                    import time
                    time.sleep(1)

            except Exception as e:
                log_error(f"截图重试 {attempt + 1} 异常: {e}", component="NativeScreenshotEngine")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(1)

        return None


# ========================================================================
# 🎯 全局实例管理
# ========================================================================

_screenshot_engine_instance = None

def get_screenshot_engine(emulator_path: str = None, screenshot_dir: str = None) -> NativeScreenshotEngine:
    """
    获取截图引擎全局实例

    Args:
        emulator_path: 模拟器路径，仅在首次调用时有效
        screenshot_dir: 截图目录，仅在首次调用时有效

    Returns:
        NativeScreenshotEngine实例
    """
    global _screenshot_engine_instance

    if _screenshot_engine_instance is None:
        _screenshot_engine_instance = NativeScreenshotEngine(emulator_path, screenshot_dir)

    return _screenshot_engine_instance
