#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速模式验证测试
========================================
功能描述: 验证test_real_instagram_follow_flow.py的快速模式修改是否正确
创建时间: 2025-07-25
作者: AI Assistant
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入测试模块
from test_real_instagram_follow_flow import RealInstagramFollowTester
from core.logger_manager import log_info


async def verify_quick_mode():
    """验证快速模式配置"""
    try:
        print("🧪 验证快速模式配置")
        print("=" * 50)
        
        # 创建测试器实例
        tester = RealInstagramFollowTester(emulator_id=2)
        
        # 验证配置加载
        real_config = tester.load_real_config()
        if real_config:
            print("✅ 真实配置加载成功")
            print(f"   直接关注数量: {real_config.get('direct_follow_count')}")
            print(f"   粉丝关注数量: {real_config.get('fans_follow_count')}")
            print(f"   地区筛选: 泰国={real_config.get('thailand')}")
        else:
            print("❌ 真实配置加载失败")
            return False
        
        # 验证环境设置
        if await tester.setup_real_test_environment():
            print("✅ 测试环境设置成功")
        else:
            print("❌ 测试环境设置失败")
            return False
        
        # 验证Instagram任务实例
        if tester.instagram_task:
            print("✅ Instagram任务实例创建成功")
            print(f"   模拟器ID: {tester.instagram_task.emulator_id}")
            print(f"   粉丝关注数量: {tester.instagram_task.fans_follow_count}")
        else:
            print("❌ Instagram任务实例创建失败")
            return False
        
        # 清理环境
        await tester.cleanup_real_test_environment()
        
        return True
        
    except Exception as e:
        print(f"❌ 验证异常: {e}")
        return False


async def simulate_quick_mode_flow():
    """模拟快速模式流程"""
    try:
        print(f"\n🚀 模拟快速模式流程")
        print("=" * 50)
        
        # 创建测试器实例
        tester = RealInstagramFollowTester(emulator_id=2)
        
        # 模拟快速模式的阶段跳过
        print("⚡ 快速模式：跳过阶段1-3")
        
        # 模拟前面阶段为成功状态
        tester.test_results["stage1"] = {
            "name": "环境验证与应用检测",
            "result": True,
            "message": "⚡ 快速模式跳过",
            "timestamp": "12:00:00"
        }
        tester.test_results["stage2"] = {
            "name": "V2Ray节点连接", 
            "result": True,
            "message": "⚡ 快速模式跳过",
            "timestamp": "12:00:01"
        }
        tester.test_results["stage3"] = {
            "name": "Instagram应用启动",
            "result": True, 
            "message": "⚡ 快速模式跳过",
            "timestamp": "12:00:02"
        }
        
        print("✅ 阶段1: 环境验证与应用检测 (跳过)")
        print("✅ 阶段2: V2Ray节点连接 (跳过)")
        print("✅ 阶段3: Instagram应用启动 (跳过)")
        print("🎯 阶段4: 关注粉丝业务流程 (准备执行)")
        
        # 验证测试结果结构
        print(f"\n📊 测试结果结构验证:")
        for stage_id, stage_info in tester.test_results.items():
            print(f"  {stage_id}: {stage_info['name']} - {stage_info['message']}")
        
        # 模拟报告生成
        success_count = 3  # 前面3个阶段算作成功
        total_stages = 4   # 总共4个阶段
        
        print(f"\n📈 快速模式统计:")
        print(f"  跳过阶段: 3个")
        print(f"  执行阶段: 1个")
        print(f"  总阶段数: {total_stages}")
        print(f"  当前成功数: {success_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟异常: {e}")
        return False


async def main():
    """主测试函数"""
    try:
        print("快速模式验证测试工具")
        print("=" * 60)
        
        # 验证快速模式配置
        config_success = await verify_quick_mode()
        
        # 模拟快速模式流程
        flow_success = await simulate_quick_mode_flow()
        
        print(f"\n{'='*60}")
        print(f"验证结果:")
        print(f"  配置验证: {'✅ 通过' if config_success else '❌ 失败'}")
        print(f"  流程模拟: {'✅ 通过' if flow_success else '❌ 失败'}")
        
        overall_success = config_success and flow_success
        print(f"  总体结果: {'✅ 快速模式就绪' if overall_success else '❌ 需要修复'}")
        print(f"{'='*60}")
        
        if overall_success:
            print(f"\n🎉 快速模式修改成功！")
            print(f"✅ test_real_instagram_follow_flow.py 已配置为快速模式")
            print(f"✅ 将跳过阶段1-3，直接从阶段4开始")
            print(f"✅ 节省测试时间，专注于关注粉丝核心功能")
            print(f"\n💡 使用方法:")
            print(f"   python test_real_instagram_follow_flow.py")
        else:
            print(f"\n🔧 需要检查:")
            print(f"1. 确认配置文件app_config.json存在")
            print(f"2. 验证模拟器连接状态")
            print(f"3. 检查Instagram任务模块")
        
    except Exception as e:
        print(f"❌ 主程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
