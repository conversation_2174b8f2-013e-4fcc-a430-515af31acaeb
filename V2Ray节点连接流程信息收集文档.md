# V2Ray节点连接流程信息收集文档

## 📋 文档说明

本文档详细列出了实现V2Ray节点连接自动化流程所需要收集的UI元素信息。请按照每个步骤的要求，提供相应的UI元素详细信息。

## 🛠️ 信息收集工具

### 推荐使用uiautomator2获取元素信息：

```python
import uiautomator2 as u2

# 连接雷电模拟器
d = u2.connect("127.0.0.1:5557")  # 请根据实际端口调整

# 获取当前界面所有元素
print(d.dump_hierarchy())

# 查找特定元素
elements = d(resourceId="com.v2ray.ang:id/tv_test_state")
for elem in elements:
    print(f"Resource-ID: {elem.resource_id}")
    print(f"Class: {elem.class_name}")
    print(f"Text: {elem.text}")
    print(f"Bounds: {elem.bounds}")
    print(f"Clickable: {elem.clickable}")
    print(f"Enabled: {elem.enabled}")
    print("---")
```

## 📱 需要收集的信息格式

每个UI元素请提供以下信息：
```
resource-id: [资源ID]
class: [控件类型]
text: [显示文本]
bounds: [坐标位置]
clickable: [是否可点击]
enabled: [是否启用]
visible-to-user: [是否可见]
package: [包名]
```

---

## 步骤5：检查节点列表状态

### 🎯 目标
判断V2Ray应用中是否已有节点配置，决定后续流程走向。V2Ray主界面的：`activity: com.v2ray.ang/com.v2ray.ang.ui.MainActivity`

### 📋 需要提供的信息

#### 5.1 V2Ray主界面（有节点时）
**场景描述**: V2Ray应用已导入节点，主界面显示节点列表

**需要的元素信息**:
**判断是否有节点**
   - 包含所有节点的容器元素
   - 可能的resource-id: `com.v2ray.ang:id/info_container` 此ID识别是否有节点，，没有则进行步骤6以后面的流程
5.2 连接V2Ray节点
- **检查连接状态**: `resource-id: com.v2ray.ang:id/tv_test_state，text: 未连接`未连接的状态
- **点击连接按钮**: `resource-id: com.v2ray.ang:id/layout_edit，` (如果未连接，点击此元素)
- **等待连接**: 检查状态变为"已连接，点击测试连接"
- **检查已连接**：`resource-id: com.v2ray.ang:id/tv_test_state，text: 已连接，点击测试连接`已连接的状态


5.3. 测试节点延迟
- **点击测试**: `resource-id: com.v2ray.ang:id/tv_test_state，text: 已连接，点击测试连接` 已连接的状态点击此元素进行测速，，需要点击此元素的位置
- **验证结果**: 等待状态显示"成功"
- **点击测试后会出现三种情况‘测试中’，‘成功’，‘失败’**点击出现‘测试中’，就等待直到出现‘成功’或‘失败’，，如果出现‘成功’则等待2秒延迟，进行阶段三的步骤，如果出现‘失败’进行**重试机制**: 最多5次ping测试，失败则更换节点，，更换节点为步骤7开始
- **测试中**：`resource-id: com.v2ray.ang:id/tv_test_state，text: 测试中…`
- **失败**：`resource-id: com.v2ray.ang:id/tv_test_state，text: 失败：: net/http: request canceled (Client.Timeout exceeded while awaiting headers)`失败的元素，，注意‘text: 失败’只要包含text: 失败，就判断为失败，
- **成功**`resource-id: com.v2ray.ang:id/tv_test_state，text: 连接成功：延时 225 毫秒`成功的元素，，注意只要包含text: 连接成功，就判断为成功，，text: 连接成功：延时 225 毫秒，，text: 连接成功：延时 123 毫秒，，text: 连接成功：延时 321 毫秒，，text: 连接成功：延时 456 毫秒，，text: 连接成功：
- **重试机制**: 最多5次ping测试，失败则更换节点


#### 5.2 V2Ray主界面（无节点时）
**场景描述**: V2Ray应用刚安装或清空了所有节点配置

**需要的元素信息**:
1. **空状态提示**
  com.v2ray.ang:id/info_container，，没有识别到此元素就说明无节点

---

## 步骤6：导入V2Ray节点订阅

### 🎯 目标
当检测到无节点时，自动导入订阅链接获取节点配置。

### 📋 需要提供的信息

#### 6.1 点击添加按钮
**场景描述**: 主界面右上角的添加按钮

**需要的元素信息**:
1. **"+"号图标**
   - `content-desc: 添加配置`
   - class: `class: android.widget.TextView`
   - 图标: 通常是"+"号图标

**请提供**: FAB按钮的详细元素信息和精确坐标

#### 6.2 添加菜单选项
**场景描述**: 点击"+"号图标按钮后弹出的菜单选项

**需要的元素信息**:
1. **从剪贴板导入选项**
   `resource-id: com.v2ray.ang:id/title`
   `text: 从剪贴板导入`


**请提供**: 弹出菜单中所有选项的元素信息

#### 6.3 三点菜单
**场景描述**: 主界面右上角的更多选项菜单

**需要的元素信息**:
1. **三点菜单按钮**
   - `class: android.widget.ImageView`
   - `content-desc: 更多选项`
   - 位置: 右上角

2. **更新订阅选项**
   `resource-id: com.v2ray.ang:id/title`
   `class: android.widget.TextView`
   `text: 更新订阅`

**请提供**: 三点菜单按钮和展开后选项的元素信息

---

## 步骤7：随机选择节点

### 🎯 目标
从导入的节点列表中随机选择一个节点进行连接。

### 📋 需要提供的信息

#### 7.1 节点列表界面
**场景描述**: 显示所有可用节点的列表界面

**需要的元素信息**:
1. **节点列表容器**
   - 可滚动的列表容器
   - 可能的class: `class: android.widget.LinearLayout`

2. **节点项布局**
   - 单个节点的完整布局结构
   - 包含节点名称、延迟、状态等信息

3. **节点名称元素**
   - 显示节点名称的文本元素
   - `resource-id: com.v2ray.ang:id/info_container`
   

4. **节点延迟元素**
   - 显示延迟信息的元素
   - 可能显示: "123ms", "超时", "未测试" 等
  **检查连接状态**: `resource-id: com.v2ray.ang:id/tv_test_state，text: 未连接`未连接的状态
- **检查已连接**：`resource-id: com.v2ray.ang:id/tv_test_state，text: 已连接`已连接的状态
**请提供**: 节点列表的完整UI结构和单个节点项的详细信息

#### 7.2 节点选择交互
**场景描述**: 点击节点项进行选择的交互方式

**需要的元素信息**:
1. **可点击区域**
   - 节点项的可点击范围
   - 是整个节点项还是特定区域
   -   - `resource-id: com.v2ray.ang:id/info_container`-

2. **选中状态指示**
   - 节点被选中后的视觉反馈
   - 有背景色变化，但是好像无法通过元素获取，可以参考代码中是如何实现的参考的代码中是否有此判断机制

**请提供**: 节点选择前后的UI状态对比

---

## 步骤8：连接V2Ray节点

### 🎯 目标
启动选中节点的连接，建立VPN连接。

### 📋 需要提供的信息

#### 8.1 连接状态元素
**场景描述**: 显示当前连接状态的UI元素

**需要的元素信息**:
1. **状态文本元素**（已提供部分信息）
   - resource-id: `com.v2ray.ang:id/tv_test_state`
   - 未连接状态文本: `text: 未连接`
   - 失败状态文本: 没有失败的状态
   - 连接中状态文本: 没有连接中的状态
   - 已连接状态文本: `text: 已连接，点击测试连接`

**请提供**: 所有连接状态下的文本内容变化

#### 8.2 连接控制按钮
**场景描述**: 用于启动/停止连接的按钮

**需要的元素信息**:
1. **连接按钮**
`resource-id: com.v2ray.ang:id/layout_edit`点击此元素连接
   - 可能是FAB按钮或其他按钮
   - 未连接时的图标/文本
   - 已连接时的图标/文本

2. **按钮状态变化**
   - 连接前后按钮的视觉变化
   - 图标、颜色、文本的变化，，连接后又颜色的变换变成绿色，但是resource-id没有变换
   - 可以检测连接状态文本
   - resource-id: `com.v2ray.ang:id/tv_test_state`
   - 未连接状态文本: `text: 未连接`
   - 已连接状态文本: `text: 已连接，点击测试连接`

**请提供**: 连接按钮在不同状态下的元素信息

---

## 步骤9：测试节点延迟

### 🎯 目标
测试已连接节点的网络延迟，验证连接质量。

### 📋 需要提供的信息

#### 9.1 延迟测试触发
**场景描述**: 如何启动延迟测试功能

**需要的元素信息**:
1. **测试触发方式**
   - 是否点击状态文本触发测试
   - 是否有专门的测试按钮
   - 是否通过菜单选项触发

2. **测试按钮/区域**
   - 如果有专门按钮，提供其元素信息
   - 可点击区域的精确坐标

**请提供**: 延迟测试的具体触发方式和相关元素

#### 9.2 测试结果显示
**场景描述**: 延迟测试过程和结果的UI反馈

**需要的元素信息**:
1. **测试中状态**
   - 测试进行时的文本显示
   - 可能的文本: "测试中...", "Testing...", "Ping..."

2. **测试成功结果**
   - 显示具体延迟数值
   - 可能的格式: "123ms", "45ms", "网络良好"

3. **测试失败结果**
   - 测试失败时的显示
   - 可能的文本: "超时", "失败", "网络错误"

   **点击测试**: `resource-id: com.v2ray.ang:id/tv_test_state，text: 已连接，点击测试连接` 已连接的状态点击此元素进行测速，，需要点击此元素的位置
- **验证结果**: 等待状态显示"成功"
- **点击测试后会出现三种情况‘测试中’，‘成功’，‘失败’**点击出现‘测试中’，就等待直到出现‘成功’或‘失败’，，如果出现‘成功’则等待2秒延迟，进行阶段三的步骤，如果出现‘失败’进行**重试机制**: 最多5次ping测试，失败则更换节点，，更换节点为步骤7开始
- **测试中**：`resource-id: com.v2ray.ang:id/tv_test_state，text: 测试中…`
- **失败**：`resource-id: com.v2ray.ang:id/tv_test_state，text: 失败：: net/http: request canceled (Client.Timeout exceeded while awaiting headers)`失败的元素，，注意‘text: 失败’只要包含text: 失败，就判断为失败，
- **成功**`resource-id: com.v2ray.ang:id/tv_test_state，text: 连接成功：延时 225 毫秒`成功的元素，，注意只要包含text: 连接成功，就判断为成功，，text: 连接成功：延时 225 毫秒，，text: 连接成功：延时 123 毫秒，，text: 连接成功：延时 321 毫秒，，text: 连接成功：延时 456 毫秒，，text: 连接成功：
- **重试机制**: 最多5次ping测试，失败则更换节点

**请提供**: 测试过程中所有可能的状态文本和UI变化



---

## 📋 信息提交清单

请按以下顺序提供信息，每次提供一个场景的完整信息：

### ✅ 第一批：基础状态信息
- [ ] 步骤5.1：V2Ray主界面（有节点时）
- [ ] 步骤5.2：V2Ray主界面（无节点时）

### ✅ 第二批：导入流程信息  
- [ ] 步骤6.1：添加按钮（FAB）
- [ ] 步骤6.2：添加菜单选项
- [ ] 步骤6.3：三点菜单

### ✅ 第三批：节点操作信息
- [ ] 步骤7.1：节点列表界面
- [ ] 步骤7.2：节点选择交互

### ✅ 第四批：连接测试信息
- [ ] 步骤8.1：连接状态元素
- [ ] 步骤8.2：连接控制按钮
- [ ] 步骤9.1：延迟测试触发
- [ ] 步骤9.2：测试结果显示

## 📝 补充说明

1. **坐标信息**: 请提供精确的bounds坐标，用于点击操作
2. **文本内容**: 请提供所有可能的文本变化，包括中英文版本
3. **状态变化**: 请详细描述UI元素在不同状态下的变化
4. **特殊情况**: 如有网络错误、权限问题等异常状态，也请一并提供

## 🔄 信息更新

如果V2Ray应用版本更新导致UI变化，请及时更新相应的元素信息。

---

**开始收集**: 请从"第一批：基础状态信息"开始，逐步提供所需信息。
