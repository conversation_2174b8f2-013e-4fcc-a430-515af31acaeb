#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 强制性开发入口工具 - 每次开发前必须运行
========================================
功能描述: 强制性开发入口，确保每次开发都遵循企业级标准
主要功能: 
- 强制检查开发标准文档（DEVELOPMENT_GUIDE.md, DEV_PLAN.md）
- 验证开发环境和质量工具
- 确认遵循企业级标准
- 自动启动实时监控
- 记录开发会话
调用关系: 每次开发前必须运行的入口工具
注意事项: 零容忍政策 - 必须通过所有检查才能开始开发
文件位置: start_development.py
========================================
"""


import sys
import subprocess
import argparse


from pathlib import Path
from typing import List, Optional, Tuple
from datetime import datetime
import json
import signal

# ========================================
# 🎯 开发标准配置
# ========================================

# 必需的开发标准文档
REQUIRED_DOCS = {
    'README.md': '项目总览和用户指南',
    'DEV_PLAN.md': '开发计划和路线图',
    'DEVELOPMENT_GUIDE.md': '完整开发指南'
}

# 企业级标准清单
ENTERPRISE_STANDARDS = [
    "🚨 UI界面零卡顿 - UI响应时间 < 16ms，绝对禁止UI线程阻塞",
    "📁 对应代码放在对应文件 - 严格遵循分层架构，禁止跨层调用",
    "🎯 结构清晰 - 所有文件都有清晰的功能分组注释",
    "📊 层级分明 - 方法按功能和调用关系组织",
    "🔧 易于维护 - 开发者可以快速定位和修改代码",
    "🚫 禁止只增不删 - 修改代码必须删除冗余，统一验证逻辑"
]

# 质量保障工具列表
QUALITY_TOOLS = [
    'ui_blocking_detector.py',
    'ui_blocking_ci_check.py',
    'check_code_cleanup.py',
    'check_function_grouping.py',
    'check_layer_compliance.py',
    'setup_mandatory_hooks.py',
    'git_quality_enforcer.py',
    'git_hooks_manager.py',
    'pre_development_check.py'
]

# ========================================
# 🚀 强制性开发入口核心类
# ========================================

class DevelopmentStarter:
    """强制性开发入口"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.task_description = ""
        self.session_id = None
        self.monitoring_processes = []
        
    def check_required_docs(self) -> bool:
        """检查必需的开发标准文档"""
        print("📚 检查开发标准文档...")
        
        missing_docs = []
        for doc_name, description in REQUIRED_DOCS.items():
            doc_path = self.project_root / doc_name
            if doc_path.exists():
                print(f"   ✅ {doc_name} - {description}")
            else:
                missing_docs.append(doc_name)
                print(f"   ❌ {doc_name} - {description} (缺失)")
        
        if missing_docs:
            print(f"\n🚫 缺少必需的开发标准文档: {', '.join(missing_docs)}")
            print("请先创建所有必需的开发标准文档后再开始开发")
            return False
        
        return True
    
    def verify_development_environment(self) -> bool:
        """验证开发环境"""
        print("\n🔧 验证开发环境...")
        
        pre_check_path = self.project_root / "质量保障工具" / "pre_development_check.py"
        
        if not pre_check_path.exists():
            print("❌ 开发前检查工具不存在")
            return False
        
        try:
            result = subprocess.run(
                ['python', str(pre_check_path)],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print("   ✅ 开发环境检查通过")
                return True
            else:
                print("   ❌ 开发环境检查失败")
                print(f"   错误: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ 运行开发环境检查失败: {e}")
            return False
    
    def check_quality_tools(self) -> bool:
        """检查质量保障工具"""
        print("\n🛠️ 检查质量保障工具...")
        
        tools_dir = self.project_root / "质量保障工具"
        if not tools_dir.exists():
            print("❌ 质量保障工具目录不存在")
            return False
        
        missing_tools = []
        for tool_name in QUALITY_TOOLS:
            tool_path = tools_dir / tool_name
            if tool_path.exists():
                print(f"   ✅ {tool_name}")
            else:
                missing_tools.append(tool_name)
                print(f"   ❌ {tool_name} (缺失)")
        
        if missing_tools:
            print(f"\n🚫 缺少质量保障工具: {', '.join(missing_tools)}")
            return False
        
        return True
    
    def verify_git_hooks(self) -> bool:
        """验证Git钩子状态"""
        print("\n🔗 验证Git钩子状态...")
        
        hooks_manager_path = self.project_root / "质量保障工具" / "git_hooks_manager.py"
        
        if not hooks_manager_path.exists():
            print("❌ Git钩子管理器不存在")
            return False
        
        try:
            result = subprocess.run(
                ['python', str(hooks_manager_path), '--check'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if "状态正常" in result.stdout or "已安装" in result.stdout or "已存在" in result.stdout or result.returncode == 0:
                print("   ✅ Git钩子状态正常")
                return True
            else:
                print("   ⚠️ Git钩子可能需要修复")
                # 尝试自动修复
                print("   🔧 尝试自动修复Git钩子...")
                repair_result = subprocess.run(
                    ['python', str(hooks_manager_path), '--repair-all'],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                if repair_result.returncode == 0:
                    print("   ✅ Git钩子修复成功")
                    return True
                else:
                    print("   ⚠️ Git钩子修复失败，但继续开发")
                    print("   💡 可以稍后手动运行: python 质量保障工具/git_hooks_manager.py --repair-all")
                    return True  # 允许继续开发
                
        except Exception as e:
            print(f"   ❌ 检查Git钩子失败: {e}")
            return False
    
    def confirm_enterprise_standards(self) -> bool:
        """确认遵循企业级标准"""
        print("\n📋 企业级开发标准确认")
        print("=" * 60)
        print("请确认您将遵循以下企业级标准：")
        print()
        
        for i, standard in enumerate(ENTERPRISE_STANDARDS, 1):
            print(f"{i}. {standard}")
        
        print("\n" + "=" * 60)
        
        while True:
            response = input("您是否确认遵循上述所有企业级标准？(y/n): ").strip().lower()
            if response in ['y', 'yes', '是', '确认']:
                print("✅ 已确认遵循企业级标准")
                return True
            elif response in ['n', 'no', '否', '不确认']:
                print("🚫 必须确认遵循企业级标准才能开始开发")
                return False
            else:
                print("请输入 y/yes/是/确认 或 n/no/否/不确认")
    
    def start_real_time_monitoring(self) -> bool:
        """启动实时监控"""
        print("\n🚀 启动实时监控...")
        
        tools_dir = self.project_root / "质量保障工具"
        
        # 启动UI阻塞监控
        ui_monitor_path = tools_dir / "ui_blocking_detector.py"
        if ui_monitor_path.exists():
            try:
                ui_process = subprocess.Popen(
                    ['python', str(ui_monitor_path), '--watch', '--interval', '10'],
                    cwd=self.project_root,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                self.monitoring_processes.append(('UI阻塞监控', ui_process))
                print("   ✅ UI阻塞实时监控已启动")
            except Exception as e:
                print(f"   ⚠️ 启动UI阻塞监控失败: {e}")
        
        # 启动功能分组监控
        grouping_monitor_path = tools_dir / "check_function_grouping.py"
        if grouping_monitor_path.exists():
            try:
                grouping_process = subprocess.Popen(
                    ['python', str(grouping_monitor_path), '--watch'],
                    cwd=self.project_root,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                self.monitoring_processes.append(('功能分组监控', grouping_process))
                print("   ✅ 功能分组实时监控已启动")
            except Exception as e:
                print(f"   ⚠️ 启动功能分组监控失败: {e}")
        
        if self.monitoring_processes:
            print(f"\n🎯 已启动 {len(self.monitoring_processes)} 个实时监控进程")
            return True
        else:
            print("⚠️ 未能启动任何监控进程")
            return False
    
    def create_development_session(self, task_description: str) -> str:
        """创建开发会话"""
        session_id = f"dev_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        session_data = {
            'session_id': session_id,
            'task_description': task_description,
            'start_time': datetime.now().isoformat(),
            'project_root': str(self.project_root),
            'standards_confirmed': True,
            'monitoring_active': len(self.monitoring_processes) > 0,
            'status': 'active'
        }
        
        # 保存会话信息
        sessions_dir = self.project_root / ".dev_sessions"
        sessions_dir.mkdir(exist_ok=True)
        
        session_file = sessions_dir / f"{session_id}.json"
        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📝 开发会话已创建: {session_id}")
        print(f"   任务: {task_description}")
        
        return session_id
    
    def stop_monitoring(self) -> None:
        """停止监控进程"""
        print("\n🛑 停止实时监控...")
        
        for name, process in self.monitoring_processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"   ✅ 已停止 {name}")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"   🔪 强制停止 {name}")
            except Exception as e:
                print(f"   ⚠️ 停止 {name} 失败: {e}")
        
        self.monitoring_processes.clear()
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n\n🛑 收到停止信号，正在清理...")
        self.stop_monitoring()
        
        # 更新会话状态
        if self.session_id:
            sessions_dir = self.project_root / ".dev_sessions"
            session_file = sessions_dir / f"{self.session_id}.json"
            
            if session_file.exists():
                try:
                    with open(session_file, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                    
                    session_data['end_time'] = datetime.now().isoformat()
                    session_data['status'] = 'completed'
                    
                    with open(session_file, 'w', encoding='utf-8') as f:
                        json.dump(session_data, f, indent=2, ensure_ascii=False)
                    
                    print(f"📝 开发会话已结束: {self.session_id}")
                except Exception as e:
                    print(f"⚠️ 更新会话状态失败: {e}")
        
        print("👋 开发会话结束，感谢遵循企业级标准！")
        sys.exit(0)
    
    def start_development(self, task_description: str) -> bool:
        """启动开发流程"""
        self.task_description = task_description
        
        print("🚀 企业级开发流程启动")
        print("=" * 60)
        print(f"📋 开发任务: {task_description}")
        print("=" * 60)
        
        # 执行所有检查
        checks = [
            ("开发标准文档", self.check_required_docs),
            ("开发环境", self.verify_development_environment),
            ("质量保障工具", self.check_quality_tools),
            ("Git钩子", self.verify_git_hooks),
            ("企业级标准确认", self.confirm_enterprise_standards),
        ]
        
        for check_name, check_func in checks:
            print(f"\n🔍 {check_name}检查...")
            if not check_func():
                print(f"\n🚫 {check_name}检查失败，无法开始开发")
                print("请解决上述问题后重新运行此命令")
                return False
        
        # 启动实时监控
        if not self.start_real_time_monitoring():
            print("\n⚠️ 实时监控启动失败，但可以继续开发")
        
        # 创建开发会话
        self.session_id = self.create_development_session(task_description)
        
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # 显示开发指导
        self.show_development_guidance()
        
        # 保持运行状态
        self.keep_running()
        
        return True
    
    def show_development_guidance(self) -> None:
        """显示开发指导"""
        print("\n" + "=" * 60)
        print("🎯 开发指导")
        print("=" * 60)
        print("✅ 所有检查已通过，现在可以开始开发")
        print("✅ 实时监控已启动，会自动检测质量问题")
        print("✅ Git钩子已配置，提交/推送时会自动执行质量检查")
        print()
        print("📋 开发过程中请遵循：")
        for standard in ENTERPRISE_STANDARDS:
            print(f"   • {standard}")
        print()
        print("🔧 常用命令：")
        print("   • git commit  # 自动执行: UI阻塞检测 + 代码清理 + 功能分组检查")
        print("   • git push    # 自动执行: 分层架构检查 + UI阻塞CI检查")
        print()
        print("🛑 结束开发：按 Ctrl+C 停止监控并结束会话")
        print("=" * 60)
    
    def keep_running(self) -> None:
        """保持运行状态"""
        try:
            while True:
                # 删除伪异步处理 - 使用真正的异步机制
                pass  # 让Qt事件循环自然处理
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)

# ========================================
# 🎯 命令行接口
# ========================================

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='强制性开发入口工具')
    parser.add_argument('task_description', help='开发任务描述')
    parser.add_argument('--project-root', type=str, default='.', help='项目根目录')
    
    args = parser.parse_args()
    
    if not args.task_description.strip():
        print("❌ 请提供开发任务描述")
        print("示例: python start_development.py \"实现模拟器管理功能\"")
        sys.exit(1)
    
    starter = DevelopmentStarter(args.project_root)
    
    if not starter.start_development(args.task_description):
        sys.exit(1)

if __name__ == "__main__":
    main()
