#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 UI层 - 用户界面层
========================================
功能描述: UI模块初始化文件，导出主要UI组件
主要组件: MainWindowV2, SettingsUI, styled_widgets
调用关系: 被主程序导入使用
注意事项: 保持导入的简洁性，只导出必要的组件
========================================
"""

# 导出主要UI组件
from .main_window_v2 import MainWindowV2
from .settings_ui import SettingsUI

# 导出常用的自定义控件
from .styled_widgets import (
    StyledButton, StyledLineEdit, GradientFrame,
    RoundedWidget, DonutWidget, NavButton, StatusCard,
    HeartbeatIndicator, ModernToggleSwitch, ModernSpinBox,
    StyleManager
)

__all__ = [
    'MainWindowV2',
    'SettingsUI',
    'StyledButton',
    'StyledLineEdit',
    'GradientFrame',
    'RoundedWidget',
    'DonutWidget',
    'NavButton',
    'StatusCard',
    'HeartbeatIndicator',
    'ModernToggleSwitch',
    'ModernSpinBox',
    'StyleManager'
]

__version__ = "2.0.0"
__author__ = "UI Development Team"
__description__ = "雷电模拟器中控系统UI界面模块"
