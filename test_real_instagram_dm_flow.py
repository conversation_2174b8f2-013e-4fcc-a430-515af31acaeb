#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Instagram私信真实流程测试文件
========================================
功能描述: 使用app_config.json真实参数测试Instagram私信流程
测试范围: 从个人主页开始的完整私信发送流程
创建时间: 2025-07-19
作者: AI Assistant

测试流程:
1. 步骤11: 导航到个人主页
2. 步骤12: 获取粉丝数量信息  
3. 步骤13: 打开粉丝列表
4. 步骤14: 初始化私信任务
5. 步骤15: 批量私信发送循环

注意事项:
- 使用app_config.json中的真实参数
- 模拟真实的Instagram私信发送场景
- 包含完整的错误处理和日志记录
"""

import asyncio
import sys
import json
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入核心模块
from core.instagram_task import InstagramDMTask
from core.simple_config import get_config_manager
from core.logger_manager import log_info, log_error, log_warning


class RealInstagramDMTester:
    """Instagram私信真实流程测试器"""

    def __init__(self, emulator_id: int = 2):
        """初始化测试器"""
        self.emulator_id = emulator_id
        self.config_manager = get_config_manager()
        self.instagram_task = None
        self.test_results = {}
        self.start_time = None
        
    def load_real_config(self) -> Dict[str, Any]:
        """加载app_config.json中的真实配置"""
        try:
            log_info(f"[真实测试器] 加载app_config.json真实配置")
            
            # 从配置管理器获取真实配置
            real_config = {
                "emulator_id": self.emulator_id,
                "message_count": self.config_manager.get("instagram_dm.message_count", 25),
                "delay_min": self.config_manager.get("instagram_dm.delay_min", 2),
                "delay_max": self.config_manager.get("instagram_dm.delay_max", 200),
                "message_delay": self.config_manager.get("instagram_dm.message_delay", 1000),
                "send_mode": self.config_manager.get("instagram_dm.send_mode", 1),
                "message_content_1": self.config_manager.get("instagram_dm.message_content_1", "Hello!"),
                "message_content_2": self.config_manager.get("instagram_dm.message_content_2", "Hi!"),
                "record_file_path": self.config_manager.get("instagram_dm.record_file_path", "sent_users.txt"),
                "recall_before_dm": self.config_manager.get("instagram_dm.recall_before_dm", False),
                "min_followers_count": self.config_manager.get("instagram_dm.min_followers_count", 100)
            }
            
            log_info(f"[真实测试器] 真实配置加载完成:")
            log_info(f"[真实测试器]   私信数量: {real_config['message_count']}")
            log_info(f"[真实测试器]   延迟范围: {real_config['delay_min']}-{real_config['delay_max']}ms")
            log_info(f"[真实测试器]   消息延迟: {real_config['message_delay']}ms")
            log_info(f"[真实测试器]   发送模式: {real_config['send_mode']}")
            log_info(f"[真实测试器]   私信内容1: {real_config['message_content_1'][:50]}...")
            
            return real_config
            
        except Exception as e:
            log_error(f"[真实测试器] 加载真实配置失败: {e}")
            return {}

    async def setup_real_test_environment(self) -> bool:
        """设置真实测试环境"""
        try:
            log_info(f"[真实测试器] 开始设置真实测试环境")
            
            # 1. 创建Instagram任务实例（使用真实配置管理器）
            self.instagram_task = InstagramDMTask(self.emulator_id, self.config_manager)
            
            # 2. 验证模拟器状态
            if not await self._verify_emulator_ready():
                log_error(f"[真实测试器] 模拟器{self.emulator_id}未就绪")
                return False
                
            # 3. 验证雷电API
            if not self.instagram_task.is_ld_available():
                log_error(f"[真实测试器] 雷电API不可用")
                return False
            
            log_info(f"[真实测试器] ✅ 真实测试环境设置完成")
            return True
            
        except Exception as e:
            log_error(f"[真实测试器] 设置真实测试环境异常: {e}")
            return False

    async def _verify_emulator_ready(self) -> bool:
        """验证模拟器就绪状态"""
        try:
            log_info(f"[真实测试器] 验证模拟器{self.emulator_id}就绪状态")
            
            # 检查雷电API是否可用
            if self.instagram_task.ld is None:
                log_error(f"[真实测试器] 雷电API未初始化")
                return False
                
            # 检查模拟器是否运行
            is_running, is_android, _ = self.instagram_task.ld.is_running(self.emulator_id)
            if not is_running or not is_android:
                log_error(f"[真实测试器] 模拟器{self.emulator_id}状态异常: 运行={is_running}, Android={is_android}")
                return False
                
            log_info(f"[真实测试器] ✅ 模拟器{self.emulator_id}就绪")
            return True
            
        except Exception as e:
            log_error(f"[真实测试器] 验证模拟器状态异常: {e}")
            return False

    async def test_step_11_navigate_to_profile(self) -> bool:
        """测试步骤11: 导航到个人主页"""
        try:
            log_info(f"[真实测试器] 开始测试步骤11: 导航到个人主页")
            
            result = await self.instagram_task._step_navigate_to_profile()
            self.test_results["step_11"] = {
                "name": "导航到个人主页",
                "result": result,
                "message": "成功" if result else "失败",
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            
            if result:
                log_info(f"[真实测试器] ✅ 步骤11测试通过")
            else:
                log_error(f"[真实测试器] ❌ 步骤11测试失败")
                
            return result
            
        except Exception as e:
            log_error(f"[真实测试器] 步骤11测试异常: {e}")
            self.test_results["step_11"] = {
                "name": "导航到个人主页",
                "result": False,
                "message": f"异常: {e}",
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            return False

    async def test_step_12_get_followers_count(self) -> bool:
        """测试步骤12: 获取粉丝数量信息"""
        try:
            log_info(f"[真实测试器] 开始测试步骤12: 获取粉丝数量信息")
            
            result = await self.instagram_task._step_get_followers_count()
            followers_count = getattr(self.instagram_task, 'followers_count', 0)
            
            self.test_results["step_12"] = {
                "name": "获取粉丝数量信息",
                "result": result,
                "message": f"成功，粉丝数: {followers_count}" if result else "失败",
                "followers_count": followers_count,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            
            if result:
                log_info(f"[真实测试器] ✅ 步骤12测试通过，粉丝数: {followers_count}")
            else:
                log_error(f"[真实测试器] ❌ 步骤12测试失败")
                
            return result
            
        except Exception as e:
            log_error(f"[真实测试器] 步骤12测试异常: {e}")
            self.test_results["step_12"] = {
                "name": "获取粉丝数量信息",
                "result": False,
                "message": f"异常: {e}",
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            return False

    async def test_step_13_open_followers_list(self) -> bool:
        """测试步骤13: 打开粉丝列表"""
        try:
            log_info(f"[真实测试器] 开始测试步骤13: 打开粉丝列表")
            
            result = await self.instagram_task._step_open_followers_list()
            self.test_results["step_13"] = {
                "name": "打开粉丝列表",
                "result": result,
                "message": "成功" if result else "失败",
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            
            if result:
                log_info(f"[真实测试器] ✅ 步骤13测试通过")
            else:
                log_error(f"[真实测试器] ❌ 步骤13测试失败")
                
            return result
            
        except Exception as e:
            log_error(f"[真实测试器] 步骤13测试异常: {e}")
            self.test_results["step_13"] = {
                "name": "打开粉丝列表",
                "result": False,
                "message": f"异常: {e}",
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            return False

    async def test_step_14_initialize_dm_task(self) -> bool:
        """测试步骤14: 初始化私信任务"""
        try:
            log_info(f"[真实测试器] 开始测试步骤14: 初始化私信任务")
            
            result = await self.instagram_task._step_initialize_dm_task()
            sent_users_count = len(getattr(self.instagram_task, 'sent_users', set()))
            
            self.test_results["step_14"] = {
                "name": "初始化私信任务",
                "result": result,
                "message": f"成功，已加载{sent_users_count}条去重记录" if result else "失败",
                "sent_users_count": sent_users_count,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            
            if result:
                log_info(f"[真实测试器] ✅ 步骤14测试通过，去重记录: {sent_users_count}条")
            else:
                log_error(f"[真实测试器] ❌ 步骤14测试失败")
                
            return result
            
        except Exception as e:
            log_error(f"[真实测试器] 步骤14测试异常: {e}")
            self.test_results["step_14"] = {
                "name": "初始化私信任务",
                "result": False,
                "message": f"异常: {e}",
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            return False

    async def test_step_15_send_mass_dm(self) -> bool:
        """测试步骤15: 批量私信发送循环（使用真实参数）"""
        try:
            log_info(f"[真实测试器] 开始测试步骤15: 批量私信发送循环")

            # 显示当前真实配置
            log_info(f"[真实测试器] 当前真实配置:")
            log_info(f"[真实测试器]   私信数量: {self.instagram_task.message_count}")
            log_info(f"[真实测试器]   延迟范围: {self.instagram_task.delay_min}-{self.instagram_task.delay_max}ms")
            log_info(f"[真实测试器]   消息延迟: {self.instagram_task.message_delay}ms")
            log_info(f"[真实测试器]   发送模式: {self.instagram_task.send_mode}")

            # 记录开始时间
            start_time = datetime.now()

            result = await self.instagram_task._step_business_send_mass_dm()

            # 记录结束时间和发送统计
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            sent_count = getattr(self.instagram_task, 'sent_count', 0)

            self.test_results["step_15"] = {
                "name": "批量私信发送循环",
                "result": result,
                "message": f"成功发送{sent_count}条私信，耗时{duration:.2f}秒" if result else "失败",
                "sent_count": sent_count,
                "duration": duration,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }

            if result:
                log_info(f"[真实测试器] ✅ 步骤15测试通过，发送{sent_count}条私信")
            else:
                log_error(f"[真实测试器] ❌ 步骤15测试失败")

            return result

        except Exception as e:
            log_error(f"[真实测试器] 步骤15测试异常: {e}")
            self.test_results["step_15"] = {
                "name": "批量私信发送循环",
                "result": False,
                "message": f"异常: {e}",
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            return False

    async def run_real_flow_test(self) -> Dict[str, Any]:
        """运行真实的Instagram私信流程测试"""
        try:
            log_info(f"[真实测试器] 开始运行真实Instagram私信流程测试")
            log_info(f"[真实测试器] 使用模拟器ID: {self.emulator_id}")

            self.start_time = datetime.now()

            # 加载真实配置
            real_config = self.load_real_config()
            if not real_config:
                return {"status": "failed", "message": "真实配置加载失败"}

            # 设置真实测试环境
            if not await self.setup_real_test_environment():
                return {"status": "failed", "message": "真实测试环境设置失败"}

            # 执行各个步骤测试（从个人主页开始）
            test_steps = [
                ("step_11", self.test_step_11_navigate_to_profile, "导航到个人主页"),
                ("step_12", self.test_step_12_get_followers_count, "获取粉丝数量信息"),
                ("step_13", self.test_step_13_open_followers_list, "打开粉丝列表"),
                ("step_14", self.test_step_14_initialize_dm_task, "初始化私信任务"),
                ("step_15", self.test_step_15_send_mass_dm, "批量私信发送循环")
            ]

            success_count = 0
            total_steps = len(test_steps)

            for step_name, test_func, step_desc in test_steps:
                log_info(f"[真实测试器] 执行测试步骤: {step_name} - {step_desc}")

                try:
                    result = await test_func()
                    if result:
                        success_count += 1
                        log_info(f"[真实测试器] ✅ {step_name} 测试成功")
                    else:
                        log_error(f"[真实测试器] ❌ {step_name} 测试失败")
                        # 关键步骤失败时的处理策略
                        if step_name in ["step_11", "step_12", "step_13"]:
                            log_warning(f"[真实测试器] 关键步骤失败，但继续执行后续测试")

                except Exception as e:
                    log_error(f"[真实测试器] {step_name} 测试异常: {e}")

                # 步骤间短暂延迟
                await asyncio.sleep(1)

            # 生成测试报告
            test_report = self._generate_real_test_report(success_count, total_steps, real_config)

            # 保存测试结果
            await self._save_real_test_results(test_report)

            log_info(f"[真实测试器] ✅ 真实流程测试完成，成功率: {success_count}/{total_steps}")
            return test_report

        except Exception as e:
            log_error(f"[真实测试器] 真实流程测试异常: {e}")
            return {"status": "failed", "message": f"测试异常: {e}"}

    def _generate_real_test_report(self, success_count: int, total_steps: int, real_config: Dict[str, Any]) -> Dict[str, Any]:
        """生成真实测试报告"""
        try:
            end_time = datetime.now()
            duration = (end_time - self.start_time).total_seconds() if self.start_time else 0
            success_rate = (success_count / total_steps) * 100 if total_steps > 0 else 0

            report = {
                "status": "completed",
                "test_type": "real_flow_test",
                "emulator_id": self.emulator_id,
                "test_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "duration": f"{duration:.2f}秒",
                "summary": {
                    "total_steps": total_steps,
                    "success_count": success_count,
                    "failed_count": total_steps - success_count,
                    "success_rate": f"{success_rate:.1f}%"
                },
                "step_results": self.test_results,
                "real_config_used": real_config,
                "final_stats": {
                    "sent_count": getattr(self.instagram_task, 'sent_count', 0) if self.instagram_task else 0,
                    "followers_count": getattr(self.instagram_task, 'followers_count', 0) if self.instagram_task else 0
                }
            }

            return report

        except Exception as e:
            log_error(f"[真实测试器] 生成真实测试报告异常: {e}")
            return {"status": "failed", "message": f"报告生成异常: {e}"}

    async def _save_real_test_results(self, report: Dict[str, Any]):
        """保存真实测试结果到文件"""
        try:
            # 创建测试结果目录
            results_dir = project_root / "test_results"
            results_dir.mkdir(exist_ok=True)

            # 生成结果文件名
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            result_file = results_dir / f"real_instagram_dm_test_emulator{self.emulator_id}_{timestamp}.json"

            # 保存结果
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            log_info(f"[真实测试器] 真实测试结果已保存: {result_file}")

        except Exception as e:
            log_error(f"[真实测试器] 保存真实测试结果异常: {e}")

    async def cleanup_real_test_environment(self):
        """清理真实测试环境"""
        try:
            log_info(f"[真实测试器] 开始清理真实测试环境")

            # 释放资源
            if self.instagram_task:
                # 注销配置观察者
                self.instagram_task.unregister_config_observer()

            log_info(f"[真实测试器] ✅ 真实测试环境清理完成")

        except Exception as e:
            log_error(f"[真实测试器] 清理真实测试环境异常: {e}")


async def main():
    """主测试函数"""
    try:
        log_info("=" * 80)
        log_info("Instagram私信真实流程测试 - 使用app_config.json真实参数")
        log_info("=" * 80)

        # 创建真实测试器实例
        tester = RealInstagramDMTester(emulator_id=2)  # 可以修改模拟器ID

        # 运行真实流程测试
        test_report = await tester.run_real_flow_test()

        # 打印测试结果摘要
        print("\n" + "=" * 80)
        print("真实流程测试结果摘要")
        print("=" * 80)
        print(f"测试类型: {test_report.get('test_type', 'N/A')}")
        print(f"模拟器ID: {test_report.get('emulator_id', 'N/A')}")
        print(f"测试时间: {test_report.get('test_time', 'N/A')}")
        print(f"测试耗时: {test_report.get('duration', 'N/A')}")
        print(f"测试状态: {test_report.get('status', 'N/A')}")

        if 'summary' in test_report:
            summary = test_report['summary']
            print(f"总步骤数: {summary.get('total_steps', 0)}")
            print(f"成功步骤: {summary.get('success_count', 0)}")
            print(f"失败步骤: {summary.get('failed_count', 0)}")
            print(f"成功率: {summary.get('success_rate', '0%')}")

        # 打印最终统计
        if 'final_stats' in test_report:
            stats = test_report['final_stats']
            print(f"\n最终统计:")
            print(f"发送私信数: {stats.get('sent_count', 0)}")
            print(f"粉丝总数: {stats.get('followers_count', 0)}")

        # 打印各步骤详细结果
        if 'step_results' in test_report:
            print("\n步骤详细结果:")
            print("-" * 60)
            for step_id, step_info in test_report['step_results'].items():
                status = "✅ 成功" if step_info.get('result') else "❌ 失败"
                timestamp = step_info.get('timestamp', 'N/A')
                print(f"{step_id}: {step_info.get('name', 'N/A')} - {status} ({timestamp})")
                print(f"    详情: {step_info.get('message', 'N/A')}")

        # 打印使用的真实配置
        if 'real_config_used' in test_report:
            print(f"\n使用的真实配置 (来自app_config.json):")
            print("-" * 60)
            config = test_report['real_config_used']
            print(f"私信数量: {config.get('message_count', 'N/A')}")
            print(f"延迟范围: {config.get('delay_min', 'N/A')}-{config.get('delay_max', 'N/A')}ms")
            print(f"消息延迟: {config.get('message_delay', 'N/A')}ms")
            print(f"发送模式: {config.get('send_mode', 'N/A')}")

        # 清理测试环境
        await tester.cleanup_real_test_environment()

        print("\n" + "=" * 80)
        print("真实流程测试完成")
        print("=" * 80)

    except Exception as e:
        log_error(f"主测试函数异常: {e}")
        print(f"\n❌ 测试执行异常: {e}")


def run_single_real_step_test(step_name: str, emulator_id: int = 2):
    """运行单个真实步骤测试的便捷函数"""
    async def single_step_main():
        try:
            log_info(f"=" * 80)
            log_info(f"Instagram真实单步骤测试 - {step_name} - 模拟器{emulator_id}")
            log_info(f"=" * 80)

            # 创建真实测试器实例
            tester = RealInstagramDMTester(emulator_id)

            # 设置真实测试环境
            if not await tester.setup_real_test_environment():
                print("❌ 真实测试环境设置失败")
                return

            # 执行指定步骤测试
            step_methods = {
                "step_11": tester.test_step_11_navigate_to_profile,
                "step_12": tester.test_step_12_get_followers_count,
                "step_13": tester.test_step_13_open_followers_list,
                "step_14": tester.test_step_14_initialize_dm_task,
                "step_15": tester.test_step_15_send_mass_dm
            }

            if step_name not in step_methods:
                print(f"❌ 未知的步骤名称: {step_name}")
                print(f"可用步骤: {list(step_methods.keys())}")
                return

            # 执行测试
            result = await step_methods[step_name]()

            # 打印结果
            print(f"\n{'='*60}")
            print(f"真实步骤 {step_name} 测试结果")
            print(f"{'='*60}")
            print(f"结果: {'✅ 成功' if result else '❌ 失败'}")

            if step_name in tester.test_results:
                step_info = tester.test_results[step_name]
                print(f"详情: {step_info.get('message', 'N/A')}")
                print(f"时间: {step_info.get('timestamp', 'N/A')}")

            # 清理测试环境
            await tester.cleanup_real_test_environment()

        except Exception as e:
            log_error(f"真实单步骤测试异常: {e}")
            print(f"\n❌ 测试执行异常: {e}")

    # 运行单步骤测试
    asyncio.run(single_step_main())


if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        # 单步骤测试模式
        step_name = sys.argv[1]
        emulator_id = int(sys.argv[2]) if len(sys.argv) > 2 else 2
        print(f"运行真实单步骤测试: {step_name} (模拟器{emulator_id})")
        run_single_real_step_test(step_name, emulator_id)
    else:
        # 完整流程测试模式
        print("运行真实完整流程测试")
        asyncio.run(main())
