#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
📊 开发会话管理器 - 管理开发会话和统计
========================================
功能描述: 管理开发会话的生命周期，提供开发统计和报告功能
主要功能: 
- 查看活跃的开发会话
- 生成开发统计报告
- 管理会话状态
- 清理过期会话
调用关系: 独立运行的管理工具，配合start_development.py使用
注意事项: 确保开发会话的正确记录和管理
文件位置: development_session_manager.py
========================================
"""

import json
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import psutil

# ========================================
# 🎯 会话管理配置
# ========================================

@dataclass
class DevelopmentSession:
    """开发会话数据结构"""
    session_id: str
    task_description: str
    start_time: datetime
    end_time: Optional[datetime]
    project_root: str
    standards_confirmed: bool
    monitoring_active: bool
    status: str  # 'active', 'completed', 'abandoned'
    duration_minutes: Optional[float] = None

# ========================================
# 📊 开发会话管理器核心类
# ========================================

class DevelopmentSessionManager:
    """开发会话管理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.sessions_dir = self.project_root / ".dev_sessions"
        self.sessions_dir.mkdir(exist_ok=True)
    
    def load_session(self, session_file: Path) -> Optional[DevelopmentSession]:
        """加载单个会话"""
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            start_time = datetime.fromisoformat(data['start_time'])
            end_time = None
            duration_minutes = None
            
            if data.get('end_time'):
                end_time = datetime.fromisoformat(data['end_time'])
                duration_minutes = (end_time - start_time).total_seconds() / 60
            
            return DevelopmentSession(
                session_id=data['session_id'],
                task_description=data['task_description'],
                start_time=start_time,
                end_time=end_time,
                project_root=data['project_root'],
                standards_confirmed=data.get('standards_confirmed', False),
                monitoring_active=data.get('monitoring_active', False),
                status=data.get('status', 'unknown'),
                duration_minutes=duration_minutes
            )
        except Exception as e:
            print(f"⚠️ 加载会话失败 {session_file}: {e}")
            return None
    
    def load_all_sessions(self) -> List[DevelopmentSession]:
        """加载所有会话"""
        sessions = []
        
        for session_file in self.sessions_dir.glob("dev_*.json"):
            session = self.load_session(session_file)
            if session:
                sessions.append(session)
        
        # 按开始时间排序
        sessions.sort(key=lambda s: s.start_time, reverse=True)
        return sessions
    
    def get_active_sessions(self) -> List[DevelopmentSession]:
        """获取活跃会话"""
        all_sessions = self.load_all_sessions()
        return [s for s in all_sessions if s.status == 'active']
    
    def check_process_running(self, session_id: str) -> bool:
        """检查会话进程是否还在运行"""
        try:
            # 查找包含session_id的Python进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and any('start_development.py' in arg for arg in cmdline):
                            return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return False
        except Exception:
            return False
    
    def update_session_status(self, session_id: str, status: str, end_time: Optional[datetime] = None) -> bool:
        """更新会话状态"""
        session_file = self.sessions_dir / f"{session_id}.json"
        
        if not session_file.exists():
            return False
        
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            data['status'] = status
            if end_time:
                data['end_time'] = end_time.isoformat()
            
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"⚠️ 更新会话状态失败: {e}")
            return False
    
    def cleanup_abandoned_sessions(self) -> int:
        """清理被遗弃的会话"""
        active_sessions = self.get_active_sessions()
        cleaned_count = 0
        
        for session in active_sessions:
            # 检查会话是否超过24小时未结束
            if datetime.now() - session.start_time > timedelta(hours=24):
                if not self.check_process_running(session.session_id):
                    # 标记为遗弃
                    self.update_session_status(session.session_id, 'abandoned', datetime.now())
                    cleaned_count += 1
                    print(f"🧹 清理遗弃会话: {session.session_id}")
        
        return cleaned_count
    
    def stop_all_sessions(self) -> int:
        """停止所有活跃会话"""
        active_sessions = self.get_active_sessions()
        stopped_count = 0
        
        for session in active_sessions:
            # 尝试优雅地停止进程
            try:
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] and 'python' in proc.info['name'].lower():
                            cmdline = proc.info['cmdline']
                            if cmdline and any('start_development.py' in arg for arg in cmdline):
                                proc.terminate()
                                proc.wait(timeout=5)
                                break
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                        continue
            except Exception:
                pass
            
            # 更新会话状态
            self.update_session_status(session.session_id, 'stopped', datetime.now())
            stopped_count += 1
            print(f"🛑 停止会话: {session.session_id}")
        
        return stopped_count
    
    def generate_session_report(self, days: int = 7) -> Dict:
        """生成会话报告"""
        all_sessions = self.load_all_sessions()
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # 过滤指定天数内的会话
        recent_sessions = [s for s in all_sessions if s.start_time >= cutoff_date]
        
        # 统计数据
        total_sessions = len(recent_sessions)
        completed_sessions = [s for s in recent_sessions if s.status == 'completed']
        active_sessions = [s for s in recent_sessions if s.status == 'active']
        abandoned_sessions = [s for s in recent_sessions if s.status == 'abandoned']
        
        # 计算总开发时间
        total_minutes = 0
        for session in completed_sessions:
            if session.duration_minutes:
                total_minutes += session.duration_minutes
        
        # 平均会话时长
        avg_duration = total_minutes / len(completed_sessions) if completed_sessions else 0
        
        # 按任务类型分组
        task_types = {}
        for session in recent_sessions:
            task_key = session.task_description[:20] + "..." if len(session.task_description) > 20 else session.task_description
            task_types[task_key] = task_types.get(task_key, 0) + 1
        
        report = {
            'period_days': days,
            'total_sessions': total_sessions,
            'completed_sessions': len(completed_sessions),
            'active_sessions': len(active_sessions),
            'abandoned_sessions': len(abandoned_sessions),
            'total_development_hours': total_minutes / 60,
            'average_session_minutes': avg_duration,
            'task_distribution': task_types,
            'sessions': [
                {
                    'session_id': s.session_id,
                    'task': s.task_description,
                    'start_time': s.start_time.strftime('%Y-%m-%d %H:%M'),
                    'duration_minutes': s.duration_minutes,
                    'status': s.status
                }
                for s in recent_sessions
            ]
        }
        
        return report
    
    def print_status(self) -> None:
        """打印当前状态"""
        print("📊 开发会话状态")
        print("=" * 50)
        
        # 清理遗弃的会话
        cleaned = self.cleanup_abandoned_sessions()
        if cleaned > 0:
            print(f"🧹 清理了 {cleaned} 个遗弃会话")
        
        # 显示活跃会话
        active_sessions = self.get_active_sessions()
        
        if active_sessions:
            print(f"\n🚀 活跃会话 ({len(active_sessions)}):")
            for session in active_sessions:
                duration = datetime.now() - session.start_time
                hours = int(duration.total_seconds() // 3600)
                minutes = int((duration.total_seconds() % 3600) // 60)
                
                print(f"\n📋 {session.session_id}")
                print(f"   任务: {session.task_description}")
                print(f"   开始: {session.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"   持续: {hours}小时{minutes}分钟")
                print(f"   监控: {'✅' if session.monitoring_active else '❌'}")
                
                # 检查进程状态
                if self.check_process_running(session.session_id):
                    print(f"   状态: 🟢 运行中")
                else:
                    print(f"   状态: 🔴 进程已停止")
        else:
            print("\n✅ 当前没有活跃的开发会话")
        
        # 显示最近完成的会话
        all_sessions = self.load_all_sessions()
        recent_completed = [s for s in all_sessions if s.status == 'completed'][:3]
        
        if recent_completed:
            print(f"\n📝 最近完成的会话:")
            for session in recent_completed:
                duration_str = f"{session.duration_minutes:.1f}分钟" if session.duration_minutes else "未知"
                print(f"   • {session.task_description[:30]}... ({duration_str})")
    
    def print_report(self, days: int = 7) -> None:
        """打印开发报告"""
        report = self.generate_session_report(days)
        
        print(f"📈 开发统计报告 (最近{days}天)")
        print("=" * 50)
        
        print(f"📊 会话统计:")
        print(f"   总会话数: {report['total_sessions']}")
        print(f"   已完成: {report['completed_sessions']}")
        print(f"   进行中: {report['active_sessions']}")
        print(f"   已遗弃: {report['abandoned_sessions']}")
        
        print(f"\n⏱️ 时间统计:")
        print(f"   总开发时间: {report['total_development_hours']:.1f} 小时")
        print(f"   平均会话时长: {report['average_session_minutes']:.1f} 分钟")
        
        if report['task_distribution']:
            print(f"\n📋 任务分布:")
            for task, count in sorted(report['task_distribution'].items(), key=lambda x: x[1], reverse=True):
                print(f"   {task}: {count} 次")
        
        if report['sessions']:
            print(f"\n📝 会话详情:")
            for session in report['sessions'][:10]:  # 显示最近10个
                status_icon = {'completed': '✅', 'active': '🚀', 'abandoned': '❌'}.get(session['status'], '❓')
                duration = f"{session['duration_minutes']:.1f}分" if session['duration_minutes'] else "进行中"
                print(f"   {status_icon} {session['start_time']} | {session['task'][:40]}... | {duration}")

# ========================================
# 🎯 命令行接口
# ========================================

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='开发会话管理器')
    parser.add_argument('--status', action='store_true', help='显示当前会话状态')
    parser.add_argument('--report', type=int, metavar='DAYS', help='生成指定天数的开发报告')
    parser.add_argument('--stop-all', action='store_true', help='停止所有活跃会话')
    parser.add_argument('--cleanup', action='store_true', help='清理遗弃的会话')
    parser.add_argument('--export', type=str, help='导出报告到JSON文件')
    parser.add_argument('--project-root', type=str, default='.', help='项目根目录')
    
    args = parser.parse_args()
    
    manager = DevelopmentSessionManager(args.project_root)
    
    if args.stop_all:
        # 停止所有会话
        stopped = manager.stop_all_sessions()
        print(f"🛑 已停止 {stopped} 个活跃会话")
    
    elif args.cleanup:
        # 清理遗弃会话
        cleaned = manager.cleanup_abandoned_sessions()
        print(f"🧹 已清理 {cleaned} 个遗弃会话")
    
    elif args.report is not None:
        # 生成报告
        manager.print_report(args.report)
        
        if args.export:
            report = manager.generate_session_report(args.report)
            with open(args.export, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"\n📄 报告已导出到: {args.export}")
    
    else:
        # 默认显示状态
        manager.print_status()

if __name__ == "__main__":
    main()
