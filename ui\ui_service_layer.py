#!/usr/bin/env python3
"""
🎯 UI服务层 - 修复架构违规
功能：
- UI层和业务层之间的中介
- 避免UI层直接导入core层组件
- 提供UI需要的业务功能接口
- 保持分层架构清晰
"""

from typing import Any, Dict, List, Optional
from PyQt6.QtGui import QColor

class UIServiceLayer:
    """🎯 UI服务层 - 业务逻辑和UI之间的中介"""
    
    def __init__(self, config_manager=None):
        self._config_manager = config_manager
        self._status_converter = None
        self._logger_manager = None
    
    @property
    def config_manager(self):
        """获取配置管理器"""
        if self._config_manager is None:
            from core.simple_config import get_config_manager
            self._config_manager = get_config_manager()
        return self._config_manager
    
    @property
    def status_converter(self):
        """获取状态转换器"""
        if self._status_converter is None:
            from core.status_converter import StatusConverter
            self._status_converter = StatusConverter
        return self._status_converter
    
    def log_emulator(self, message: str, **kwargs):
        """记录模拟器日志"""
        from core.logger_manager import log_emulator
        log_emulator(message, **kwargs)
    
    def log_performance(self, message: str, duration: float = None, **kwargs):
        """记录性能日志"""
        from core.logger_manager import log_performance
        log_performance(message, duration, **kwargs)
    
    def log_info(self, message: str, **kwargs):
        """记录信息日志"""
        from core.logger_manager import log_info
        log_info(message, **kwargs)
    
    def get_status_color(self, status: str) -> QColor:
        """获取状态颜色 - 委托给统一的状态转换器"""
        return self.status_converter.get_status_color(status)

    def get_task_activity_color(self, activity_status: str) -> QColor:
        """获取心跳状态颜色 - 委托给统一的状态转换器"""
        return self.status_converter.get_task_activity_color(activity_status)
    
    # 🎯 删除不存在的状态转换方法 - StatusConverter 不提供英中文转换
    # 系统统一使用中文状态，无需转换
    
    def get_config_value(self, key: str, default_value: Any = None) -> Any:
        """获取配置值"""
        return self.config_manager.get(key, default_value)
    
    def set_config_value(self, key: str, value: Any):
        """设置配置值"""
        self.config_manager.set(key, value)
    
    def save_config(self):
        """保存配置"""
        self.config_manager.save()
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config_manager.get_all_config()
    
    def create_timer_callback(self, callback_func):
        """创建定时器回调 - 避免UI层直接导入QtCore"""
        from PyQt6.QtCore import QTimer
        if not hasattr(self, '_timers'):
            self._timers = []
        
        timer = QTimer()
        timer.timeout.connect(callback_func)
        self._timers.append(timer)
        return timer
    
    def format_emulator_data_for_table(self, emulator_list: List[Dict[str, Any]]) -> List[List[str]]:
        """格式化模拟器数据供表格显示 - 委托给统一的状态转换器"""
        from core.status_converter import format_emulator_list_for_table
        return format_emulator_list_for_table(emulator_list)
    
    def validate_emulator_path(self, path: str) -> bool:
        """验证模拟器路径"""
        import os
        return os.path.exists(path) and os.path.isdir(path)
    
    def get_current_datetime(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def format_duration(self, start_time: str) -> str:
        """格式化持续时间"""
        try:
            from datetime import datetime
            start = datetime.fromisoformat(start_time)
            now = datetime.now()
            duration = now - start
            
            hours = duration.seconds // 3600
            minutes = (duration.seconds % 3600) // 60
            seconds = duration.seconds % 60
            
            if duration.days > 0:
                return f"{duration.days}天{hours}小时{minutes}分钟"
            elif hours > 0:
                return f"{hours}小时{minutes}分钟{seconds}秒"
            elif minutes > 0:
                return f"{minutes}分钟{seconds}秒"
            else:
                return f"{seconds}秒"
        except:
            return "未知"
    
    def show_message_box(self, parent, title: str, message: str, message_type: str = "information"):
        """显示消息框"""
        from PyQt6.QtWidgets import QMessageBox
        
        msg_box = QMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        
        if message_type == "warning":
            msg_box.setIcon(QMessageBox.Icon.Warning)
        elif message_type == "error":
            msg_box.setIcon(QMessageBox.Icon.Critical)
        elif message_type == "question":
            msg_box.setIcon(QMessageBox.Icon.Question)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        else:
            msg_box.setIcon(QMessageBox.Icon.Information)
        
        return msg_box.exec()
    
    # 🎯 删除伪异步处理方法 - 使用信号机制替代processEvents()
    # 如需处理事件，应使用Qt信号/槽机制或QTimer

    # ============================================================================
    # 🎯 Instagram私信任务相关服务方法
    # ============================================================================
    # 功能描述: 提供Instagram私信任务相关的UI服务，包括状态更新、颜色管理等
    # 调用关系: 被Instagram私信UI和主窗口调用
    # 注意事项: 使用事件驱动更新，确保UI响应性
    # 架构合规: UI服务层，不包含业务逻辑，只负责UI相关服务
    # ============================================================================

    def get_instagram_dm_status_color(self, status: str):
        """获取Instagram私信任务状态颜色 - 委托给StatusConverter"""
        try:
            from core.status_converter import StatusConverter
            return StatusConverter.get_instagram_dm_status_color(status)
        except Exception as e:
            self.logger.error(f"获取Instagram私信状态颜色失败: {e}")
            from PyQt6.QtGui import QColor
            return QColor("#333333")

    def update_instagram_dm_status(self, emulator_index: int, status: str):
        """更新Instagram私信任务状态"""
        try:
            self.logger.debug(f"更新模拟器{emulator_index}的Instagram私信状态: {status}")
            # 发送状态更新信号，使用事件驱动更新UI
            self.status_updated.emit("instagram_dm_status", {
                "emulator_index": emulator_index,
                "status": status,
                "timestamp": self.get_current_datetime()
            })
        except Exception as e:
            self.logger.error(f"更新Instagram私信任务状态失败: {e}")

    def log_instagram_dm_event(self, emulator_index: int, event: str, details: str = ""):
        """记录Instagram私信事件日志"""
        try:
            log_message = f"[模拟器{emulator_index}] Instagram私信: {event}"
            if details:
                log_message += f" - {details}"

            self.logger.info(log_message)

            # 发送日志更新信号
            self.log_updated.emit("instagram_dm", {
                "emulator_index": emulator_index,
                "event": event,
                "details": details,
                "message": log_message,
                "timestamp": self.get_current_datetime()
            })
        except Exception as e:
            self.logger.error(f"记录Instagram私信事件日志失败: {e}")

    def update_instagram_dm_progress(self, emulator_index: int, sent: int, target: int, success: int):
        """更新Instagram私信进度"""
        try:
            from core.status_converter import InstagramDMStatus

            # 格式化状态文本
            if sent < target:
                status = InstagramDMStatus.format_running(sent, target)
            else:
                status = InstagramDMStatus.format_completed(success, target)

            # 更新状态
            self.update_instagram_dm_status(emulator_index, status)

            # 记录进度日志
            self.log_instagram_dm_event(
                emulator_index,
                "进度更新",
                f"已发送{sent}/{target}，成功{success}"
            )

        except Exception as e:
            self.logger.error(f"更新Instagram私信进度失败: {e}")

    def handle_instagram_dm_error(self, emulator_index: int, error_msg: str):
        """处理Instagram私信错误"""
        try:
            from core.status_converter import InstagramDMStatus

            # 格式化错误状态
            status = InstagramDMStatus.format_error(error_msg)

            # 更新状态
            self.update_instagram_dm_status(emulator_index, status)

            # 记录错误日志
            self.log_instagram_dm_event(emulator_index, "任务异常", error_msg)

        except Exception as e:
            self.logger.error(f"处理Instagram私信错误失败: {e}")

# 🎯 全局UI服务层实例
_ui_service_layer = None

def get_ui_service_layer(config_manager=None) -> UIServiceLayer:
    """获取全局UI服务层实例"""
    global _ui_service_layer
    if _ui_service_layer is None:
        _ui_service_layer = UIServiceLayer(config_manager)
    return _ui_service_layer
