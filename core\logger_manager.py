#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 简化日志系统 - 统一日志管理
========================================
功能描述: 使用Python标准logging，简化日志操作
主要方法: setup_logging(), get_logger(), log_info(), log_error()
调用关系: 替代复杂的企业级日志系统
注意事项: 
- 使用Python标准logging模块
- 统一日志格式和输出
- 简单的文件和控制台输出
========================================
"""

import logging
import sys
import re
from pathlib import Path
from PyQt6.QtCore import QObject, pyqtSignal
# 删除未使用的导入


class SimpleLogger(QObject):
    """简化的日志管理器 - 支持事件驱动的日志更新"""

    # 🎯 事件驱动信号 - 专用于表格运行日志列实时更新
    log_updated = pyqtSignal(str, str, str, int)  # component, level, message, emulator_id
    emulator_log_updated = pyqtSignal(int, str)   # emulator_id, log_message - 表格列专用

    # 🎯 Instagram任务状态更新信号 - 专用于任务状态列更新
    instagram_task_status_updated = pyqtSignal(int, str)  # emulator_id, task_status

    def __init__(self, log_dir: str = "logs", log_level: int = logging.INFO):
        super().__init__()  # 初始化QObject
        self.log_dir = Path(log_dir)
        self.log_level = log_level
        self.loggers = {}
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志系统"""
        # 创建日志目录
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # 主日志文件处理器
        main_log_file = self.log_dir / "app.log"
        file_handler = logging.FileHandler(main_log_file, mode='w', encoding='utf-8')
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        logging.info("简化日志系统初始化完成")
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            logger.setLevel(self.log_level)
            self.loggers[name] = logger
        return self.loggers[name]
    
    def log_info(self, message: str, component: str = "App"):
        """记录信息日志"""
        logger = self.get_logger(component)
        logger.info(message)

        # 🎯 发射日志更新信号
        emulator_id = self._extract_emulator_id(message)
        self.log_updated.emit(component, "INFO", message, emulator_id)

        # 🎯 模拟器日志信号 - 触发表格运行日志列实时更新
        if emulator_id > 0:
            self.emulator_log_updated.emit(emulator_id, message)
    
    def log_error(self, message: str, component: str = "App"):
        """记录错误日志"""
        logger = self.get_logger(component)
        logger.error(message)

        # 🎯 发射日志更新信号
        emulator_id = self._extract_emulator_id(message)
        self.log_updated.emit(component, "ERROR", message, emulator_id)

        # 🎯 模拟器日志信号 - 触发表格运行日志列实时更新
        if emulator_id > 0:
            self.emulator_log_updated.emit(emulator_id, message)

    def log_warning(self, message: str, component: str = "App"):
        """记录警告日志"""
        logger = self.get_logger(component)
        logger.warning(message)

        # 🎯 发射日志更新信号
        emulator_id = self._extract_emulator_id(message)
        self.log_updated.emit(component, "WARNING", message, emulator_id)

        # 🎯 模拟器日志信号 - 触发表格运行日志列实时更新
        if emulator_id > 0:
            self.emulator_log_updated.emit(emulator_id, message)
    
    def log_debug(self, message: str, component: str = "App"):
        """记录调试日志"""
        logger = self.get_logger(component)
        logger.debug(message)

    def _extract_emulator_id(self, message: str) -> int:
        """提取模拟器ID - 用于触发表格运行日志列更新"""
        try:
            # 匹配各种可能的模拟器ID格式
            patterns = [
                r'模拟器\s*(\d+)[^\d]',      # 模拟器1、模拟器 1
                r'模拟器(\d+)[^\d]',        # 模拟器1
                r'emulator_id:\s*(\d+)',    # emulator_id: 1
                r'emulator-(\d+)',          # emulator-5556 (需要转换为模拟器ID)
            ]

            for pattern in patterns:
                match = re.search(pattern, message)
                if match:
                    emulator_id = int(match.group(1))
                    # 端口格式转换
                    if 'emulator-' in pattern:
                        # emulator-5556 -> 模拟器ID: (5556-5554)/2 = 1
                        if emulator_id >= 5554 and emulator_id % 2 == 0:
                            emulator_id = (emulator_id - 5554) // 2
                        else:
                            continue  # 无效的ADB端口格式
                    return emulator_id

            return 0  # 未找到模拟器ID
        except Exception:
            return 0


# 全局日志管理器实例
_logger_manager = None


def setup_logging(log_dir: str = "logs", log_level: int = logging.INFO) -> SimpleLogger:
    """设置全局日志系统"""
    global _logger_manager
    _logger_manager = SimpleLogger(log_dir, log_level)
    return _logger_manager


def get_logger_manager() -> SimpleLogger:
    """获取全局日志管理器"""
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = SimpleLogger()
    return _logger_manager


def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return get_logger_manager().get_logger(name)


# 便捷日志函数
def log_info(message: str, component: str = "App"):
    """记录信息日志的便捷函数"""
    get_logger_manager().log_info(message, component)


def log_error(message: str, component: str = "App"):
    """记录错误日志的便捷函数"""
    get_logger_manager().log_error(message, component)


def log_warning(message: str, component: str = "App"):
    """记录警告日志的便捷函数"""
    get_logger_manager().log_warning(message, component)


def log_debug(message: str, component: str = "App"):
    """记录调试日志的便捷函数"""
    get_logger_manager().log_debug(message, component)


# 兼容性函数 - 保持与旧代码的兼容性
def log_runtime(message: str, component: str = "Runtime", **kwargs):
    """运行时日志 - 兼容性函数"""
    extra_info = " | ".join([f"{k}: {v}" for k, v in kwargs.items()]) if kwargs else ""
    full_message = f"{message} | {extra_info}" if extra_info else message
    log_info(full_message, component)


def log_emulator(message: str, component: str = "Emulator", **kwargs):
    """模拟器日志 - 兼容性函数"""
    extra_info = " | ".join([f"{k}: {v}" for k, v in kwargs.items()]) if kwargs else ""
    full_message = f"{message} | {extra_info}" if extra_info else message
    log_info(full_message, component)


def log_performance(message: str, duration: float = 0, component: str = "Performance", **kwargs):
    """性能日志 - 兼容性函数"""
    extra_info = " | ".join([f"{k}: {v}" for k, v in kwargs.items()]) if kwargs else ""
    duration_info = f"耗时: {duration:.2f}s" if duration > 0 else ""
    parts = [message, duration_info, extra_info]
    full_message = " | ".join([p for p in parts if p])
    log_info(full_message, component)


def log_system(message: str, component: str = "System", **kwargs):
    """系统日志 - 兼容性函数"""
    extra_info = " | ".join([f"{k}: {v}" for k, v in kwargs.items()]) if kwargs else ""
    full_message = f"{message} | {extra_info}" if extra_info else message
    log_info(full_message, component)


def log_business(message: str, component: str = "Business", **kwargs):
    """业务日志 - 兼容性函数"""
    extra_info = " | ".join([f"{k}: {v}" for k, v in kwargs.items()]) if kwargs else ""
    full_message = f"{message} | {extra_info}" if extra_info else message
    log_info(full_message, component)


def log_task_relay(message: str, component: str = "TaskRelay", **kwargs):
    """任务接力日志 - 兼容性函数"""
    extra_info = " | ".join([f"{k}: {v}" for k, v in kwargs.items()]) if kwargs else ""
    full_message = f"{message} | {extra_info}" if extra_info else message
    log_info(full_message, component)
