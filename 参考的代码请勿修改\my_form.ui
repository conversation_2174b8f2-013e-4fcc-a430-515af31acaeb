<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1202</width>
    <height>858</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>雷电</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_2">
    <item>
     <layout class="QVBoxLayout" name="verticalLayout" stretch="8,5">
      <item>
       <widget class="QTableWidget" name="tableWidget">
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>11</pointsize>
         </font>
        </property>
        <property name="editTriggers">
         <set>QAbstractItemView::DoubleClicked|QAbstractItemView::EditKeyPressed</set>
        </property>
        <property name="showDropIndicator" stdset="0">
         <bool>false</bool>
        </property>
        <attribute name="verticalHeaderVisible">
         <bool>false</bool>
        </attribute>
        <column>
         <property name="text">
          <string>选中</string>
         </property>
         <property name="font">
          <font>
           <family>微软雅黑</family>
           <pointsize>10</pointsize>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>索引</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>标题</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>顶层句柄</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>绑定句柄</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>PID</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>窗口状态</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>状态</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>日志</string>
         </property>
        </column>
       </widget>
      </item>
      <item>
       <widget class="QTabWidget" name="tabWidget">
        <property name="currentIndex">
         <number>5</number>
        </property>
        <widget class="QWidget" name="tab_5">
         <attribute name="title">
          <string>配置</string>
         </attribute>
         <widget class="QGroupBox" name="groupBox_5">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>401</width>
            <height>251</height>
           </rect>
          </property>
          <property name="title">
           <string/>
          </property>
          <widget class="QWidget" name="gridLayoutWidget_2">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>20</y>
             <width>381</width>
             <height>91</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_2">
            <item row="0" column="1">
             <widget class="QLineEdit" name="lineEdit_ospath"/>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_20">
              <property name="text">
               <string>  模拟器路径   </string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QPushButton" name="pushButton_emulator_path_Browse">
              <property name="text">
               <string>浏览</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="lineEdit_share_path"/>
            </item>
            <item row="1" column="2">
             <widget class="QPushButton" name="pushButton_share_path_Browse">
              <property name="text">
               <string>浏览</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_21">
              <property name="text">
               <string>  共享路径</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_37">
              <property name="text">
               <string> 截图保存路径  </string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLineEdit" name="lineEdit_share_path_2"/>
            </item>
            <item row="2" column="2">
             <widget class="QPushButton" name="pushButton_share_path_Browse_2">
              <property name="text">
               <string>浏览</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QGroupBox" name="groupBox_7">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>130</y>
             <width>391</width>
             <height>121</height>
            </rect>
           </property>
           <property name="title">
            <string/>
           </property>
           <widget class="QWidget" name="gridLayoutWidget_10">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>10</y>
              <width>361</width>
              <height>104</height>
             </rect>
            </property>
            <layout class="QGridLayout" name="gridLayout_10">
             <item row="0" column="0">
              <widget class="QLabel" name="label_30">
               <property name="text">
                <string>  并发最大线程:  </string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLineEdit" name="lineEdit_max_concurrent_threads"/>
             </item>
             <item row="2" column="1">
              <widget class="QLineEdit" name="lineEdit_task_delay"/>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="label_34">
               <property name="text">
                <string>  任务执行间隔:  </string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="label_31">
               <property name="text">
                <string>  模拟器启动间隔:  </string>
               </property>
              </widget>
             </item>
             <item row="1" column="2">
              <widget class="QLabel" name="label_33">
               <property name="text">
                <string>秒</string>
               </property>
              </widget>
             </item>
             <item row="2" column="2">
              <widget class="QLabel" name="label_36">
               <property name="text">
                <string>秒</string>
               </property>
              </widget>
             </item>
             <item row="0" column="2">
              <widget class="QLabel" name="label_32">
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item row="3" column="0">
              <widget class="QLabel" name="label_24">
               <property name="text">
                <string>  任务超时检测 </string>
               </property>
              </widget>
             </item>
             <item row="3" column="1">
              <widget class="QLineEdit" name="lineEdit_task_out"/>
             </item>
             <item row="3" column="2">
              <widget class="QLabel" name="label_25">
               <property name="text">
                <string>秒  </string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLineEdit" name="lineEdit_start_delay">
               <property name="text">
                <string>15</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </widget>
         </widget>
         <widget class="QGroupBox" name="groupBox_8">
          <property name="geometry">
           <rect>
            <x>410</x>
            <y>0</y>
            <width>271</width>
            <height>141</height>
           </rect>
          </property>
          <property name="title">
           <string/>
          </property>
          <widget class="QWidget" name="gridLayoutWidget_6">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>20</y>
             <width>251</width>
             <height>81</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_6">
            <item row="0" column="2">
             <widget class="QLabel" name="label_26">
              <property name="text">
               <string> 窗口高度:</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_27">
              <property name="text">
               <string> 列间距</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_19">
              <property name="text">
               <string> 窗口宽度:</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="lineEdit_wnd_width">
              <property name="text">
               <string>360</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLineEdit" name="lineEdit_wnd_column_spacing">
              <property name="text">
               <string>100</string>
              </property>
             </widget>
            </item>
            <item row="0" column="3">
             <widget class="QLineEdit" name="lineEdit_wnd_height">
              <property name="text">
               <string>540</string>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QLabel" name="label_35">
              <property name="text">
               <string> 行间距</string>
              </property>
             </widget>
            </item>
            <item row="2" column="3">
             <widget class="QLineEdit" name="lineEdit_wnd_row_spacing">
              <property name="text">
               <string>100</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="horizontalLayoutWidget_2">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>110</y>
             <width>251</width>
             <height>31</height>
            </rect>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <item>
             <widget class="QPushButton" name="pushButton_sortWnd">
              <property name="text">
               <string>窗口排序</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
         <widget class="QGroupBox" name="groupBox_9">
          <property name="geometry">
           <rect>
            <x>410</x>
            <y>150</y>
            <width>271</width>
            <height>101</height>
           </rect>
          </property>
          <property name="title">
           <string/>
          </property>
          <widget class="QWidget" name="gridLayoutWidget_11">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>10</y>
             <width>253</width>
             <height>81</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_12">
            <item row="1" column="0">
             <widget class="QLabel" name="label_39">
              <property name="text">
               <string> WebSocket_Port:</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_38">
              <property name="text">
               <string> WebSocket_IP:</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="lineEdit">
              <property name="text">
               <string>*************</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="lineEdit_2">
              <property name="text">
               <string>13142</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QPushButton" name="pushButton_8">
              <property name="text">
               <string> 开启WS服务</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QCheckBox" name="checkBox_ws">
              <property name="text">
               <string>自动开启WS</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
         <widget class="QPlainTextEdit" name="plainTextEdit_log">
          <property name="geometry">
           <rect>
            <x>690</x>
            <y>0</y>
            <width>491</width>
            <height>251</height>
           </rect>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
font: 9pt &quot;微软雅黑&quot;;
color: rgb(0, 170, 0);</string>
          </property>
         </widget>
        </widget>
        <widget class="QWidget" name="tab">
         <attribute name="title">
          <string>设置1</string>
         </attribute>
         <widget class="QGroupBox" name="groupBox">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>10</y>
            <width>491</width>
            <height>231</height>
           </rect>
          </property>
          <property name="title">
           <string/>
          </property>
          <widget class="QWidget" name="gridLayoutWidget">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>10</y>
             <width>451</width>
             <height>211</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout">
            <item row="1" column="2">
             <widget class="QLineEdit" name="lineEdit_emulator_num2">
              <property name="text">
               <string>1</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QPushButton" name="pushButton_copy_emulator">
              <property name="font">
               <font>
                <family>微软雅黑</family>
                <pointsize>11</pointsize>
               </font>
              </property>
              <property name="text">
               <string>复制模拟器</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QPushButton" name="pushButton_add">
              <property name="font">
               <font>
                <family>微软雅黑</family>
                <pointsize>11</pointsize>
               </font>
              </property>
              <property name="text">
               <string>  新建模拟器  </string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QLineEdit" name="lineEdit_emulator_num">
              <property name="text">
               <string>1</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_16">
              <property name="font">
               <font>
                <family>微软雅黑</family>
                <pointsize>11</pointsize>
               </font>
              </property>
              <property name="text">
               <string>新建模拟器延迟</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLabel" name="label_12">
              <property name="text">
               <string>数    量</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLabel" name="label_13">
              <property name="text">
               <string>数量</string>
              </property>
             </widget>
            </item>
            <item row="0" column="3">
             <widget class="QLabel" name="label_14">
              <property name="font">
               <font>
                <family>微软雅黑</family>
                <pointsize>11</pointsize>
               </font>
              </property>
              <property name="text">
               <string>   个</string>
              </property>
             </widget>
            </item>
            <item row="1" column="3">
             <widget class="QLabel" name="label_15">
              <property name="font">
               <font>
                <family>微软雅黑</family>
                <pointsize>11</pointsize>
               </font>
              </property>
              <property name="text">
               <string>   个</string>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QLineEdit" name="lineEdit_emulator_delay">
              <property name="text">
               <string>10</string>
              </property>
             </widget>
            </item>
            <item row="2" column="3">
             <widget class="QLabel" name="label_17">
              <property name="font">
               <font>
                <family>微软雅黑</family>
                <pointsize>11</pointsize>
               </font>
              </property>
              <property name="text">
               <string>   秒</string>
              </property>
             </widget>
            </item>
            <item row="1" column="4">
             <widget class="QLabel" name="label_18">
              <property name="text">
               <string>    索引    </string>
              </property>
             </widget>
            </item>
            <item row="1" column="5">
             <widget class="QLineEdit" name="lineEdit_emulator_index"/>
            </item>
           </layout>
          </widget>
         </widget>
        </widget>
        <widget class="QWidget" name="tab_3">
         <attribute name="title">
          <string>文件操作</string>
         </attribute>
         <widget class="QGroupBox" name="groupBox_6">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>10</y>
            <width>1181</width>
            <height>231</height>
           </rect>
          </property>
          <property name="title">
           <string>GroupBox</string>
          </property>
          <widget class="QWidget" name="gridLayoutWidget_8">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>20</y>
             <width>361</width>
             <height>91</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_8">
            <item row="0" column="1">
             <widget class="QLineEdit" name="lineEdit_emulator_file_path">
              <property name="text">
               <string>/sdcard</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_28">
              <property name="text">
               <string> 模拟器文件路径 </string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_29">
              <property name="text">
               <string> 电脑文件路径 </string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="lineEdit_host_file_path">
              <property name="text">
               <string>D:/LDPlayer9.0.66/Pictures</string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QPushButton" name="pushButton_randowm_name">
              <property name="text">
               <string>随机名</string>
              </property>
             </widget>
            </item>
            <item row="1" column="2">
             <widget class="QPushButton" name="pushButton_9">
              <property name="text">
               <string>随机名</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="gridLayoutWidget_9">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>120</y>
             <width>351</width>
             <height>111</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_9">
            <item row="1" column="0">
             <widget class="QPushButton" name="pushButton_pull_file">
              <property name="text">
               <string>复制文件到电脑</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QPushButton" name="pushButton_push_file">
              <property name="text">
               <string>发送文件到模拟器</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QPushButton" name="pushButton_5">
              <property name="text">
               <string>PushButton</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QPushButton" name="pushButton_6">
              <property name="text">
               <string>PushButton</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="gridLayoutWidget_7">
           <property name="geometry">
            <rect>
             <x>370</x>
             <y>20</y>
             <width>131</width>
             <height>201</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_7">
            <item row="2" column="0">
             <widget class="QPushButton" name="pushButton">
              <property name="text">
               <string>获取分辨率</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QPushButton" name="pushButton_root_open">
              <property name="text">
               <string>开启: 旋转/锁定/root</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QPushButton" name="pushButton_root_close">
              <property name="text">
               <string>关闭: 旋转/锁定/root</string>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QPushButton" name="pushButton_2">
              <property name="text">
               <string>获取包名列表</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QGroupBox" name="groupBox_3">
           <property name="geometry">
            <rect>
             <x>510</x>
             <y>10</y>
             <width>281</width>
             <height>221</height>
            </rect>
           </property>
           <property name="title">
            <string>模拟器配置</string>
           </property>
           <widget class="QWidget" name="gridLayoutWidget_3">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>20</y>
              <width>261</width>
              <height>101</height>
             </rect>
            </property>
            <layout class="QGridLayout" name="gridLayout_3">
             <item row="0" column="1">
              <widget class="QLineEdit" name="lineEdit_Resolution_weight">
               <property name="text">
                <string>720</string>
               </property>
              </widget>
             </item>
             <item row="0" column="2">
              <widget class="QLineEdit" name="lineEdit_Resolution_height">
               <property name="text">
                <string>1080</string>
               </property>
              </widget>
             </item>
             <item row="0" column="0">
              <widget class="QLabel" name="label">
               <property name="text">
                <string>分辨率:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="label_2">
               <property name="text">
                <string>CPU</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QComboBox" name="comboBox_Cpu"/>
             </item>
             <item row="1" column="2">
              <widget class="QLabel" name="label_3">
               <property name="text">
                <string> 内存        </string>
               </property>
              </widget>
             </item>
             <item row="1" column="3">
              <widget class="QComboBox" name="comboBox_Memory"/>
             </item>
             <item row="0" column="3">
              <widget class="QComboBox" name="comboBox_Dpi"/>
             </item>
            </layout>
           </widget>
           <widget class="QPushButton" name="pushButton_modifyResolution">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>140</y>
              <width>111</width>
              <height>31</height>
             </rect>
            </property>
            <property name="text">
             <string>修改分辨率</string>
            </property>
           </widget>
           <widget class="QPushButton" name="pushButton_modifyPhone">
            <property name="geometry">
             <rect>
              <x>140</x>
              <y>140</y>
              <width>111</width>
              <height>31</height>
             </rect>
            </property>
            <property name="text">
             <string>修改手机配置</string>
            </property>
           </widget>
           <widget class="QPushButton" name="pushButton_modifyCPU">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>180</y>
              <width>111</width>
              <height>31</height>
             </rect>
            </property>
            <property name="text">
             <string>修改模拟器配置</string>
            </property>
           </widget>
           <widget class="QPushButton" name="pushButton_random_device">
            <property name="geometry">
             <rect>
              <x>140</x>
              <y>180</y>
              <width>111</width>
              <height>31</height>
             </rect>
            </property>
            <property name="text">
             <string>随机生成配置</string>
            </property>
           </widget>
          </widget>
          <widget class="QGroupBox" name="groupBox_4">
           <property name="geometry">
            <rect>
             <x>790</x>
             <y>10</y>
             <width>381</width>
             <height>221</height>
            </rect>
           </property>
           <property name="title">
            <string>手机配置</string>
           </property>
           <widget class="QWidget" name="gridLayoutWidget_4">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>20</y>
              <width>351</width>
              <height>191</height>
             </rect>
            </property>
            <layout class="QGridLayout" name="gridLayout_4">
             <item row="0" column="0">
              <widget class="QLabel" name="label_4">
               <property name="text">
                <string>手机厂商</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLineEdit" name="lineEdit_phone_Manufacturer"/>
             </item>
             <item row="3" column="3">
              <widget class="QLineEdit" name="lineEdit_phone_Mac"/>
             </item>
             <item row="2" column="3">
              <widget class="QLineEdit" name="lineEdit_phone_Sim"/>
             </item>
             <item row="2" column="1">
              <widget class="QLineEdit" name="lineEdit_phone_IMSI"/>
             </item>
             <item row="3" column="1">
              <widget class="QLineEdit" name="lineEdit_phone_Android_ID"/>
             </item>
             <item row="3" column="0">
              <widget class="QLabel" name="label_10">
               <property name="text">
                <string>安卓ID</string>
               </property>
              </widget>
             </item>
             <item row="1" column="3">
              <widget class="QLineEdit" name="lineEdit_phone_IMEI"/>
             </item>
             <item row="1" column="1">
              <widget class="QLineEdit" name="lineEdit_phone_Number"/>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="label_5">
               <property name="text">
                <string>手机号码</string>
               </property>
              </widget>
             </item>
             <item row="0" column="2">
              <widget class="QLabel" name="label_6">
               <property name="text">
                <string>手机型号</string>
               </property>
              </widget>
             </item>
             <item row="1" column="2">
              <widget class="QLabel" name="label_7">
               <property name="text">
                <string>IMEI</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="label_8">
               <property name="text">
                <string>IMSI</string>
               </property>
              </widget>
             </item>
             <item row="0" column="3">
              <widget class="QLineEdit" name="lineEdit_phone_Model"/>
             </item>
             <item row="2" column="2">
              <widget class="QLabel" name="label_9">
               <property name="text">
                <string>Sim序列号</string>
               </property>
              </widget>
             </item>
             <item row="3" column="2">
              <widget class="QLabel" name="label_11">
               <property name="text">
                <string>Mac</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </widget>
         </widget>
        </widget>
        <widget class="QWidget" name="tab_4">
         <attribute name="title">
          <string>定制功能</string>
         </attribute>
         <widget class="QGroupBox" name="groupBox_2">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>10</y>
            <width>681</width>
            <height>271</height>
           </rect>
          </property>
          <property name="title">
           <string/>
          </property>
          <widget class="QWidget" name="gridLayoutWidget_5">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>20</y>
             <width>641</width>
             <height>71</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_5">
            <item row="1" column="1">
             <widget class="QLineEdit" name="lineEdit_App_pkname">
              <property name="text">
               <string>tv.danmaku.bili</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_23">
              <property name="text">
               <string>ApK 包名:</string>
              </property>
             </widget>
            </item>
            <item row="1" column="3">
             <widget class="QPushButton" name="pushButton_App_UnInstall">
              <property name="text">
               <string>卸载App</string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QPushButton" name="pushButton_App_Browse">
              <property name="text">
               <string>浏览</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_22">
              <property name="text">
               <string>ApK 路径:</string>
              </property>
             </widget>
            </item>
            <item row="0" column="3">
             <widget class="QPushButton" name="pushButton_App_Install">
              <property name="text">
               <string>安装App</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="lineEdit_App_Path"/>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="gridLayoutWidget_12">
           <property name="geometry">
            <rect>
             <x>300</x>
             <y>130</y>
             <width>161</width>
             <height>81</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_11">
            <item row="0" column="2">
             <widget class="QLineEdit" name="lineEdit_last_device"/>
            </item>
            <item row="0" column="1">
             <widget class="QLabel" name="label_41">
              <property name="text">
               <string>上次运行到   :</string>
              </property>
             </widget>
            </item>
            <item row="1" column="2">
             <widget class="QPushButton" name="pushButton_tiaozhuan">
              <property name="text">
               <string> 跳至该行 </string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="gridLayoutWidget_13">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>100</y>
             <width>81</width>
             <height>161</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_13">
            <item row="0" column="0">
             <widget class="QPushButton" name="pushButton_App_launch">
              <property name="text">
               <string>启动App</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QPushButton" name="pushButton_App_kill">
              <property name="text">
               <string>终止App</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QGroupBox" name="groupBox_10">
           <property name="geometry">
            <rect>
             <x>470</x>
             <y>100</y>
             <width>181</width>
             <height>121</height>
            </rect>
           </property>
           <property name="title">
            <string>GroupBox</string>
           </property>
           <widget class="QWidget" name="gridLayoutWidget_15">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>30</y>
              <width>158</width>
              <height>81</height>
             </rect>
            </property>
            <layout class="QGridLayout" name="gridLayout_15">
             <item row="0" column="0">
              <widget class="QPushButton" name="pushButton_start_task">
               <property name="text">
                <string>启动</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QPushButton" name="pushButton_export">
               <property name="text">
                <string>导出状态</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QPushButton" name="pushButton_save_config">
               <property name="text">
                <string>保存配置</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </widget>
          <widget class="QLabel" name="label_50">
           <property name="geometry">
            <rect>
             <x>90</x>
             <y>100</y>
             <width>361</width>
             <height>31</height>
            </rect>
           </property>
           <property name="styleSheet">
            <string notr="true">color: rgb(255, 0, 0);</string>
           </property>
           <property name="text">
            <string>  安装/卸载  支持多文件,路径和包名数量须匹配, 以 | 分割</string>
           </property>
          </widget>
          <widget class="QWidget" name="gridLayoutWidget_14">
           <property name="geometry">
            <rect>
             <x>110</x>
             <y>130</y>
             <width>171</width>
             <height>131</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_14">
            <item row="1" column="0">
             <widget class="QCheckBox" name="checkBox_is_uninstall_app">
              <property name="text">
               <string>重装 V2ray</string>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QCheckBox" name="checkBox_change_title">
              <property name="text">
               <string>是否修改标题</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QCheckBox" name="checkBox_update_ins">
              <property name="text">
               <string>更新INS</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QCheckBox" name="checkBox_update_node">
              <property name="text">
               <string>更新订阅</string>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QCheckBox" name="checkBox_default_font_size">
              <property name="text">
               <string>调整系统默认字体大小</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="layoutWidget">
           <property name="geometry">
            <rect>
             <x>300</x>
             <y>230</y>
             <width>351</width>
             <height>31</height>
            </rect>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="QLineEdit" name="lineEdit_title_text"/>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_select_title_text">
              <property name="text">
               <string>选择文本</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_53">
              <property name="text">
               <string>   |   </string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_update_title">
              <property name="text">
               <string>改标题</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_update_remark">
              <property name="text">
               <string>改备注</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
         <widget class="QWidget" name="gridLayoutWidget_16">
          <property name="geometry">
           <rect>
            <x>700</x>
            <y>70</y>
            <width>471</width>
            <height>171</height>
           </rect>
          </property>
          <layout class="QGridLayout" name="gridLayout_16">
           <item row="2" column="5">
            <widget class="QLabel" name="label_49">
             <property name="text">
              <string>   重启    </string>
             </property>
            </widget>
           </item>
           <item row="2" column="4">
            <widget class="QLineEdit" name="lineEdit_8"/>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="lineEdit_INS_timeout1">
             <property name="text">
              <string>60</string>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QLabel" name="label_46">
             <property name="text">
              <string>   失败    </string>
             </property>
            </widget>
           </item>
           <item row="1" column="5">
            <widget class="QLabel" name="label_47">
             <property name="text">
              <string>   重启    </string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_44">
             <property name="text">
              <string>INS首页等待超时:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="4">
            <widget class="QLineEdit" name="lineEdit_7"/>
           </item>
           <item row="1" column="6">
            <widget class="QLineEdit" name="lineEdit_9"/>
           </item>
           <item row="2" column="3">
            <widget class="QLabel" name="label_48">
             <property name="text">
              <string>   失败    </string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="lineEdit_total_timeout">
             <property name="text">
              <string>900</string>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_45">
             <property name="text">
              <string>INS状态等待超时:</string>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QLineEdit" name="lineEdit_INS_timeout2">
             <property name="text">
              <string>30</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_42">
             <property name="text">
              <string>总任务超时时间: </string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_43">
             <property name="text">
              <string>V2ray总超时时间: </string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="lineEdit_v2ray_timeout">
             <property name="text">
              <string>500</string>
             </property>
            </widget>
           </item>
           <item row="1" column="7">
            <widget class="QLabel" name="label_51">
             <property name="text">
              <string>   重启    </string>
             </property>
            </widget>
           </item>
           <item row="2" column="6">
            <widget class="QLineEdit" name="lineEdit_10"/>
           </item>
           <item row="2" column="7">
            <widget class="QLabel" name="label_52">
             <property name="text">
              <string>   重启    </string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QLineEdit" name="lineEdit_node_client">
          <property name="geometry">
           <rect>
            <x>760</x>
            <y>20</y>
            <width>401</width>
            <height>31</height>
           </rect>
          </property>
         </widget>
         <widget class="QLabel" name="label_40">
          <property name="geometry">
           <rect>
            <x>680</x>
            <y>24</y>
            <width>71</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>订阅地址:</string>
          </property>
         </widget>
         <widget class="QPushButton" name="pushButton_task_2">
          <property name="geometry">
           <rect>
            <x>700</x>
            <y>250</y>
            <width>75</width>
            <height>24</height>
           </rect>
          </property>
          <property name="text">
           <string>任务2</string>
          </property>
         </widget>
        </widget>
        <widget class="QWidget" name="tab_6">
         <attribute name="title">
          <string>定制功能:关注</string>
         </attribute>
         <widget class="QGroupBox" name="groupBox_11">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>10</y>
            <width>311</width>
            <height>271</height>
           </rect>
          </property>
          <property name="title">
           <string>任务数量设置</string>
          </property>
          <widget class="QWidget" name="gridLayoutWidget_18">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>20</y>
             <width>291</width>
             <height>251</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_18">
            <item row="0" column="0">
             <widget class="QLabel" name="label_58">
              <property name="text">
               <string>  直接用户关注数量:</string>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QCheckBox" name="checkBox_is_blue">
              <property name="text">
               <string>蓝V用户跳过</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="3" column="2">
             <widget class="QLabel" name="label_61">
              <property name="text">
               <string>跳过 </string>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="label_60">
              <property name="text">
               <string>  粉丝数低于:</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="lineEdit_follow_Num_2">
              <property name="text">
               <string>50</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_59">
              <property name="text">
               <string>  用户粉丝关注数量:</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="lineEdit_follow_Num_1">
              <property name="text">
               <string>100</string>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QLineEdit" name="lineEdit_min_followers">
              <property name="text">
               <string>100</string>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <widget class="QCheckBox" name="checkBox_is_private">
              <property name="text">
               <string>私密用户跳过</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_69">
              <property name="text">
               <string>  私信任务数量:</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLineEdit" name="lineEdit_send_msg_Num">
              <property name="text">
               <string>50</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
         <widget class="QGroupBox" name="groupBox_12">
          <property name="geometry">
           <rect>
            <x>320</x>
            <y>10</y>
            <width>351</width>
            <height>271</height>
           </rect>
          </property>
          <property name="title">
           <string>延时参数设置</string>
          </property>
          <widget class="QWidget" name="gridLayoutWidget_19">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>20</y>
             <width>331</width>
             <height>251</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_19">
            <item row="1" column="2">
             <widget class="QLineEdit" name="lineEdit_delay_4">
              <property name="text">
               <string>15</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="lineEdit_delay_1">
              <property name="text">
               <string>5</string>
              </property>
             </widget>
            </item>
            <item row="5" column="1">
             <widget class="QLineEdit" name="lineEdit_delay_5">
              <property name="text">
               <string>3</string>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <widget class="QLineEdit" name="lineEdit_scroll_timeout">
              <property name="text">
               <string>100</string>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="label_64">
              <property name="text">
               <string> 用户资料页加载超时</string>
              </property>
             </widget>
            </item>
            <item row="5" column="0">
             <widget class="QLabel" name="label_66">
              <property name="text">
               <string> 通用延迟</string>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QLineEdit" name="lineEdit_page_load_timeout">
              <property name="text">
               <string>30</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_62">
              <property name="text">
               <string> 切换用户延迟</string>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QLineEdit" name="lineEdit_follow_xiuxi2">
              <property name="text">
               <string>10</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="lineEdit_delay_3">
              <property name="text">
               <string>5</string>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QLabel" name="label_65">
              <property name="text">
               <string> 粉丝列表页滑动超时</string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QLineEdit" name="lineEdit_delay_2">
              <property name="text">
               <string>15</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_68">
              <property name="text">
               <string> 关注X个后休息</string>
              </property>
             </widget>
            </item>
            <item row="5" column="2">
             <widget class="QLineEdit" name="lineEdit_delay_6">
              <property name="text">
               <string>6</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLineEdit" name="lineEdit_follow_xiuxi1">
              <property name="text">
               <string>10</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_63">
              <property name="text">
               <string> 关注延迟</string>
              </property>
             </widget>
            </item>
            <item row="2" column="3">
             <widget class="QLineEdit" name="lineEdit_follow_xiuxi3">
              <property name="text">
               <string>20</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
         <widget class="QGroupBox" name="groupBox_13">
          <property name="geometry">
           <rect>
            <x>690</x>
            <y>20</y>
            <width>131</width>
            <height>271</height>
           </rect>
          </property>
          <property name="title">
           <string>用户地区选择</string>
          </property>
          <widget class="QWidget" name="gridLayoutWidget_20">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>20</y>
             <width>111</width>
             <height>241</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_20">
            <item row="0" column="0">
             <widget class="QCheckBox" name="checkBox_regions_1">
              <property name="text">
               <string>中国</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QCheckBox" name="checkBox_regions_3">
              <property name="text">
               <string>韩国</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QCheckBox" name="checkBox_regions_2">
              <property name="text">
               <string>日本</string>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QCheckBox" name="checkBox_regions_4">
              <property name="text">
               <string>泰国</string>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QCheckBox" name="checkBox_regions_5">
              <property name="text">
               <string>通用</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
         <widget class="QGroupBox" name="groupBox_14">
          <property name="geometry">
           <rect>
            <x>850</x>
            <y>10</y>
            <width>321</width>
            <height>211</height>
           </rect>
          </property>
          <property name="title">
           <string>调试</string>
          </property>
          <widget class="QWidget" name="gridLayoutWidget_21">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>20</y>
             <width>301</width>
             <height>41</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_21">
            <item row="0" column="1">
             <widget class="QLineEdit" name="lineEdit_target_user_path"/>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_67">
              <property name="text">
               <string>目标用户文本选择</string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QPushButton" name="pushButton_select_target_user_path">
              <property name="text">
               <string>选择</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="gridLayoutWidget_22">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>70</y>
             <width>301</width>
             <height>71</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_22">
            <item row="1" column="0">
             <widget class="QRadioButton" name="radioButton_task_follow_1">
              <property name="text">
               <string>关注目标</string>
              </property>
             </widget>
            </item>
            <item row="1" column="2">
             <widget class="QRadioButton" name="radioButton_task_follow_3">
              <property name="text">
               <string>私信</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QRadioButton" name="radioButton_task_follow_2">
              <property name="text">
               <string>关注目标粉丝</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QRadioButton" name="radioButton_task_1">
              <property name="text">
               <string>任务1</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QRadioButton" name="radioButton_task_2">
              <property name="text">
               <string>任务2</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="gridLayoutWidget_23">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>150</y>
             <width>301</width>
             <height>41</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout_23">
            <item row="0" column="0">
             <widget class="QPushButton" name="pushButton_task_follow">
              <property name="text">
               <string>开始任务</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QPushButton" name="pushButton_stop_task">
              <property name="text">
               <string>停止</string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QPushButton" name="pushButton_save_config_2">
              <property name="text">
               <string>保存配置</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </widget>
        <widget class="QWidget" name="tab_8">
         <attribute name="title">
          <string>私信话术</string>
         </attribute>
         <widget class="QGroupBox" name="groupBox_16">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>10</y>
            <width>561</width>
            <height>271</height>
           </rect>
          </property>
          <property name="title">
           <string>GroupBox</string>
          </property>
          <widget class="QPlainTextEdit" name="plainTextEdit_send_messages">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>50</y>
             <width>541</width>
             <height>211</height>
            </rect>
           </property>
           <property name="plainText">
            <string>hi|nihao|hello</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_70">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>20</y>
             <width>461</width>
             <height>21</height>
            </rect>
           </property>
           <property name="text">
            <string>多条话术请使用 |  进行分割</string>
           </property>
          </widget>
         </widget>
        </widget>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar">
   <property name="font">
    <font>
     <family>微软雅黑</family>
     <pointsize>10</pointsize>
    </font>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
