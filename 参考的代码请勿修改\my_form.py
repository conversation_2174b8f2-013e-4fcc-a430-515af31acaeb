# Form implementation generated from reading ui file 'my_form.ui'
#
# Created by: PyQt6 UI code generator 6.9.0
#
# WARNING: Any manual changes made to this file will be lost when pyuic6 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt6 import <PERSON>t<PERSON><PERSON>, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1202, 858)
        self.centralwidget = QtWidgets.QWidget(parent=MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setObjectName("verticalLayout")
        self.tableWidget = QtWidgets.QTableWidget(parent=self.centralwidget)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.tableWidget.setFont(font)
        self.tableWidget.setEditTriggers(QtWidgets.QAbstractItemView.EditTrigger.DoubleClicked|QtWidgets.QAbstractItemView.EditTrigger.EditKeyPressed)
        self.tableWidget.setProperty("showDropIndicator", False)
        self.tableWidget.setObjectName("tableWidget")
        self.tableWidget.setColumnCount(9)
        self.tableWidget.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(10)
        item.setFont(font)
        self.tableWidget.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(8, item)
        self.tableWidget.verticalHeader().setVisible(False)
        self.verticalLayout.addWidget(self.tableWidget)
        self.tabWidget = QtWidgets.QTabWidget(parent=self.centralwidget)
        self.tabWidget.setObjectName("tabWidget")
        self.tab_5 = QtWidgets.QWidget()
        self.tab_5.setObjectName("tab_5")
        self.groupBox_5 = QtWidgets.QGroupBox(parent=self.tab_5)
        self.groupBox_5.setGeometry(QtCore.QRect(0, 0, 401, 251))
        self.groupBox_5.setTitle("")
        self.groupBox_5.setObjectName("groupBox_5")
        self.gridLayoutWidget_2 = QtWidgets.QWidget(parent=self.groupBox_5)
        self.gridLayoutWidget_2.setGeometry(QtCore.QRect(10, 20, 381, 91))
        self.gridLayoutWidget_2.setObjectName("gridLayoutWidget_2")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.gridLayoutWidget_2)
        self.gridLayout_2.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.lineEdit_ospath = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_2)
        self.lineEdit_ospath.setObjectName("lineEdit_ospath")
        self.gridLayout_2.addWidget(self.lineEdit_ospath, 0, 1, 1, 1)
        self.label_20 = QtWidgets.QLabel(parent=self.gridLayoutWidget_2)
        self.label_20.setObjectName("label_20")
        self.gridLayout_2.addWidget(self.label_20, 0, 0, 1, 1)
        self.pushButton_emulator_path_Browse = QtWidgets.QPushButton(parent=self.gridLayoutWidget_2)
        self.pushButton_emulator_path_Browse.setObjectName("pushButton_emulator_path_Browse")
        self.gridLayout_2.addWidget(self.pushButton_emulator_path_Browse, 0, 2, 1, 1)
        self.lineEdit_share_path = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_2)
        self.lineEdit_share_path.setObjectName("lineEdit_share_path")
        self.gridLayout_2.addWidget(self.lineEdit_share_path, 1, 1, 1, 1)
        self.pushButton_share_path_Browse = QtWidgets.QPushButton(parent=self.gridLayoutWidget_2)
        self.pushButton_share_path_Browse.setObjectName("pushButton_share_path_Browse")
        self.gridLayout_2.addWidget(self.pushButton_share_path_Browse, 1, 2, 1, 1)
        self.label_21 = QtWidgets.QLabel(parent=self.gridLayoutWidget_2)
        self.label_21.setObjectName("label_21")
        self.gridLayout_2.addWidget(self.label_21, 1, 0, 1, 1)
        self.label_37 = QtWidgets.QLabel(parent=self.gridLayoutWidget_2)
        self.label_37.setObjectName("label_37")
        self.gridLayout_2.addWidget(self.label_37, 2, 0, 1, 1)
        self.lineEdit_share_path_2 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_2)
        self.lineEdit_share_path_2.setObjectName("lineEdit_share_path_2")
        self.gridLayout_2.addWidget(self.lineEdit_share_path_2, 2, 1, 1, 1)
        self.pushButton_share_path_Browse_2 = QtWidgets.QPushButton(parent=self.gridLayoutWidget_2)
        self.pushButton_share_path_Browse_2.setObjectName("pushButton_share_path_Browse_2")
        self.gridLayout_2.addWidget(self.pushButton_share_path_Browse_2, 2, 2, 1, 1)
        self.groupBox_7 = QtWidgets.QGroupBox(parent=self.groupBox_5)
        self.groupBox_7.setGeometry(QtCore.QRect(0, 130, 391, 121))
        self.groupBox_7.setTitle("")
        self.groupBox_7.setObjectName("groupBox_7")
        self.gridLayoutWidget_10 = QtWidgets.QWidget(parent=self.groupBox_7)
        self.gridLayoutWidget_10.setGeometry(QtCore.QRect(10, 10, 361, 104))
        self.gridLayoutWidget_10.setObjectName("gridLayoutWidget_10")
        self.gridLayout_10 = QtWidgets.QGridLayout(self.gridLayoutWidget_10)
        self.gridLayout_10.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_10.setObjectName("gridLayout_10")
        self.label_30 = QtWidgets.QLabel(parent=self.gridLayoutWidget_10)
        self.label_30.setObjectName("label_30")
        self.gridLayout_10.addWidget(self.label_30, 0, 0, 1, 1)
        self.lineEdit_max_concurrent_threads = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_10)
        self.lineEdit_max_concurrent_threads.setObjectName("lineEdit_max_concurrent_threads")
        self.gridLayout_10.addWidget(self.lineEdit_max_concurrent_threads, 0, 1, 1, 1)
        self.lineEdit_task_delay = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_10)
        self.lineEdit_task_delay.setObjectName("lineEdit_task_delay")
        self.gridLayout_10.addWidget(self.lineEdit_task_delay, 2, 1, 1, 1)
        self.label_34 = QtWidgets.QLabel(parent=self.gridLayoutWidget_10)
        self.label_34.setObjectName("label_34")
        self.gridLayout_10.addWidget(self.label_34, 2, 0, 1, 1)
        self.label_31 = QtWidgets.QLabel(parent=self.gridLayoutWidget_10)
        self.label_31.setObjectName("label_31")
        self.gridLayout_10.addWidget(self.label_31, 1, 0, 1, 1)
        self.label_33 = QtWidgets.QLabel(parent=self.gridLayoutWidget_10)
        self.label_33.setObjectName("label_33")
        self.gridLayout_10.addWidget(self.label_33, 1, 2, 1, 1)
        self.label_36 = QtWidgets.QLabel(parent=self.gridLayoutWidget_10)
        self.label_36.setObjectName("label_36")
        self.gridLayout_10.addWidget(self.label_36, 2, 2, 1, 1)
        self.label_32 = QtWidgets.QLabel(parent=self.gridLayoutWidget_10)
        self.label_32.setText("")
        self.label_32.setObjectName("label_32")
        self.gridLayout_10.addWidget(self.label_32, 0, 2, 1, 1)
        self.label_24 = QtWidgets.QLabel(parent=self.gridLayoutWidget_10)
        self.label_24.setObjectName("label_24")
        self.gridLayout_10.addWidget(self.label_24, 3, 0, 1, 1)
        self.lineEdit_task_out = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_10)
        self.lineEdit_task_out.setObjectName("lineEdit_task_out")
        self.gridLayout_10.addWidget(self.lineEdit_task_out, 3, 1, 1, 1)
        self.label_25 = QtWidgets.QLabel(parent=self.gridLayoutWidget_10)
        self.label_25.setObjectName("label_25")
        self.gridLayout_10.addWidget(self.label_25, 3, 2, 1, 1)
        self.lineEdit_start_delay = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_10)
        self.lineEdit_start_delay.setObjectName("lineEdit_start_delay")
        self.gridLayout_10.addWidget(self.lineEdit_start_delay, 1, 1, 1, 1)
        self.groupBox_8 = QtWidgets.QGroupBox(parent=self.tab_5)
        self.groupBox_8.setGeometry(QtCore.QRect(410, 0, 271, 141))
        self.groupBox_8.setTitle("")
        self.groupBox_8.setObjectName("groupBox_8")
        self.gridLayoutWidget_6 = QtWidgets.QWidget(parent=self.groupBox_8)
        self.gridLayoutWidget_6.setGeometry(QtCore.QRect(10, 20, 251, 81))
        self.gridLayoutWidget_6.setObjectName("gridLayoutWidget_6")
        self.gridLayout_6 = QtWidgets.QGridLayout(self.gridLayoutWidget_6)
        self.gridLayout_6.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_6.setObjectName("gridLayout_6")
        self.label_26 = QtWidgets.QLabel(parent=self.gridLayoutWidget_6)
        self.label_26.setObjectName("label_26")
        self.gridLayout_6.addWidget(self.label_26, 0, 2, 1, 1)
        self.label_27 = QtWidgets.QLabel(parent=self.gridLayoutWidget_6)
        self.label_27.setObjectName("label_27")
        self.gridLayout_6.addWidget(self.label_27, 2, 0, 1, 1)
        self.label_19 = QtWidgets.QLabel(parent=self.gridLayoutWidget_6)
        self.label_19.setObjectName("label_19")
        self.gridLayout_6.addWidget(self.label_19, 0, 0, 1, 1)
        self.lineEdit_wnd_width = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_6)
        self.lineEdit_wnd_width.setObjectName("lineEdit_wnd_width")
        self.gridLayout_6.addWidget(self.lineEdit_wnd_width, 0, 1, 1, 1)
        self.lineEdit_wnd_column_spacing = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_6)
        self.lineEdit_wnd_column_spacing.setObjectName("lineEdit_wnd_column_spacing")
        self.gridLayout_6.addWidget(self.lineEdit_wnd_column_spacing, 2, 1, 1, 1)
        self.lineEdit_wnd_height = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_6)
        self.lineEdit_wnd_height.setObjectName("lineEdit_wnd_height")
        self.gridLayout_6.addWidget(self.lineEdit_wnd_height, 0, 3, 1, 1)
        self.label_35 = QtWidgets.QLabel(parent=self.gridLayoutWidget_6)
        self.label_35.setObjectName("label_35")
        self.gridLayout_6.addWidget(self.label_35, 2, 2, 1, 1)
        self.lineEdit_wnd_row_spacing = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_6)
        self.lineEdit_wnd_row_spacing.setObjectName("lineEdit_wnd_row_spacing")
        self.gridLayout_6.addWidget(self.lineEdit_wnd_row_spacing, 2, 3, 1, 1)
        self.horizontalLayoutWidget_2 = QtWidgets.QWidget(parent=self.groupBox_8)
        self.horizontalLayoutWidget_2.setGeometry(QtCore.QRect(10, 110, 251, 31))
        self.horizontalLayoutWidget_2.setObjectName("horizontalLayoutWidget_2")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.horizontalLayoutWidget_2)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.pushButton_sortWnd = QtWidgets.QPushButton(parent=self.horizontalLayoutWidget_2)
        self.pushButton_sortWnd.setObjectName("pushButton_sortWnd")
        self.horizontalLayout_4.addWidget(self.pushButton_sortWnd)
        self.groupBox_9 = QtWidgets.QGroupBox(parent=self.tab_5)
        self.groupBox_9.setGeometry(QtCore.QRect(410, 150, 271, 101))
        self.groupBox_9.setTitle("")
        self.groupBox_9.setObjectName("groupBox_9")
        self.gridLayoutWidget_11 = QtWidgets.QWidget(parent=self.groupBox_9)
        self.gridLayoutWidget_11.setGeometry(QtCore.QRect(10, 10, 253, 81))
        self.gridLayoutWidget_11.setObjectName("gridLayoutWidget_11")
        self.gridLayout_12 = QtWidgets.QGridLayout(self.gridLayoutWidget_11)
        self.gridLayout_12.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_12.setObjectName("gridLayout_12")
        self.label_39 = QtWidgets.QLabel(parent=self.gridLayoutWidget_11)
        self.label_39.setObjectName("label_39")
        self.gridLayout_12.addWidget(self.label_39, 1, 0, 1, 1)
        self.label_38 = QtWidgets.QLabel(parent=self.gridLayoutWidget_11)
        self.label_38.setObjectName("label_38")
        self.gridLayout_12.addWidget(self.label_38, 0, 0, 1, 1)
        self.lineEdit = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_11)
        self.lineEdit.setObjectName("lineEdit")
        self.gridLayout_12.addWidget(self.lineEdit, 0, 1, 1, 1)
        self.lineEdit_2 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_11)
        self.lineEdit_2.setObjectName("lineEdit_2")
        self.gridLayout_12.addWidget(self.lineEdit_2, 1, 1, 1, 1)
        self.pushButton_8 = QtWidgets.QPushButton(parent=self.gridLayoutWidget_11)
        self.pushButton_8.setObjectName("pushButton_8")
        self.gridLayout_12.addWidget(self.pushButton_8, 2, 1, 1, 1)
        self.checkBox_ws = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_11)
        self.checkBox_ws.setObjectName("checkBox_ws")
        self.gridLayout_12.addWidget(self.checkBox_ws, 2, 0, 1, 1)
        self.plainTextEdit_log = QtWidgets.QPlainTextEdit(parent=self.tab_5)
        self.plainTextEdit_log.setGeometry(QtCore.QRect(690, 0, 491, 251))
        self.plainTextEdit_log.setStyleSheet("background-color: rgb(0, 0, 0);\n"
"font: 9pt \"微软雅黑\";\n"
"color: rgb(0, 170, 0);")
        self.plainTextEdit_log.setObjectName("plainTextEdit_log")
        self.tabWidget.addTab(self.tab_5, "")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.groupBox = QtWidgets.QGroupBox(parent=self.tab)
        self.groupBox.setGeometry(QtCore.QRect(0, 10, 491, 231))
        self.groupBox.setTitle("")
        self.groupBox.setObjectName("groupBox")
        self.gridLayoutWidget = QtWidgets.QWidget(parent=self.groupBox)
        self.gridLayoutWidget.setGeometry(QtCore.QRect(10, 10, 451, 211))
        self.gridLayoutWidget.setObjectName("gridLayoutWidget")
        self.gridLayout = QtWidgets.QGridLayout(self.gridLayoutWidget)
        self.gridLayout.setContentsMargins(0, 0, 0, 0)
        self.gridLayout.setObjectName("gridLayout")
        self.lineEdit_emulator_num2 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget)
        self.lineEdit_emulator_num2.setObjectName("lineEdit_emulator_num2")
        self.gridLayout.addWidget(self.lineEdit_emulator_num2, 1, 2, 1, 1)
        self.pushButton_copy_emulator = QtWidgets.QPushButton(parent=self.gridLayoutWidget)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.pushButton_copy_emulator.setFont(font)
        self.pushButton_copy_emulator.setObjectName("pushButton_copy_emulator")
        self.gridLayout.addWidget(self.pushButton_copy_emulator, 1, 0, 1, 1)
        self.pushButton_add = QtWidgets.QPushButton(parent=self.gridLayoutWidget)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.pushButton_add.setFont(font)
        self.pushButton_add.setObjectName("pushButton_add")
        self.gridLayout.addWidget(self.pushButton_add, 0, 0, 1, 1)
        self.lineEdit_emulator_num = QtWidgets.QLineEdit(parent=self.gridLayoutWidget)
        self.lineEdit_emulator_num.setObjectName("lineEdit_emulator_num")
        self.gridLayout.addWidget(self.lineEdit_emulator_num, 0, 2, 1, 1)
        self.label_16 = QtWidgets.QLabel(parent=self.gridLayoutWidget)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.label_16.setFont(font)
        self.label_16.setObjectName("label_16")
        self.gridLayout.addWidget(self.label_16, 2, 0, 1, 1)
        self.label_12 = QtWidgets.QLabel(parent=self.gridLayoutWidget)
        self.label_12.setObjectName("label_12")
        self.gridLayout.addWidget(self.label_12, 0, 1, 1, 1)
        self.label_13 = QtWidgets.QLabel(parent=self.gridLayoutWidget)
        self.label_13.setObjectName("label_13")
        self.gridLayout.addWidget(self.label_13, 1, 1, 1, 1)
        self.label_14 = QtWidgets.QLabel(parent=self.gridLayoutWidget)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.label_14.setFont(font)
        self.label_14.setObjectName("label_14")
        self.gridLayout.addWidget(self.label_14, 0, 3, 1, 1)
        self.label_15 = QtWidgets.QLabel(parent=self.gridLayoutWidget)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.label_15.setFont(font)
        self.label_15.setObjectName("label_15")
        self.gridLayout.addWidget(self.label_15, 1, 3, 1, 1)
        self.lineEdit_emulator_delay = QtWidgets.QLineEdit(parent=self.gridLayoutWidget)
        self.lineEdit_emulator_delay.setObjectName("lineEdit_emulator_delay")
        self.gridLayout.addWidget(self.lineEdit_emulator_delay, 2, 2, 1, 1)
        self.label_17 = QtWidgets.QLabel(parent=self.gridLayoutWidget)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.label_17.setFont(font)
        self.label_17.setObjectName("label_17")
        self.gridLayout.addWidget(self.label_17, 2, 3, 1, 1)
        self.label_18 = QtWidgets.QLabel(parent=self.gridLayoutWidget)
        self.label_18.setObjectName("label_18")
        self.gridLayout.addWidget(self.label_18, 1, 4, 1, 1)
        self.lineEdit_emulator_index = QtWidgets.QLineEdit(parent=self.gridLayoutWidget)
        self.lineEdit_emulator_index.setObjectName("lineEdit_emulator_index")
        self.gridLayout.addWidget(self.lineEdit_emulator_index, 1, 5, 1, 1)
        self.tabWidget.addTab(self.tab, "")
        self.tab_3 = QtWidgets.QWidget()
        self.tab_3.setObjectName("tab_3")
        self.groupBox_6 = QtWidgets.QGroupBox(parent=self.tab_3)
        self.groupBox_6.setGeometry(QtCore.QRect(0, 10, 1181, 231))
        self.groupBox_6.setObjectName("groupBox_6")
        self.gridLayoutWidget_8 = QtWidgets.QWidget(parent=self.groupBox_6)
        self.gridLayoutWidget_8.setGeometry(QtCore.QRect(0, 20, 361, 91))
        self.gridLayoutWidget_8.setObjectName("gridLayoutWidget_8")
        self.gridLayout_8 = QtWidgets.QGridLayout(self.gridLayoutWidget_8)
        self.gridLayout_8.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_8.setObjectName("gridLayout_8")
        self.lineEdit_emulator_file_path = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_8)
        self.lineEdit_emulator_file_path.setObjectName("lineEdit_emulator_file_path")
        self.gridLayout_8.addWidget(self.lineEdit_emulator_file_path, 0, 1, 1, 1)
        self.label_28 = QtWidgets.QLabel(parent=self.gridLayoutWidget_8)
        self.label_28.setObjectName("label_28")
        self.gridLayout_8.addWidget(self.label_28, 0, 0, 1, 1)
        self.label_29 = QtWidgets.QLabel(parent=self.gridLayoutWidget_8)
        self.label_29.setObjectName("label_29")
        self.gridLayout_8.addWidget(self.label_29, 1, 0, 1, 1)
        self.lineEdit_host_file_path = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_8)
        self.lineEdit_host_file_path.setObjectName("lineEdit_host_file_path")
        self.gridLayout_8.addWidget(self.lineEdit_host_file_path, 1, 1, 1, 1)
        self.pushButton_randowm_name = QtWidgets.QPushButton(parent=self.gridLayoutWidget_8)
        self.pushButton_randowm_name.setObjectName("pushButton_randowm_name")
        self.gridLayout_8.addWidget(self.pushButton_randowm_name, 0, 2, 1, 1)
        self.pushButton_9 = QtWidgets.QPushButton(parent=self.gridLayoutWidget_8)
        self.pushButton_9.setObjectName("pushButton_9")
        self.gridLayout_8.addWidget(self.pushButton_9, 1, 2, 1, 1)
        self.gridLayoutWidget_9 = QtWidgets.QWidget(parent=self.groupBox_6)
        self.gridLayoutWidget_9.setGeometry(QtCore.QRect(0, 120, 351, 111))
        self.gridLayoutWidget_9.setObjectName("gridLayoutWidget_9")
        self.gridLayout_9 = QtWidgets.QGridLayout(self.gridLayoutWidget_9)
        self.gridLayout_9.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_9.setObjectName("gridLayout_9")
        self.pushButton_pull_file = QtWidgets.QPushButton(parent=self.gridLayoutWidget_9)
        self.pushButton_pull_file.setObjectName("pushButton_pull_file")
        self.gridLayout_9.addWidget(self.pushButton_pull_file, 1, 0, 1, 1)
        self.pushButton_push_file = QtWidgets.QPushButton(parent=self.gridLayoutWidget_9)
        self.pushButton_push_file.setObjectName("pushButton_push_file")
        self.gridLayout_9.addWidget(self.pushButton_push_file, 0, 0, 1, 1)
        self.pushButton_5 = QtWidgets.QPushButton(parent=self.gridLayoutWidget_9)
        self.pushButton_5.setObjectName("pushButton_5")
        self.gridLayout_9.addWidget(self.pushButton_5, 0, 1, 1, 1)
        self.pushButton_6 = QtWidgets.QPushButton(parent=self.gridLayoutWidget_9)
        self.pushButton_6.setObjectName("pushButton_6")
        self.gridLayout_9.addWidget(self.pushButton_6, 1, 1, 1, 1)
        self.gridLayoutWidget_7 = QtWidgets.QWidget(parent=self.groupBox_6)
        self.gridLayoutWidget_7.setGeometry(QtCore.QRect(370, 20, 131, 201))
        self.gridLayoutWidget_7.setObjectName("gridLayoutWidget_7")
        self.gridLayout_7 = QtWidgets.QGridLayout(self.gridLayoutWidget_7)
        self.gridLayout_7.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_7.setObjectName("gridLayout_7")
        self.pushButton = QtWidgets.QPushButton(parent=self.gridLayoutWidget_7)
        self.pushButton.setObjectName("pushButton")
        self.gridLayout_7.addWidget(self.pushButton, 2, 0, 1, 1)
        self.pushButton_root_open = QtWidgets.QPushButton(parent=self.gridLayoutWidget_7)
        self.pushButton_root_open.setObjectName("pushButton_root_open")
        self.gridLayout_7.addWidget(self.pushButton_root_open, 0, 0, 1, 1)
        self.pushButton_root_close = QtWidgets.QPushButton(parent=self.gridLayoutWidget_7)
        self.pushButton_root_close.setObjectName("pushButton_root_close")
        self.gridLayout_7.addWidget(self.pushButton_root_close, 1, 0, 1, 1)
        self.pushButton_2 = QtWidgets.QPushButton(parent=self.gridLayoutWidget_7)
        self.pushButton_2.setObjectName("pushButton_2")
        self.gridLayout_7.addWidget(self.pushButton_2, 3, 0, 1, 1)
        self.groupBox_3 = QtWidgets.QGroupBox(parent=self.groupBox_6)
        self.groupBox_3.setGeometry(QtCore.QRect(510, 10, 281, 221))
        self.groupBox_3.setObjectName("groupBox_3")
        self.gridLayoutWidget_3 = QtWidgets.QWidget(parent=self.groupBox_3)
        self.gridLayoutWidget_3.setGeometry(QtCore.QRect(10, 20, 261, 101))
        self.gridLayoutWidget_3.setObjectName("gridLayoutWidget_3")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.gridLayoutWidget_3)
        self.gridLayout_3.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.lineEdit_Resolution_weight = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_3)
        self.lineEdit_Resolution_weight.setObjectName("lineEdit_Resolution_weight")
        self.gridLayout_3.addWidget(self.lineEdit_Resolution_weight, 0, 1, 1, 1)
        self.lineEdit_Resolution_height = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_3)
        self.lineEdit_Resolution_height.setObjectName("lineEdit_Resolution_height")
        self.gridLayout_3.addWidget(self.lineEdit_Resolution_height, 0, 2, 1, 1)
        self.label = QtWidgets.QLabel(parent=self.gridLayoutWidget_3)
        self.label.setObjectName("label")
        self.gridLayout_3.addWidget(self.label, 0, 0, 1, 1)
        self.label_2 = QtWidgets.QLabel(parent=self.gridLayoutWidget_3)
        self.label_2.setObjectName("label_2")
        self.gridLayout_3.addWidget(self.label_2, 1, 0, 1, 1)
        self.comboBox_Cpu = QtWidgets.QComboBox(parent=self.gridLayoutWidget_3)
        self.comboBox_Cpu.setObjectName("comboBox_Cpu")
        self.gridLayout_3.addWidget(self.comboBox_Cpu, 1, 1, 1, 1)
        self.label_3 = QtWidgets.QLabel(parent=self.gridLayoutWidget_3)
        self.label_3.setObjectName("label_3")
        self.gridLayout_3.addWidget(self.label_3, 1, 2, 1, 1)
        self.comboBox_Memory = QtWidgets.QComboBox(parent=self.gridLayoutWidget_3)
        self.comboBox_Memory.setObjectName("comboBox_Memory")
        self.gridLayout_3.addWidget(self.comboBox_Memory, 1, 3, 1, 1)
        self.comboBox_Dpi = QtWidgets.QComboBox(parent=self.gridLayoutWidget_3)
        self.comboBox_Dpi.setObjectName("comboBox_Dpi")
        self.gridLayout_3.addWidget(self.comboBox_Dpi, 0, 3, 1, 1)
        self.pushButton_modifyResolution = QtWidgets.QPushButton(parent=self.groupBox_3)
        self.pushButton_modifyResolution.setGeometry(QtCore.QRect(10, 140, 111, 31))
        self.pushButton_modifyResolution.setObjectName("pushButton_modifyResolution")
        self.pushButton_modifyPhone = QtWidgets.QPushButton(parent=self.groupBox_3)
        self.pushButton_modifyPhone.setGeometry(QtCore.QRect(140, 140, 111, 31))
        self.pushButton_modifyPhone.setObjectName("pushButton_modifyPhone")
        self.pushButton_modifyCPU = QtWidgets.QPushButton(parent=self.groupBox_3)
        self.pushButton_modifyCPU.setGeometry(QtCore.QRect(10, 180, 111, 31))
        self.pushButton_modifyCPU.setObjectName("pushButton_modifyCPU")
        self.pushButton_random_device = QtWidgets.QPushButton(parent=self.groupBox_3)
        self.pushButton_random_device.setGeometry(QtCore.QRect(140, 180, 111, 31))
        self.pushButton_random_device.setObjectName("pushButton_random_device")
        self.groupBox_4 = QtWidgets.QGroupBox(parent=self.groupBox_6)
        self.groupBox_4.setGeometry(QtCore.QRect(790, 10, 381, 221))
        self.groupBox_4.setObjectName("groupBox_4")
        self.gridLayoutWidget_4 = QtWidgets.QWidget(parent=self.groupBox_4)
        self.gridLayoutWidget_4.setGeometry(QtCore.QRect(10, 20, 351, 191))
        self.gridLayoutWidget_4.setObjectName("gridLayoutWidget_4")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.gridLayoutWidget_4)
        self.gridLayout_4.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.label_4 = QtWidgets.QLabel(parent=self.gridLayoutWidget_4)
        self.label_4.setObjectName("label_4")
        self.gridLayout_4.addWidget(self.label_4, 0, 0, 1, 1)
        self.lineEdit_phone_Manufacturer = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_4)
        self.lineEdit_phone_Manufacturer.setObjectName("lineEdit_phone_Manufacturer")
        self.gridLayout_4.addWidget(self.lineEdit_phone_Manufacturer, 0, 1, 1, 1)
        self.lineEdit_phone_Mac = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_4)
        self.lineEdit_phone_Mac.setObjectName("lineEdit_phone_Mac")
        self.gridLayout_4.addWidget(self.lineEdit_phone_Mac, 3, 3, 1, 1)
        self.lineEdit_phone_Sim = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_4)
        self.lineEdit_phone_Sim.setObjectName("lineEdit_phone_Sim")
        self.gridLayout_4.addWidget(self.lineEdit_phone_Sim, 2, 3, 1, 1)
        self.lineEdit_phone_IMSI = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_4)
        self.lineEdit_phone_IMSI.setObjectName("lineEdit_phone_IMSI")
        self.gridLayout_4.addWidget(self.lineEdit_phone_IMSI, 2, 1, 1, 1)
        self.lineEdit_phone_Android_ID = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_4)
        self.lineEdit_phone_Android_ID.setObjectName("lineEdit_phone_Android_ID")
        self.gridLayout_4.addWidget(self.lineEdit_phone_Android_ID, 3, 1, 1, 1)
        self.label_10 = QtWidgets.QLabel(parent=self.gridLayoutWidget_4)
        self.label_10.setObjectName("label_10")
        self.gridLayout_4.addWidget(self.label_10, 3, 0, 1, 1)
        self.lineEdit_phone_IMEI = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_4)
        self.lineEdit_phone_IMEI.setObjectName("lineEdit_phone_IMEI")
        self.gridLayout_4.addWidget(self.lineEdit_phone_IMEI, 1, 3, 1, 1)
        self.lineEdit_phone_Number = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_4)
        self.lineEdit_phone_Number.setObjectName("lineEdit_phone_Number")
        self.gridLayout_4.addWidget(self.lineEdit_phone_Number, 1, 1, 1, 1)
        self.label_5 = QtWidgets.QLabel(parent=self.gridLayoutWidget_4)
        self.label_5.setObjectName("label_5")
        self.gridLayout_4.addWidget(self.label_5, 1, 0, 1, 1)
        self.label_6 = QtWidgets.QLabel(parent=self.gridLayoutWidget_4)
        self.label_6.setObjectName("label_6")
        self.gridLayout_4.addWidget(self.label_6, 0, 2, 1, 1)
        self.label_7 = QtWidgets.QLabel(parent=self.gridLayoutWidget_4)
        self.label_7.setObjectName("label_7")
        self.gridLayout_4.addWidget(self.label_7, 1, 2, 1, 1)
        self.label_8 = QtWidgets.QLabel(parent=self.gridLayoutWidget_4)
        self.label_8.setObjectName("label_8")
        self.gridLayout_4.addWidget(self.label_8, 2, 0, 1, 1)
        self.lineEdit_phone_Model = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_4)
        self.lineEdit_phone_Model.setObjectName("lineEdit_phone_Model")
        self.gridLayout_4.addWidget(self.lineEdit_phone_Model, 0, 3, 1, 1)
        self.label_9 = QtWidgets.QLabel(parent=self.gridLayoutWidget_4)
        self.label_9.setObjectName("label_9")
        self.gridLayout_4.addWidget(self.label_9, 2, 2, 1, 1)
        self.label_11 = QtWidgets.QLabel(parent=self.gridLayoutWidget_4)
        self.label_11.setObjectName("label_11")
        self.gridLayout_4.addWidget(self.label_11, 3, 2, 1, 1)
        self.tabWidget.addTab(self.tab_3, "")
        self.tab_4 = QtWidgets.QWidget()
        self.tab_4.setObjectName("tab_4")
        self.groupBox_2 = QtWidgets.QGroupBox(parent=self.tab_4)
        self.groupBox_2.setGeometry(QtCore.QRect(0, 10, 681, 271))
        self.groupBox_2.setTitle("")
        self.groupBox_2.setObjectName("groupBox_2")
        self.gridLayoutWidget_5 = QtWidgets.QWidget(parent=self.groupBox_2)
        self.gridLayoutWidget_5.setGeometry(QtCore.QRect(10, 20, 641, 71))
        self.gridLayoutWidget_5.setObjectName("gridLayoutWidget_5")
        self.gridLayout_5 = QtWidgets.QGridLayout(self.gridLayoutWidget_5)
        self.gridLayout_5.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_5.setObjectName("gridLayout_5")
        self.lineEdit_App_pkname = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_5)
        self.lineEdit_App_pkname.setObjectName("lineEdit_App_pkname")
        self.gridLayout_5.addWidget(self.lineEdit_App_pkname, 1, 1, 1, 1)
        self.label_23 = QtWidgets.QLabel(parent=self.gridLayoutWidget_5)
        self.label_23.setObjectName("label_23")
        self.gridLayout_5.addWidget(self.label_23, 1, 0, 1, 1)
        self.pushButton_App_UnInstall = QtWidgets.QPushButton(parent=self.gridLayoutWidget_5)
        self.pushButton_App_UnInstall.setObjectName("pushButton_App_UnInstall")
        self.gridLayout_5.addWidget(self.pushButton_App_UnInstall, 1, 3, 1, 1)
        self.pushButton_App_Browse = QtWidgets.QPushButton(parent=self.gridLayoutWidget_5)
        self.pushButton_App_Browse.setObjectName("pushButton_App_Browse")
        self.gridLayout_5.addWidget(self.pushButton_App_Browse, 0, 2, 1, 1)
        self.label_22 = QtWidgets.QLabel(parent=self.gridLayoutWidget_5)
        self.label_22.setObjectName("label_22")
        self.gridLayout_5.addWidget(self.label_22, 0, 0, 1, 1)
        self.pushButton_App_Install = QtWidgets.QPushButton(parent=self.gridLayoutWidget_5)
        self.pushButton_App_Install.setObjectName("pushButton_App_Install")
        self.gridLayout_5.addWidget(self.pushButton_App_Install, 0, 3, 1, 1)
        self.lineEdit_App_Path = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_5)
        self.lineEdit_App_Path.setObjectName("lineEdit_App_Path")
        self.gridLayout_5.addWidget(self.lineEdit_App_Path, 0, 1, 1, 1)
        self.gridLayoutWidget_12 = QtWidgets.QWidget(parent=self.groupBox_2)
        self.gridLayoutWidget_12.setGeometry(QtCore.QRect(300, 130, 161, 81))
        self.gridLayoutWidget_12.setObjectName("gridLayoutWidget_12")
        self.gridLayout_11 = QtWidgets.QGridLayout(self.gridLayoutWidget_12)
        self.gridLayout_11.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_11.setObjectName("gridLayout_11")
        self.lineEdit_last_device = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_12)
        self.lineEdit_last_device.setObjectName("lineEdit_last_device")
        self.gridLayout_11.addWidget(self.lineEdit_last_device, 0, 2, 1, 1)
        self.label_41 = QtWidgets.QLabel(parent=self.gridLayoutWidget_12)
        self.label_41.setObjectName("label_41")
        self.gridLayout_11.addWidget(self.label_41, 0, 1, 1, 1)
        self.pushButton_tiaozhuan = QtWidgets.QPushButton(parent=self.gridLayoutWidget_12)
        self.pushButton_tiaozhuan.setObjectName("pushButton_tiaozhuan")
        self.gridLayout_11.addWidget(self.pushButton_tiaozhuan, 1, 2, 1, 1)
        self.gridLayoutWidget_13 = QtWidgets.QWidget(parent=self.groupBox_2)
        self.gridLayoutWidget_13.setGeometry(QtCore.QRect(10, 100, 81, 161))
        self.gridLayoutWidget_13.setObjectName("gridLayoutWidget_13")
        self.gridLayout_13 = QtWidgets.QGridLayout(self.gridLayoutWidget_13)
        self.gridLayout_13.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_13.setObjectName("gridLayout_13")
        self.pushButton_App_launch = QtWidgets.QPushButton(parent=self.gridLayoutWidget_13)
        self.pushButton_App_launch.setObjectName("pushButton_App_launch")
        self.gridLayout_13.addWidget(self.pushButton_App_launch, 0, 0, 1, 1)
        self.pushButton_App_kill = QtWidgets.QPushButton(parent=self.gridLayoutWidget_13)
        self.pushButton_App_kill.setObjectName("pushButton_App_kill")
        self.gridLayout_13.addWidget(self.pushButton_App_kill, 1, 0, 1, 1)
        self.groupBox_10 = QtWidgets.QGroupBox(parent=self.groupBox_2)
        self.groupBox_10.setGeometry(QtCore.QRect(470, 100, 181, 121))
        self.groupBox_10.setObjectName("groupBox_10")
        self.gridLayoutWidget_15 = QtWidgets.QWidget(parent=self.groupBox_10)
        self.gridLayoutWidget_15.setGeometry(QtCore.QRect(10, 30, 158, 81))
        self.gridLayoutWidget_15.setObjectName("gridLayoutWidget_15")
        self.gridLayout_15 = QtWidgets.QGridLayout(self.gridLayoutWidget_15)
        self.gridLayout_15.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_15.setObjectName("gridLayout_15")
        self.pushButton_start_task = QtWidgets.QPushButton(parent=self.gridLayoutWidget_15)
        self.pushButton_start_task.setObjectName("pushButton_start_task")
        self.gridLayout_15.addWidget(self.pushButton_start_task, 0, 0, 1, 1)
        self.pushButton_export = QtWidgets.QPushButton(parent=self.gridLayoutWidget_15)
        self.pushButton_export.setObjectName("pushButton_export")
        self.gridLayout_15.addWidget(self.pushButton_export, 0, 1, 1, 1)
        self.pushButton_save_config = QtWidgets.QPushButton(parent=self.gridLayoutWidget_15)
        self.pushButton_save_config.setObjectName("pushButton_save_config")
        self.gridLayout_15.addWidget(self.pushButton_save_config, 1, 1, 1, 1)
        self.label_50 = QtWidgets.QLabel(parent=self.groupBox_2)
        self.label_50.setGeometry(QtCore.QRect(90, 100, 361, 31))
        self.label_50.setStyleSheet("color: rgb(255, 0, 0);")
        self.label_50.setObjectName("label_50")
        self.gridLayoutWidget_14 = QtWidgets.QWidget(parent=self.groupBox_2)
        self.gridLayoutWidget_14.setGeometry(QtCore.QRect(110, 130, 171, 131))
        self.gridLayoutWidget_14.setObjectName("gridLayoutWidget_14")
        self.gridLayout_14 = QtWidgets.QGridLayout(self.gridLayoutWidget_14)
        self.gridLayout_14.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_14.setObjectName("gridLayout_14")
        self.checkBox_is_uninstall_app = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_14)
        self.checkBox_is_uninstall_app.setObjectName("checkBox_is_uninstall_app")
        self.gridLayout_14.addWidget(self.checkBox_is_uninstall_app, 1, 0, 1, 1)
        self.checkBox_change_title = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_14)
        self.checkBox_change_title.setObjectName("checkBox_change_title")
        self.gridLayout_14.addWidget(self.checkBox_change_title, 3, 0, 1, 1)
        self.checkBox_update_ins = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_14)
        self.checkBox_update_ins.setObjectName("checkBox_update_ins")
        self.gridLayout_14.addWidget(self.checkBox_update_ins, 2, 0, 1, 1)
        self.checkBox_update_node = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_14)
        self.checkBox_update_node.setObjectName("checkBox_update_node")
        self.gridLayout_14.addWidget(self.checkBox_update_node, 0, 0, 1, 1)
        self.checkBox_default_font_size = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_14)
        self.checkBox_default_font_size.setObjectName("checkBox_default_font_size")
        self.gridLayout_14.addWidget(self.checkBox_default_font_size, 4, 0, 1, 1)
        self.layoutWidget = QtWidgets.QWidget(parent=self.groupBox_2)
        self.layoutWidget.setGeometry(QtCore.QRect(300, 230, 351, 31))
        self.layoutWidget.setObjectName("layoutWidget")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.layoutWidget)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.lineEdit_title_text = QtWidgets.QLineEdit(parent=self.layoutWidget)
        self.lineEdit_title_text.setObjectName("lineEdit_title_text")
        self.horizontalLayout.addWidget(self.lineEdit_title_text)
        self.pushButton_select_title_text = QtWidgets.QPushButton(parent=self.layoutWidget)
        self.pushButton_select_title_text.setObjectName("pushButton_select_title_text")
        self.horizontalLayout.addWidget(self.pushButton_select_title_text)
        self.label_53 = QtWidgets.QLabel(parent=self.layoutWidget)
        self.label_53.setObjectName("label_53")
        self.horizontalLayout.addWidget(self.label_53)
        self.pushButton_update_title = QtWidgets.QPushButton(parent=self.layoutWidget)
        self.pushButton_update_title.setObjectName("pushButton_update_title")
        self.horizontalLayout.addWidget(self.pushButton_update_title)
        self.pushButton_update_remark = QtWidgets.QPushButton(parent=self.layoutWidget)
        self.pushButton_update_remark.setObjectName("pushButton_update_remark")
        self.horizontalLayout.addWidget(self.pushButton_update_remark)
        self.gridLayoutWidget_16 = QtWidgets.QWidget(parent=self.tab_4)
        self.gridLayoutWidget_16.setGeometry(QtCore.QRect(700, 70, 471, 171))
        self.gridLayoutWidget_16.setObjectName("gridLayoutWidget_16")
        self.gridLayout_16 = QtWidgets.QGridLayout(self.gridLayoutWidget_16)
        self.gridLayout_16.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_16.setObjectName("gridLayout_16")
        self.label_49 = QtWidgets.QLabel(parent=self.gridLayoutWidget_16)
        self.label_49.setObjectName("label_49")
        self.gridLayout_16.addWidget(self.label_49, 2, 5, 1, 1)
        self.lineEdit_8 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_16)
        self.lineEdit_8.setObjectName("lineEdit_8")
        self.gridLayout_16.addWidget(self.lineEdit_8, 2, 4, 1, 1)
        self.lineEdit_INS_timeout1 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_16)
        self.lineEdit_INS_timeout1.setObjectName("lineEdit_INS_timeout1")
        self.gridLayout_16.addWidget(self.lineEdit_INS_timeout1, 2, 1, 1, 1)
        self.label_46 = QtWidgets.QLabel(parent=self.gridLayoutWidget_16)
        self.label_46.setObjectName("label_46")
        self.gridLayout_16.addWidget(self.label_46, 1, 3, 1, 1)
        self.label_47 = QtWidgets.QLabel(parent=self.gridLayoutWidget_16)
        self.label_47.setObjectName("label_47")
        self.gridLayout_16.addWidget(self.label_47, 1, 5, 1, 1)
        self.label_44 = QtWidgets.QLabel(parent=self.gridLayoutWidget_16)
        self.label_44.setObjectName("label_44")
        self.gridLayout_16.addWidget(self.label_44, 2, 0, 1, 1)
        self.lineEdit_7 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_16)
        self.lineEdit_7.setObjectName("lineEdit_7")
        self.gridLayout_16.addWidget(self.lineEdit_7, 1, 4, 1, 1)
        self.lineEdit_9 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_16)
        self.lineEdit_9.setObjectName("lineEdit_9")
        self.gridLayout_16.addWidget(self.lineEdit_9, 1, 6, 1, 1)
        self.label_48 = QtWidgets.QLabel(parent=self.gridLayoutWidget_16)
        self.label_48.setObjectName("label_48")
        self.gridLayout_16.addWidget(self.label_48, 2, 3, 1, 1)
        self.lineEdit_total_timeout = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_16)
        self.lineEdit_total_timeout.setObjectName("lineEdit_total_timeout")
        self.gridLayout_16.addWidget(self.lineEdit_total_timeout, 0, 1, 1, 1)
        self.label_45 = QtWidgets.QLabel(parent=self.gridLayoutWidget_16)
        self.label_45.setObjectName("label_45")
        self.gridLayout_16.addWidget(self.label_45, 3, 0, 1, 1)
        self.lineEdit_INS_timeout2 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_16)
        self.lineEdit_INS_timeout2.setObjectName("lineEdit_INS_timeout2")
        self.gridLayout_16.addWidget(self.lineEdit_INS_timeout2, 3, 1, 1, 1)
        self.label_42 = QtWidgets.QLabel(parent=self.gridLayoutWidget_16)
        self.label_42.setObjectName("label_42")
        self.gridLayout_16.addWidget(self.label_42, 0, 0, 1, 1)
        self.label_43 = QtWidgets.QLabel(parent=self.gridLayoutWidget_16)
        self.label_43.setObjectName("label_43")
        self.gridLayout_16.addWidget(self.label_43, 1, 0, 1, 1)
        self.lineEdit_v2ray_timeout = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_16)
        self.lineEdit_v2ray_timeout.setObjectName("lineEdit_v2ray_timeout")
        self.gridLayout_16.addWidget(self.lineEdit_v2ray_timeout, 1, 1, 1, 1)
        self.label_51 = QtWidgets.QLabel(parent=self.gridLayoutWidget_16)
        self.label_51.setObjectName("label_51")
        self.gridLayout_16.addWidget(self.label_51, 1, 7, 1, 1)
        self.lineEdit_10 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_16)
        self.lineEdit_10.setObjectName("lineEdit_10")
        self.gridLayout_16.addWidget(self.lineEdit_10, 2, 6, 1, 1)
        self.label_52 = QtWidgets.QLabel(parent=self.gridLayoutWidget_16)
        self.label_52.setObjectName("label_52")
        self.gridLayout_16.addWidget(self.label_52, 2, 7, 1, 1)
        self.lineEdit_node_client = QtWidgets.QLineEdit(parent=self.tab_4)
        self.lineEdit_node_client.setGeometry(QtCore.QRect(760, 20, 401, 31))
        self.lineEdit_node_client.setObjectName("lineEdit_node_client")
        self.label_40 = QtWidgets.QLabel(parent=self.tab_4)
        self.label_40.setGeometry(QtCore.QRect(680, 24, 71, 21))
        self.label_40.setObjectName("label_40")
        self.pushButton_task_2 = QtWidgets.QPushButton(parent=self.tab_4)
        self.pushButton_task_2.setGeometry(QtCore.QRect(700, 250, 75, 24))
        self.pushButton_task_2.setObjectName("pushButton_task_2")
        self.tabWidget.addTab(self.tab_4, "")
        self.tab_6 = QtWidgets.QWidget()
        self.tab_6.setObjectName("tab_6")
        self.groupBox_11 = QtWidgets.QGroupBox(parent=self.tab_6)
        self.groupBox_11.setGeometry(QtCore.QRect(0, 10, 311, 271))
        self.groupBox_11.setObjectName("groupBox_11")
        self.gridLayoutWidget_18 = QtWidgets.QWidget(parent=self.groupBox_11)
        self.gridLayoutWidget_18.setGeometry(QtCore.QRect(10, 20, 291, 251))
        self.gridLayoutWidget_18.setObjectName("gridLayoutWidget_18")
        self.gridLayout_18 = QtWidgets.QGridLayout(self.gridLayoutWidget_18)
        self.gridLayout_18.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_18.setObjectName("gridLayout_18")
        self.label_58 = QtWidgets.QLabel(parent=self.gridLayoutWidget_18)
        self.label_58.setObjectName("label_58")
        self.gridLayout_18.addWidget(self.label_58, 0, 0, 1, 1)
        self.checkBox_is_blue = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_18)
        self.checkBox_is_blue.setChecked(True)
        self.checkBox_is_blue.setObjectName("checkBox_is_blue")
        self.gridLayout_18.addWidget(self.checkBox_is_blue, 4, 0, 1, 1)
        self.label_61 = QtWidgets.QLabel(parent=self.gridLayoutWidget_18)
        self.label_61.setObjectName("label_61")
        self.gridLayout_18.addWidget(self.label_61, 3, 2, 1, 1)
        self.label_60 = QtWidgets.QLabel(parent=self.gridLayoutWidget_18)
        self.label_60.setObjectName("label_60")
        self.gridLayout_18.addWidget(self.label_60, 3, 0, 1, 1)
        self.lineEdit_follow_Num_2 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_18)
        self.lineEdit_follow_Num_2.setObjectName("lineEdit_follow_Num_2")
        self.gridLayout_18.addWidget(self.lineEdit_follow_Num_2, 1, 1, 1, 1)
        self.label_59 = QtWidgets.QLabel(parent=self.gridLayoutWidget_18)
        self.label_59.setObjectName("label_59")
        self.gridLayout_18.addWidget(self.label_59, 1, 0, 1, 1)
        self.lineEdit_follow_Num_1 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_18)
        self.lineEdit_follow_Num_1.setObjectName("lineEdit_follow_Num_1")
        self.gridLayout_18.addWidget(self.lineEdit_follow_Num_1, 0, 1, 1, 1)
        self.lineEdit_min_followers = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_18)
        self.lineEdit_min_followers.setObjectName("lineEdit_min_followers")
        self.gridLayout_18.addWidget(self.lineEdit_min_followers, 3, 1, 1, 1)
        self.checkBox_is_private = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_18)
        self.checkBox_is_private.setChecked(True)
        self.checkBox_is_private.setObjectName("checkBox_is_private")
        self.gridLayout_18.addWidget(self.checkBox_is_private, 4, 1, 1, 1)
        self.label_69 = QtWidgets.QLabel(parent=self.gridLayoutWidget_18)
        self.label_69.setObjectName("label_69")
        self.gridLayout_18.addWidget(self.label_69, 2, 0, 1, 1)
        self.lineEdit_send_msg_Num = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_18)
        self.lineEdit_send_msg_Num.setObjectName("lineEdit_send_msg_Num")
        self.gridLayout_18.addWidget(self.lineEdit_send_msg_Num, 2, 1, 1, 1)
        self.groupBox_12 = QtWidgets.QGroupBox(parent=self.tab_6)
        self.groupBox_12.setGeometry(QtCore.QRect(320, 10, 351, 271))
        self.groupBox_12.setObjectName("groupBox_12")
        self.gridLayoutWidget_19 = QtWidgets.QWidget(parent=self.groupBox_12)
        self.gridLayoutWidget_19.setGeometry(QtCore.QRect(10, 20, 331, 251))
        self.gridLayoutWidget_19.setObjectName("gridLayoutWidget_19")
        self.gridLayout_19 = QtWidgets.QGridLayout(self.gridLayoutWidget_19)
        self.gridLayout_19.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_19.setObjectName("gridLayout_19")
        self.lineEdit_delay_4 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_delay_4.setObjectName("lineEdit_delay_4")
        self.gridLayout_19.addWidget(self.lineEdit_delay_4, 1, 2, 1, 1)
        self.lineEdit_delay_1 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_delay_1.setObjectName("lineEdit_delay_1")
        self.gridLayout_19.addWidget(self.lineEdit_delay_1, 0, 1, 1, 1)
        self.lineEdit_delay_5 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_delay_5.setObjectName("lineEdit_delay_5")
        self.gridLayout_19.addWidget(self.lineEdit_delay_5, 5, 1, 1, 1)
        self.lineEdit_scroll_timeout = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_scroll_timeout.setObjectName("lineEdit_scroll_timeout")
        self.gridLayout_19.addWidget(self.lineEdit_scroll_timeout, 4, 1, 1, 1)
        self.label_64 = QtWidgets.QLabel(parent=self.gridLayoutWidget_19)
        self.label_64.setObjectName("label_64")
        self.gridLayout_19.addWidget(self.label_64, 3, 0, 1, 1)
        self.label_66 = QtWidgets.QLabel(parent=self.gridLayoutWidget_19)
        self.label_66.setObjectName("label_66")
        self.gridLayout_19.addWidget(self.label_66, 5, 0, 1, 1)
        self.lineEdit_page_load_timeout = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_page_load_timeout.setObjectName("lineEdit_page_load_timeout")
        self.gridLayout_19.addWidget(self.lineEdit_page_load_timeout, 3, 1, 1, 1)
        self.label_62 = QtWidgets.QLabel(parent=self.gridLayoutWidget_19)
        self.label_62.setObjectName("label_62")
        self.gridLayout_19.addWidget(self.label_62, 0, 0, 1, 1)
        self.lineEdit_follow_xiuxi2 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_follow_xiuxi2.setObjectName("lineEdit_follow_xiuxi2")
        self.gridLayout_19.addWidget(self.lineEdit_follow_xiuxi2, 2, 2, 1, 1)
        self.lineEdit_delay_3 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_delay_3.setObjectName("lineEdit_delay_3")
        self.gridLayout_19.addWidget(self.lineEdit_delay_3, 1, 1, 1, 1)
        self.label_65 = QtWidgets.QLabel(parent=self.gridLayoutWidget_19)
        self.label_65.setObjectName("label_65")
        self.gridLayout_19.addWidget(self.label_65, 4, 0, 1, 1)
        self.lineEdit_delay_2 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_delay_2.setObjectName("lineEdit_delay_2")
        self.gridLayout_19.addWidget(self.lineEdit_delay_2, 0, 2, 1, 1)
        self.label_68 = QtWidgets.QLabel(parent=self.gridLayoutWidget_19)
        self.label_68.setObjectName("label_68")
        self.gridLayout_19.addWidget(self.label_68, 2, 0, 1, 1)
        self.lineEdit_delay_6 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_delay_6.setObjectName("lineEdit_delay_6")
        self.gridLayout_19.addWidget(self.lineEdit_delay_6, 5, 2, 1, 1)
        self.lineEdit_follow_xiuxi1 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_follow_xiuxi1.setObjectName("lineEdit_follow_xiuxi1")
        self.gridLayout_19.addWidget(self.lineEdit_follow_xiuxi1, 2, 1, 1, 1)
        self.label_63 = QtWidgets.QLabel(parent=self.gridLayoutWidget_19)
        self.label_63.setObjectName("label_63")
        self.gridLayout_19.addWidget(self.label_63, 1, 0, 1, 1)
        self.lineEdit_follow_xiuxi3 = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_19)
        self.lineEdit_follow_xiuxi3.setObjectName("lineEdit_follow_xiuxi3")
        self.gridLayout_19.addWidget(self.lineEdit_follow_xiuxi3, 2, 3, 1, 1)
        self.groupBox_13 = QtWidgets.QGroupBox(parent=self.tab_6)
        self.groupBox_13.setGeometry(QtCore.QRect(690, 20, 131, 271))
        self.groupBox_13.setObjectName("groupBox_13")
        self.gridLayoutWidget_20 = QtWidgets.QWidget(parent=self.groupBox_13)
        self.gridLayoutWidget_20.setGeometry(QtCore.QRect(10, 20, 111, 241))
        self.gridLayoutWidget_20.setObjectName("gridLayoutWidget_20")
        self.gridLayout_20 = QtWidgets.QGridLayout(self.gridLayoutWidget_20)
        self.gridLayout_20.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_20.setObjectName("gridLayout_20")
        self.checkBox_regions_1 = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_20)
        self.checkBox_regions_1.setObjectName("checkBox_regions_1")
        self.gridLayout_20.addWidget(self.checkBox_regions_1, 0, 0, 1, 1)
        self.checkBox_regions_3 = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_20)
        self.checkBox_regions_3.setObjectName("checkBox_regions_3")
        self.gridLayout_20.addWidget(self.checkBox_regions_3, 2, 0, 1, 1)
        self.checkBox_regions_2 = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_20)
        self.checkBox_regions_2.setObjectName("checkBox_regions_2")
        self.gridLayout_20.addWidget(self.checkBox_regions_2, 1, 0, 1, 1)
        self.checkBox_regions_4 = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_20)
        self.checkBox_regions_4.setObjectName("checkBox_regions_4")
        self.gridLayout_20.addWidget(self.checkBox_regions_4, 3, 0, 1, 1)
        self.checkBox_regions_5 = QtWidgets.QCheckBox(parent=self.gridLayoutWidget_20)
        self.checkBox_regions_5.setObjectName("checkBox_regions_5")
        self.gridLayout_20.addWidget(self.checkBox_regions_5, 4, 0, 1, 1)
        self.groupBox_14 = QtWidgets.QGroupBox(parent=self.tab_6)
        self.groupBox_14.setGeometry(QtCore.QRect(850, 10, 321, 211))
        self.groupBox_14.setObjectName("groupBox_14")
        self.gridLayoutWidget_21 = QtWidgets.QWidget(parent=self.groupBox_14)
        self.gridLayoutWidget_21.setGeometry(QtCore.QRect(10, 20, 301, 41))
        self.gridLayoutWidget_21.setObjectName("gridLayoutWidget_21")
        self.gridLayout_21 = QtWidgets.QGridLayout(self.gridLayoutWidget_21)
        self.gridLayout_21.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_21.setObjectName("gridLayout_21")
        self.lineEdit_target_user_path = QtWidgets.QLineEdit(parent=self.gridLayoutWidget_21)
        self.lineEdit_target_user_path.setObjectName("lineEdit_target_user_path")
        self.gridLayout_21.addWidget(self.lineEdit_target_user_path, 0, 1, 1, 1)
        self.label_67 = QtWidgets.QLabel(parent=self.gridLayoutWidget_21)
        self.label_67.setObjectName("label_67")
        self.gridLayout_21.addWidget(self.label_67, 0, 0, 1, 1)
        self.pushButton_select_target_user_path = QtWidgets.QPushButton(parent=self.gridLayoutWidget_21)
        self.pushButton_select_target_user_path.setObjectName("pushButton_select_target_user_path")
        self.gridLayout_21.addWidget(self.pushButton_select_target_user_path, 0, 2, 1, 1)
        self.gridLayoutWidget_22 = QtWidgets.QWidget(parent=self.groupBox_14)
        self.gridLayoutWidget_22.setGeometry(QtCore.QRect(10, 70, 301, 71))
        self.gridLayoutWidget_22.setObjectName("gridLayoutWidget_22")
        self.gridLayout_22 = QtWidgets.QGridLayout(self.gridLayoutWidget_22)
        self.gridLayout_22.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_22.setObjectName("gridLayout_22")
        self.radioButton_task_follow_1 = QtWidgets.QRadioButton(parent=self.gridLayoutWidget_22)
        self.radioButton_task_follow_1.setObjectName("radioButton_task_follow_1")
        self.gridLayout_22.addWidget(self.radioButton_task_follow_1, 1, 0, 1, 1)
        self.radioButton_task_follow_3 = QtWidgets.QRadioButton(parent=self.gridLayoutWidget_22)
        self.radioButton_task_follow_3.setObjectName("radioButton_task_follow_3")
        self.gridLayout_22.addWidget(self.radioButton_task_follow_3, 1, 2, 1, 1)
        self.radioButton_task_follow_2 = QtWidgets.QRadioButton(parent=self.gridLayoutWidget_22)
        self.radioButton_task_follow_2.setObjectName("radioButton_task_follow_2")
        self.gridLayout_22.addWidget(self.radioButton_task_follow_2, 1, 1, 1, 1)
        self.radioButton_task_1 = QtWidgets.QRadioButton(parent=self.gridLayoutWidget_22)
        self.radioButton_task_1.setObjectName("radioButton_task_1")
        self.gridLayout_22.addWidget(self.radioButton_task_1, 0, 0, 1, 1)
        self.radioButton_task_2 = QtWidgets.QRadioButton(parent=self.gridLayoutWidget_22)
        self.radioButton_task_2.setObjectName("radioButton_task_2")
        self.gridLayout_22.addWidget(self.radioButton_task_2, 0, 1, 1, 1)
        self.gridLayoutWidget_23 = QtWidgets.QWidget(parent=self.groupBox_14)
        self.gridLayoutWidget_23.setGeometry(QtCore.QRect(10, 150, 301, 41))
        self.gridLayoutWidget_23.setObjectName("gridLayoutWidget_23")
        self.gridLayout_23 = QtWidgets.QGridLayout(self.gridLayoutWidget_23)
        self.gridLayout_23.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_23.setObjectName("gridLayout_23")
        self.pushButton_task_follow = QtWidgets.QPushButton(parent=self.gridLayoutWidget_23)
        self.pushButton_task_follow.setObjectName("pushButton_task_follow")
        self.gridLayout_23.addWidget(self.pushButton_task_follow, 0, 0, 1, 1)
        self.pushButton_stop_task = QtWidgets.QPushButton(parent=self.gridLayoutWidget_23)
        self.pushButton_stop_task.setObjectName("pushButton_stop_task")
        self.gridLayout_23.addWidget(self.pushButton_stop_task, 0, 1, 1, 1)
        self.pushButton_save_config_2 = QtWidgets.QPushButton(parent=self.gridLayoutWidget_23)
        self.pushButton_save_config_2.setObjectName("pushButton_save_config_2")
        self.gridLayout_23.addWidget(self.pushButton_save_config_2, 0, 2, 1, 1)
        self.tabWidget.addTab(self.tab_6, "")
        self.tab_8 = QtWidgets.QWidget()
        self.tab_8.setObjectName("tab_8")
        self.groupBox_16 = QtWidgets.QGroupBox(parent=self.tab_8)
        self.groupBox_16.setGeometry(QtCore.QRect(10, 10, 561, 271))
        self.groupBox_16.setObjectName("groupBox_16")
        self.plainTextEdit_send_messages = QtWidgets.QPlainTextEdit(parent=self.groupBox_16)
        self.plainTextEdit_send_messages.setGeometry(QtCore.QRect(10, 50, 541, 211))
        self.plainTextEdit_send_messages.setObjectName("plainTextEdit_send_messages")
        self.label_70 = QtWidgets.QLabel(parent=self.groupBox_16)
        self.label_70.setGeometry(QtCore.QRect(10, 20, 461, 21))
        self.label_70.setObjectName("label_70")
        self.tabWidget.addTab(self.tab_8, "")
        self.verticalLayout.addWidget(self.tabWidget)
        self.verticalLayout.setStretch(0, 8)
        self.verticalLayout.setStretch(1, 5)
        self.verticalLayout_2.addLayout(self.verticalLayout)
        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QtWidgets.QStatusBar(parent=MainWindow)
        font = QtGui.QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(10)
        self.statusbar.setFont(font)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)
        self.tabWidget.setCurrentIndex(5)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "雷电"))
        item = self.tableWidget.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "选中"))
        item = self.tableWidget.horizontalHeaderItem(1)
        item.setText(_translate("MainWindow", "索引"))
        item = self.tableWidget.horizontalHeaderItem(2)
        item.setText(_translate("MainWindow", "标题"))
        item = self.tableWidget.horizontalHeaderItem(3)
        item.setText(_translate("MainWindow", "顶层句柄"))
        item = self.tableWidget.horizontalHeaderItem(4)
        item.setText(_translate("MainWindow", "绑定句柄"))
        item = self.tableWidget.horizontalHeaderItem(5)
        item.setText(_translate("MainWindow", "PID"))
        item = self.tableWidget.horizontalHeaderItem(6)
        item.setText(_translate("MainWindow", "窗口状态"))
        item = self.tableWidget.horizontalHeaderItem(7)
        item.setText(_translate("MainWindow", "状态"))
        item = self.tableWidget.horizontalHeaderItem(8)
        item.setText(_translate("MainWindow", "日志"))
        self.label_20.setText(_translate("MainWindow", "  模拟器路径   "))
        self.pushButton_emulator_path_Browse.setText(_translate("MainWindow", "浏览"))
        self.pushButton_share_path_Browse.setText(_translate("MainWindow", "浏览"))
        self.label_21.setText(_translate("MainWindow", "  共享路径"))
        self.label_37.setText(_translate("MainWindow", " 截图保存路径  "))
        self.pushButton_share_path_Browse_2.setText(_translate("MainWindow", "浏览"))
        self.label_30.setText(_translate("MainWindow", "  并发最大线程:  "))
        self.label_34.setText(_translate("MainWindow", "  任务执行间隔:  "))
        self.label_31.setText(_translate("MainWindow", "  模拟器启动间隔:  "))
        self.label_33.setText(_translate("MainWindow", "秒"))
        self.label_36.setText(_translate("MainWindow", "秒"))
        self.label_24.setText(_translate("MainWindow", "  任务超时检测 "))
        self.label_25.setText(_translate("MainWindow", "秒  "))
        self.lineEdit_start_delay.setText(_translate("MainWindow", "15"))
        self.label_26.setText(_translate("MainWindow", " 窗口高度:"))
        self.label_27.setText(_translate("MainWindow", " 列间距"))
        self.label_19.setText(_translate("MainWindow", " 窗口宽度:"))
        self.lineEdit_wnd_width.setText(_translate("MainWindow", "360"))
        self.lineEdit_wnd_column_spacing.setText(_translate("MainWindow", "100"))
        self.lineEdit_wnd_height.setText(_translate("MainWindow", "540"))
        self.label_35.setText(_translate("MainWindow", " 行间距"))
        self.lineEdit_wnd_row_spacing.setText(_translate("MainWindow", "100"))
        self.pushButton_sortWnd.setText(_translate("MainWindow", "窗口排序"))
        self.label_39.setText(_translate("MainWindow", " WebSocket_Port:"))
        self.label_38.setText(_translate("MainWindow", " WebSocket_IP:"))
        self.lineEdit.setText(_translate("MainWindow", "*************"))
        self.lineEdit_2.setText(_translate("MainWindow", "13142"))
        self.pushButton_8.setText(_translate("MainWindow", " 开启WS服务"))
        self.checkBox_ws.setText(_translate("MainWindow", "自动开启WS"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_5), _translate("MainWindow", "配置"))
        self.lineEdit_emulator_num2.setText(_translate("MainWindow", "1"))
        self.pushButton_copy_emulator.setText(_translate("MainWindow", "复制模拟器"))
        self.pushButton_add.setText(_translate("MainWindow", "  新建模拟器  "))
        self.lineEdit_emulator_num.setText(_translate("MainWindow", "1"))
        self.label_16.setText(_translate("MainWindow", "新建模拟器延迟"))
        self.label_12.setText(_translate("MainWindow", "数    量"))
        self.label_13.setText(_translate("MainWindow", "数量"))
        self.label_14.setText(_translate("MainWindow", "   个"))
        self.label_15.setText(_translate("MainWindow", "   个"))
        self.lineEdit_emulator_delay.setText(_translate("MainWindow", "10"))
        self.label_17.setText(_translate("MainWindow", "   秒"))
        self.label_18.setText(_translate("MainWindow", "    索引    "))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), _translate("MainWindow", "设置1"))
        self.groupBox_6.setTitle(_translate("MainWindow", "GroupBox"))
        self.lineEdit_emulator_file_path.setText(_translate("MainWindow", "/sdcard"))
        self.label_28.setText(_translate("MainWindow", " 模拟器文件路径 "))
        self.label_29.setText(_translate("MainWindow", " 电脑文件路径 "))
        self.lineEdit_host_file_path.setText(_translate("MainWindow", "D:/LDPlayer9.0.66/Pictures"))
        self.pushButton_randowm_name.setText(_translate("MainWindow", "随机名"))
        self.pushButton_9.setText(_translate("MainWindow", "随机名"))
        self.pushButton_pull_file.setText(_translate("MainWindow", "复制文件到电脑"))
        self.pushButton_push_file.setText(_translate("MainWindow", "发送文件到模拟器"))
        self.pushButton_5.setText(_translate("MainWindow", "PushButton"))
        self.pushButton_6.setText(_translate("MainWindow", "PushButton"))
        self.pushButton.setText(_translate("MainWindow", "获取分辨率"))
        self.pushButton_root_open.setText(_translate("MainWindow", "开启: 旋转/锁定/root"))
        self.pushButton_root_close.setText(_translate("MainWindow", "关闭: 旋转/锁定/root"))
        self.pushButton_2.setText(_translate("MainWindow", "获取包名列表"))
        self.groupBox_3.setTitle(_translate("MainWindow", "模拟器配置"))
        self.lineEdit_Resolution_weight.setText(_translate("MainWindow", "720"))
        self.lineEdit_Resolution_height.setText(_translate("MainWindow", "1080"))
        self.label.setText(_translate("MainWindow", "分辨率:"))
        self.label_2.setText(_translate("MainWindow", "CPU"))
        self.label_3.setText(_translate("MainWindow", " 内存        "))
        self.pushButton_modifyResolution.setText(_translate("MainWindow", "修改分辨率"))
        self.pushButton_modifyPhone.setText(_translate("MainWindow", "修改手机配置"))
        self.pushButton_modifyCPU.setText(_translate("MainWindow", "修改模拟器配置"))
        self.pushButton_random_device.setText(_translate("MainWindow", "随机生成配置"))
        self.groupBox_4.setTitle(_translate("MainWindow", "手机配置"))
        self.label_4.setText(_translate("MainWindow", "手机厂商"))
        self.label_10.setText(_translate("MainWindow", "安卓ID"))
        self.label_5.setText(_translate("MainWindow", "手机号码"))
        self.label_6.setText(_translate("MainWindow", "手机型号"))
        self.label_7.setText(_translate("MainWindow", "IMEI"))
        self.label_8.setText(_translate("MainWindow", "IMSI"))
        self.label_9.setText(_translate("MainWindow", "Sim序列号"))
        self.label_11.setText(_translate("MainWindow", "Mac"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_3), _translate("MainWindow", "文件操作"))
        self.lineEdit_App_pkname.setText(_translate("MainWindow", "tv.danmaku.bili"))
        self.label_23.setText(_translate("MainWindow", "ApK 包名:"))
        self.pushButton_App_UnInstall.setText(_translate("MainWindow", "卸载App"))
        self.pushButton_App_Browse.setText(_translate("MainWindow", "浏览"))
        self.label_22.setText(_translate("MainWindow", "ApK 路径:"))
        self.pushButton_App_Install.setText(_translate("MainWindow", "安装App"))
        self.label_41.setText(_translate("MainWindow", "上次运行到   :"))
        self.pushButton_tiaozhuan.setText(_translate("MainWindow", " 跳至该行 "))
        self.pushButton_App_launch.setText(_translate("MainWindow", "启动App"))
        self.pushButton_App_kill.setText(_translate("MainWindow", "终止App"))
        self.groupBox_10.setTitle(_translate("MainWindow", "GroupBox"))
        self.pushButton_start_task.setText(_translate("MainWindow", "启动"))
        self.pushButton_export.setText(_translate("MainWindow", "导出状态"))
        self.pushButton_save_config.setText(_translate("MainWindow", "保存配置"))
        self.label_50.setText(_translate("MainWindow", "  安装/卸载  支持多文件,路径和包名数量须匹配, 以 | 分割"))
        self.checkBox_is_uninstall_app.setText(_translate("MainWindow", "重装 V2ray"))
        self.checkBox_change_title.setText(_translate("MainWindow", "是否修改标题"))
        self.checkBox_update_ins.setText(_translate("MainWindow", "更新INS"))
        self.checkBox_update_node.setText(_translate("MainWindow", "更新订阅"))
        self.checkBox_default_font_size.setText(_translate("MainWindow", "调整系统默认字体大小"))
        self.pushButton_select_title_text.setText(_translate("MainWindow", "选择文本"))
        self.label_53.setText(_translate("MainWindow", "   |   "))
        self.pushButton_update_title.setText(_translate("MainWindow", "改标题"))
        self.pushButton_update_remark.setText(_translate("MainWindow", "改备注"))
        self.label_49.setText(_translate("MainWindow", "   重启    "))
        self.lineEdit_INS_timeout1.setText(_translate("MainWindow", "60"))
        self.label_46.setText(_translate("MainWindow", "   失败    "))
        self.label_47.setText(_translate("MainWindow", "   重启    "))
        self.label_44.setText(_translate("MainWindow", "INS首页等待超时:"))
        self.label_48.setText(_translate("MainWindow", "   失败    "))
        self.lineEdit_total_timeout.setText(_translate("MainWindow", "900"))
        self.label_45.setText(_translate("MainWindow", "INS状态等待超时:"))
        self.lineEdit_INS_timeout2.setText(_translate("MainWindow", "30"))
        self.label_42.setText(_translate("MainWindow", "总任务超时时间: "))
        self.label_43.setText(_translate("MainWindow", "V2ray总超时时间: "))
        self.lineEdit_v2ray_timeout.setText(_translate("MainWindow", "500"))
        self.label_51.setText(_translate("MainWindow", "   重启    "))
        self.label_52.setText(_translate("MainWindow", "   重启    "))
        self.label_40.setText(_translate("MainWindow", "订阅地址:"))
        self.pushButton_task_2.setText(_translate("MainWindow", "任务2"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_4), _translate("MainWindow", "定制功能"))
        self.groupBox_11.setTitle(_translate("MainWindow", "任务数量设置"))
        self.label_58.setText(_translate("MainWindow", "  直接用户关注数量:"))
        self.checkBox_is_blue.setText(_translate("MainWindow", "蓝V用户跳过"))
        self.label_61.setText(_translate("MainWindow", "跳过 "))
        self.label_60.setText(_translate("MainWindow", "  粉丝数低于:"))
        self.lineEdit_follow_Num_2.setText(_translate("MainWindow", "50"))
        self.label_59.setText(_translate("MainWindow", "  用户粉丝关注数量:"))
        self.lineEdit_follow_Num_1.setText(_translate("MainWindow", "100"))
        self.lineEdit_min_followers.setText(_translate("MainWindow", "100"))
        self.checkBox_is_private.setText(_translate("MainWindow", "私密用户跳过"))
        self.label_69.setText(_translate("MainWindow", "  私信任务数量:"))
        self.lineEdit_send_msg_Num.setText(_translate("MainWindow", "50"))
        self.groupBox_12.setTitle(_translate("MainWindow", "延时参数设置"))
        self.lineEdit_delay_4.setText(_translate("MainWindow", "15"))
        self.lineEdit_delay_1.setText(_translate("MainWindow", "5"))
        self.lineEdit_delay_5.setText(_translate("MainWindow", "3"))
        self.lineEdit_scroll_timeout.setText(_translate("MainWindow", "100"))
        self.label_64.setText(_translate("MainWindow", " 用户资料页加载超时"))
        self.label_66.setText(_translate("MainWindow", " 通用延迟"))
        self.lineEdit_page_load_timeout.setText(_translate("MainWindow", "30"))
        self.label_62.setText(_translate("MainWindow", " 切换用户延迟"))
        self.lineEdit_follow_xiuxi2.setText(_translate("MainWindow", "10"))
        self.lineEdit_delay_3.setText(_translate("MainWindow", "5"))
        self.label_65.setText(_translate("MainWindow", " 粉丝列表页滑动超时"))
        self.lineEdit_delay_2.setText(_translate("MainWindow", "15"))
        self.label_68.setText(_translate("MainWindow", " 关注X个后休息"))
        self.lineEdit_delay_6.setText(_translate("MainWindow", "6"))
        self.lineEdit_follow_xiuxi1.setText(_translate("MainWindow", "10"))
        self.label_63.setText(_translate("MainWindow", " 关注延迟"))
        self.lineEdit_follow_xiuxi3.setText(_translate("MainWindow", "20"))
        self.groupBox_13.setTitle(_translate("MainWindow", "用户地区选择"))
        self.checkBox_regions_1.setText(_translate("MainWindow", "中国"))
        self.checkBox_regions_3.setText(_translate("MainWindow", "韩国"))
        self.checkBox_regions_2.setText(_translate("MainWindow", "日本"))
        self.checkBox_regions_4.setText(_translate("MainWindow", "泰国"))
        self.checkBox_regions_5.setText(_translate("MainWindow", "通用"))
        self.groupBox_14.setTitle(_translate("MainWindow", "调试"))
        self.label_67.setText(_translate("MainWindow", "目标用户文本选择"))
        self.pushButton_select_target_user_path.setText(_translate("MainWindow", "选择"))
        self.radioButton_task_follow_1.setText(_translate("MainWindow", "关注目标"))
        self.radioButton_task_follow_3.setText(_translate("MainWindow", "私信"))
        self.radioButton_task_follow_2.setText(_translate("MainWindow", "关注目标粉丝"))
        self.radioButton_task_1.setText(_translate("MainWindow", "任务1"))
        self.radioButton_task_2.setText(_translate("MainWindow", "任务2"))
        self.pushButton_task_follow.setText(_translate("MainWindow", "开始任务"))
        self.pushButton_stop_task.setText(_translate("MainWindow", "停止"))
        self.pushButton_save_config_2.setText(_translate("MainWindow", "保存配置"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_6), _translate("MainWindow", "定制功能:关注"))
        self.groupBox_16.setTitle(_translate("MainWindow", "GroupBox"))
        self.plainTextEdit_send_messages.setPlainText(_translate("MainWindow", "hi|nihao|hello"))
        self.label_70.setText(_translate("MainWindow", "多条话术请使用 |  进行分割"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_8), _translate("MainWindow", "私信话术"))
