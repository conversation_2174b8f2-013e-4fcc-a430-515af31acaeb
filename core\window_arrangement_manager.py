#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 模拟器窗口排列管理器 - 重构版
========================================
功能描述: 专门负责模拟器窗口的自动排列和手动排列
主要方法: arrange_windows(), auto_arrange_on_startup()
调用关系: 被async_bridge调用，监听模拟器启动完成信号
注意事项:
- 只在模拟器完全启动后才进行排列
- 支持自动排列和手动排列两种模式
- 使用win32gui API直接操作窗口
- 智能空位填补算法，避免重复排列
========================================
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

from .simple_config import get_config_manager
from .logger_manager import log_info, log_error


# ========================================
# 🎯 窗口排列管理器主类 - 模拟器窗口自动和手动排列
# ========================================

class WindowArrangementManager(QObject):
    """🎯 模拟器窗口排列管理器 - 统一窗口排列逻辑"""

    # 🎯 信号定义
    arrangement_started = pyqtSignal()  # 开始排列
    arrangement_completed = pyqtSignal(dict)  # 排列完成，返回结果信息
    arrangement_failed = pyqtSignal(str)  # 排列失败，返回错误信息

    def __init__(self):
        super().__init__()
        self.config_manager = get_config_manager()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 🎯 排列状态管理
        self._is_arranging = False
        self._last_arrange_time = 0
        self._manual_arrange = False  # 是否为手动排列
        self._arrange_delay_timer = QTimer()
        self._arrange_delay_timer.setSingleShot(True)
        self._arrange_delay_timer.timeout.connect(self._execute_delayed_arrangement)

        # 🎯 等待启动完成的模拟器队列
        self._pending_emulators = set()  # 等待排列的模拟器ID集合
        
        log_info("窗口排列管理器初始化完成", component="WindowArrangementManager")

    # ========================================
    # 🎯 信号处理模块 - 模拟器启动完成信号处理
    # ========================================

    def on_emulator_startup_completed(self, emulator_id: int, row: int, task_type: str, result: dict):
        """🎯 处理模拟器启动完成信号 - 每个模拟器启动完成后立即单独排列"""
        try:
            # 🎯 强制输出调试信息到控制台
            print(f"[WindowArrangementManager] 收到模拟器启动信号: ID={emulator_id}, 任务类型={task_type}, 结果={result}")
            log_info(f"收到模拟器启动信号: ID={emulator_id}, 任务类型={task_type}, 结果={result}", component="WindowArrangementManager")

            # 只处理启动任务且成功的情况
            if task_type != 'start' or '成功' not in result.get('result', ''):
                print(f"[WindowArrangementManager] 跳过非启动任务或失败任务: 任务类型={task_type}, 结果={result.get('result', '')}")
                log_info(f"跳过非启动任务或失败任务: 任务类型={task_type}, 结果={result.get('result', '')}", component="WindowArrangementManager")
                return

            # 检查是否启用自动排列
            auto_arrange = self.config_manager.get("window_arrangement.auto_arrange_windows", True)
            print(f"[WindowArrangementManager] 自动排列配置: {auto_arrange}")
            if not auto_arrange:
                print(f"[WindowArrangementManager] 模拟器{emulator_id}启动完成，但自动排列已禁用")
                log_info(f"模拟器{emulator_id}启动完成，但自动排列已禁用", component="WindowArrangementManager")
                return

            print(f"[WindowArrangementManager] 模拟器{emulator_id}启动完成，立即触发窗口排列")
            log_info(f"模拟器{emulator_id}启动完成，立即触发窗口排列", component="WindowArrangementManager")

            # 🎯 每个模拟器启动完成后立即单独排列
            # 使用配置的延迟时间确保窗口完全加载，然后立即排列
            delay = self.config_manager.get("window_arrangement.arrange_delay", 2000)
            print(f"[WindowArrangementManager] 使用延迟 {delay} 毫秒后开始排列")
            QTimer.singleShot(delay, self.arrange_windows_async)

        except Exception as e:
            print(f"[WindowArrangementManager] 处理模拟器启动完成信号失败: {e}")
            log_error(f"处理模拟器启动完成信号失败: {e}", component="WindowArrangementManager")

    def _schedule_delayed_arrangement(self):
        """🎯 调度延迟排列 - 避免频繁触发"""
        # 获取延迟配置
        delay = self.config_manager.get("window_arrangement.arrange_delay", 2000)
        
        # 如果已经在等待，重新开始计时
        if self._arrange_delay_timer.isActive():
            self._arrange_delay_timer.stop()
            
        self._arrange_delay_timer.start(delay)
        log_info(f"调度延迟排列，延迟{delay}毫秒", component="WindowArrangementManager")

    def _execute_delayed_arrangement(self):
        """🎯 执行延迟排列 - 由QTimer触发"""
        try:
            if self._pending_emulators:
                pending_count = len(self._pending_emulators)
                log_info(f"执行延迟排列，等待排列的模拟器数量: {pending_count}", component="WindowArrangementManager")
                
                # 清空等待队列
                self._pending_emulators.clear()
                
                # 执行排列
                self.arrange_windows_async()
            else:
                log_info("没有等待排列的模拟器", component="WindowArrangementManager")
                
        except Exception as e:
            log_error(f"执行延迟排列失败: {e}", component="WindowArrangementManager")

    # ========================================
    # 🎯 排列执行模块 - 手动和自动排列执行
    # ========================================

    def arrange_windows_manual(self):
        """🎯 手动触发窗口排列 - 排列所有模拟器窗口（包括未运行的）"""
        try:
            print(f"[WindowArrangementManager] 手动触发窗口排列")
            log_info("手动触发窗口排列", component="WindowArrangementManager")
            # 🎯 手动排列时，排列所有模拟器窗口，不仅仅是运行中的
            self._manual_arrange = True
            self.arrange_windows_async()
        except Exception as e:
            print(f"[WindowArrangementManager] 手动排列窗口失败: {e}")
            log_error(f"手动排列窗口失败: {e}", component="WindowArrangementManager")
            self.arrangement_failed.emit(str(e))

    def arrange_windows_async(self):
        """🎯 异步执行窗口排列 - 每个模拟器启动完成后立即单独排列"""
        print(f"[WindowArrangementManager] arrange_windows_async 被调用")

        if self._is_arranging:
            print(f"[WindowArrangementManager] 窗口排列正在进行中，跳过本次请求")
            log_info("窗口排列正在进行中，跳过本次请求", component="WindowArrangementManager")
            return

        # 防止频繁排列
        current_time = time.time()
        min_interval = 1.0  # 最小间隔1秒
        if current_time - self._last_arrange_time < min_interval:
            print(f"[WindowArrangementManager] 排列间隔太短，跳过本次请求")
            log_info("排列间隔太短，跳过本次请求", component="WindowArrangementManager")
            return

        print(f"[WindowArrangementManager] 开始执行窗口排列")
        self._last_arrange_time = current_time
        self._is_arranging = True

        # 发送开始信号
        self.arrangement_started.emit()

        # 使用QTimer异步执行，避免阻塞
        QTimer.singleShot(0, self._perform_arrangement)

    def _perform_arrangement(self):
        """🎯 执行实际的窗口排列操作"""
        try:
            print(f"[WindowArrangementManager] _perform_arrangement 开始执行")
            log_info("开始执行窗口排列", component="WindowArrangementManager")
            
            # 获取配置参数
            window_width = self.config_manager.get("window_arrangement.window_width", None)
            window_height = self.config_manager.get("window_arrangement.window_height", None)
            column_spacing = self.config_manager.get("window_arrangement.column_spacing", 0)
            row_spacing = self.config_manager.get("window_arrangement.row_spacing", 0)
            windows_per_row = self.config_manager.get("window_arrangement.windows_per_row", 7)
            
            # 🎯 根据排列类型选择窗口获取方法
            if self._manual_arrange:
                # 手动排列：获取所有模拟器窗口
                hwnd_list = self._get_all_emulator_windows()
                window_type = "所有"
                self._manual_arrange = False  # 重置标志
            else:
                # 自动排列：只获取正在运行的模拟器窗口
                hwnd_list = self._get_running_emulator_windows()
                window_type = "正在运行的"

            if not hwnd_list:
                message = f"没有找到{window_type}雷电模拟器窗口"
                print(f"[WindowArrangementManager] {message}")
                log_info(message, component="WindowArrangementManager")
                result = {'status': 'success', 'message': message, 'window_count': 0}
                self.arrangement_completed.emit(result)
                return

            print(f"[WindowArrangementManager] 找到 {len(hwnd_list)} 个{window_type}模拟器窗口")
            log_info(f"找到 {len(hwnd_list)} 个{window_type}模拟器窗口", component="WindowArrangementManager")
            
            # 执行窗口排列
            success_count = self._arrange_windows_by_api(hwnd_list, window_width, window_height,
                                                       column_spacing, row_spacing, windows_per_row)
            
            result = {
                'status': 'success',
                'message': f'窗口排列完成，成功排列 {success_count}/{len(hwnd_list)} 个窗口',
                'window_count': len(hwnd_list),
                'success_count': success_count
            }
            
            log_info(f"窗口排列完成，成功排列 {success_count}/{len(hwnd_list)} 个窗口", component="WindowArrangementManager")
            self.arrangement_completed.emit(result)
            
        except Exception as e:
            error_msg = f"窗口排列异常: {e}"
            log_error(error_msg, component="WindowArrangementManager")
            self.arrangement_failed.emit(error_msg)
        finally:
            self._is_arranging = False

    # ========================================
    # 🎯 窗口检测模块 - 模拟器窗口识别和过滤
    # ========================================

    def _get_running_emulator_windows(self) -> List[int]:
        """🎯 获取正在运行的雷电模拟器窗口句柄（只排列运行中的模拟器）"""
        try:
            import win32gui
            from core.unified_emulator_manager import get_emulator_manager

            # 🎯 获取正在运行的模拟器ID列表
            emulator_manager = get_emulator_manager()
            running_emulator_ids = set()

            # 从启动管理器获取正在运行的模拟器
            if hasattr(emulator_manager, 'startup_manager') and hasattr(emulator_manager.startup_manager, 'running_emulators'):
                running_emulator_ids = set(emulator_manager.startup_manager.running_emulators.keys())
                print(f"[WindowArrangementManager] 正在运行的模拟器ID: {running_emulator_ids}")

            hwnd_list = []

            def enum_windows_callback(hwnd, windows_list):
                if win32gui.IsWindowVisible(hwnd):
                    try:
                        class_name = win32gui.GetClassName(hwnd)
                        window_title = win32gui.GetWindowText(hwnd)

                        # 使用窗口类名精确识别雷电模拟器主窗口
                        if class_name == "LDPlayerMainFrame":
                            # 🎯 从窗口标题中提取模拟器ID，只排列正在运行的
                            emulator_id = self._extract_emulator_id_from_title(window_title)

                            if emulator_id is not None and emulator_id in running_emulator_ids:
                                windows_list.append(hwnd)
                            # 🎯 移除冗余日志，避免日志沉冗

                    except Exception as e:
                        # 获取窗口信息失败，跳过此窗口
                        log_error(f"获取窗口信息失败: {e}", component="WindowArrangementManager")

            win32gui.EnumWindows(enum_windows_callback, hwnd_list)
            print(f"[WindowArrangementManager] 窗口扫描完成，找到 {len(hwnd_list)} 个运行中的模拟器窗口")
            log_info(f"窗口扫描完成，找到 {len(hwnd_list)} 个运行中的模拟器窗口", component="WindowArrangementManager")
            return hwnd_list

        except Exception as e:
            print(f"[WindowArrangementManager] 枚举窗口失败: {e}")
            log_error(f"枚举窗口失败: {e}", component="WindowArrangementManager")
            return []

    def _extract_emulator_id_from_title(self, window_title: str) -> int:
        """🎯 从窗口标题中提取模拟器ID"""
        try:
            # 雷电模拟器窗口标题格式通常是: "雷电模拟器" 或 "雷电模拟器-1" 或 "LDPlayer-1" 等
            import re

            # 尝试匹配各种可能的格式
            patterns = [
                r'雷电模拟器-(\d+)',  # 雷电模拟器-1
                r'LDPlayer-(\d+)',    # LDPlayer-1
                r'雷电模拟器(\d+)',   # 雷电模拟器1
                r'LDPlayer(\d+)',     # LDPlayer1
                r'-(\d+)$',           # 以-数字结尾
                r'(\d+)$'             # 以数字结尾
            ]

            for pattern in patterns:
                match = re.search(pattern, window_title)
                if match:
                    emulator_id = int(match.group(1))
                    print(f"[WindowArrangementManager] 从标题'{window_title}'提取到模拟器ID: {emulator_id}")
                    return emulator_id

            # 如果没有找到数字，可能是主模拟器（ID=0）
            if "雷电模拟器" in window_title or "LDPlayer" in window_title:
                print(f"[WindowArrangementManager] 从标题'{window_title}'推断为主模拟器ID: 0")
                return 0

            return None

        except Exception as e:
            print(f"[WindowArrangementManager] 提取模拟器ID失败: {e}")
            return None

    def _get_all_emulator_windows(self) -> List[int]:
        """🎯 获取所有雷电模拟器窗口句柄（手动排列时使用）"""
        try:
            import win32gui

            hwnd_list = []

            def enum_windows_callback(hwnd, windows_list):
                if win32gui.IsWindowVisible(hwnd):
                    try:
                        class_name = win32gui.GetClassName(hwnd)

                        # 使用窗口类名精确识别雷电模拟器主窗口
                        if class_name == "LDPlayerMainFrame":
                            windows_list.append(hwnd)
                        # 🎯 移除冗余日志，避免日志沉冗

                    except Exception as e:
                        # 获取窗口信息失败，跳过此窗口
                        log_error(f"获取窗口信息失败: {e}", component="WindowArrangementManager")

            win32gui.EnumWindows(enum_windows_callback, hwnd_list)
            print(f"[WindowArrangementManager] 窗口扫描完成，找到 {len(hwnd_list)} 个模拟器窗口")
            log_info(f"窗口扫描完成，找到 {len(hwnd_list)} 个模拟器窗口", component="WindowArrangementManager")
            return hwnd_list

        except Exception as e:
            print(f"[WindowArrangementManager] 枚举窗口失败: {e}")
            log_error(f"枚举窗口失败: {e}", component="WindowArrangementManager")
            return []

    # ========================================
    # 🎯 窗口排列算法模块 - 智能网格排列和空位填补
    # ========================================

    def _arrange_windows_by_api(self, hwnd_list: List[int], window_width: Optional[int],
                              window_height: Optional[int], column_spacing: int, 
                              row_spacing: int, windows_per_row: int) -> int:
        """🎯 使用win32gui API智能排列窗口（真正的空位填补算法）"""
        try:
            import win32gui
            import ctypes
            
            # 获取屏幕分辨率
            user32 = ctypes.windll.user32
            screen_width = user32.GetSystemMetrics(0)
            screen_height = user32.GetSystemMetrics(1)
            
            log_info(f"屏幕分辨率: {screen_width}x{screen_height}", component="WindowArrangementManager")
            
            # 🎯 关键修复：获取实际窗口大小用于网格计算，但不强制统一大小
            # 计算网格布局时需要知道窗口的平均大小，但不会强制改变窗口大小
            if window_width and window_width > 0 and window_height and window_height > 0:
                # 使用配置的窗口大小作为网格计算基准
                grid_width = window_width
                grid_height = window_height
                log_info(f"使用配置的网格计算基准: {grid_width}x{grid_height}", component="WindowArrangementManager")
            else:
                # 🎯 使用第一个窗口的实际大小作为网格计算基准
                if hwnd_list:
                    try:
                        first_rect = win32gui.GetWindowRect(hwnd_list[0])
                        grid_width = first_rect[2] - first_rect[0]
                        grid_height = first_rect[3] - first_rect[1]
                        log_info(f"使用第一个窗口的实际大小作为网格基准: {grid_width}x{grid_height}", component="WindowArrangementManager")
                    except:
                        grid_width = 320
                        grid_height = 576
                        log_info(f"无法获取窗口大小，使用默认网格基准: {grid_width}x{grid_height}", component="WindowArrangementManager")
                else:
                    grid_width = 320
                    grid_height = 576
                    log_info(f"没有窗口，使用默认网格基准: {grid_width}x{grid_height}", component="WindowArrangementManager")
            
            # 分析所有窗口，区分已在网格位置的窗口和需要排列的窗口
            windows_in_grid = {}  # {(row, col): hwnd}
            windows_to_arrange = []
            
            for hwnd in hwnd_list:
                try:
                    rect = win32gui.GetWindowRect(hwnd)
                    current_left = rect[0]
                    current_top = rect[1]
                    window_title = win32gui.GetWindowText(hwnd)
                    
                    # 🎯 使用网格基准大小进行网格位置检测
                    grid_pos = self._get_grid_position(current_left, current_top, grid_width, grid_height,
                                                     column_spacing, row_spacing, windows_per_row)
                    
                    if grid_pos is not None:
                        # 窗口在标准网格位置
                        windows_in_grid[grid_pos] = hwnd
                        log_info(f"窗口 '{window_title}' 已在网格位置 {grid_pos}，保持不动", component="WindowArrangementManager")
                    else:
                        # 窗口不在标准网格位置，需要重新排列
                        windows_to_arrange.append(hwnd)
                        log_info(f"窗口 '{window_title}' 不在网格位置 (当前位置: {current_left}, {current_top})，需要排列",
                                component="WindowArrangementManager")
                                
                except Exception as e:
                    log_error(f"分析窗口位置失败: {e}", component="WindowArrangementManager")
                    windows_to_arrange.append(hwnd)  # 出错时也加入排列列表
            
            if not windows_to_arrange:
                log_info("所有窗口都已在网格位置，无需排列", component="WindowArrangementManager")
                return len(hwnd_list)
                
            log_info(f"需要排列 {len(windows_to_arrange)} 个窗口", component="WindowArrangementManager")
            
            # 找出所有空闲的网格位置（从前往后按顺序）
            available_positions = []
            max_positions = 50  # 最多检查50个位置
            
            for i in range(max_positions):
                row = i // windows_per_row
                col = i % windows_per_row
                
                # 🎯 使用网格基准大小计算网格位置的坐标
                left = col * (grid_width + column_spacing)
                top = row * (grid_height + row_spacing)
                
                # 检查是否超出屏幕
                if left + grid_width > screen_width or top + grid_height > screen_height:
                    break
                    
                # 检查这个网格位置是否已被占用
                if (row, col) not in windows_in_grid:
                    available_positions.append((left, top, row, col))
                    if len(available_positions) >= len(windows_to_arrange):
                        break
            
            log_info(f"找到 {len(available_positions)} 个空闲网格位置", component="WindowArrangementManager")
            
            # 将需要排列的窗口按顺序放到空闲位置
            success_count = 0
            for i, hwnd in enumerate(windows_to_arrange):
                if i >= len(available_positions):
                    log_info(f"没有足够的空闲位置，跳过窗口{i+1}", component="WindowArrangementManager")
                    break
                    
                try:
                    # 获取窗口当前大小
                    rect = win32gui.GetWindowRect(hwnd)
                    current_width = rect[2] - rect[0]
                    current_height = rect[3] - rect[1]
                    
                    # 🎯 关键修复：只移动位置，不改变窗口大小（避免破坏模拟器内部画面）
                    # 保持窗口原有大小，只改变位置
                    final_width = current_width
                    final_height = current_height
                    
                    # 获取分配的位置
                    left, top, row, col = available_positions[i]
                    
                    # 只移动窗口位置，保持原有大小
                    win32gui.MoveWindow(hwnd, left, top, final_width, final_height, True)
                    
                    window_title = win32gui.GetWindowText(hwnd)
                    log_info(f"窗口 '{window_title}' 已排列到网格位置 ({row}, {col}): ({left}, {top}) 保持原大小: {final_width}x{final_height}",
                            component="WindowArrangementManager")
                    
                    success_count += 1
                    
                    # 短暂延迟，避免操作过快
                    time.sleep(0.1)
                    
                except Exception as e:
                    log_error(f"排列窗口{i+1}失败: {e}", component="WindowArrangementManager")
            
            return success_count
            
        except Exception as e:
            log_error(f"窗口排列API调用失败: {e}", component="WindowArrangementManager")
            return 0

    # ========================================
    # 🎯 工具方法模块 - 网格计算和位置检测
    # ========================================

    def _get_grid_position(self, left: int, top: int, window_width: int, window_height: int,
                         column_spacing: int, row_spacing: int, windows_per_row: int) -> Optional[Tuple[int, int]]:
        """🎯 获取窗口在网格中的位置（行号，列号）"""
        # 容差范围（像素）
        tolerance = 10
        
        # 🎯 直接使用传入的窗口大小（已经是标准化的）
        grid_width = window_width
        grid_height = window_height
        
        # 计算可能的网格位置
        for row in range(10):  # 检查前10行
            for col in range(windows_per_row):
                expected_left = col * (grid_width + column_spacing)
                expected_top = row * (grid_height + row_spacing)
                
                # 检查位置是否匹配（在容差范围内）
                if (abs(left - expected_left) <= tolerance and
                    abs(top - expected_top) <= tolerance):
                    return (row, col)
        
        return None


# ========================================
# 🎯 全局实例管理模块 - 单例模式实现
# ========================================

_window_arrangement_manager = None

def get_window_arrangement_manager() -> WindowArrangementManager:
    """获取全局窗口排列管理器实例"""
    global _window_arrangement_manager
    if _window_arrangement_manager is None:
        _window_arrangement_manager = WindowArrangementManager()
    return _window_arrangement_manager
