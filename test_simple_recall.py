#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单撤回功能测试
========================================
功能描述: 测试Instagram私信撤回功能
创建时间: 2025-07-23
作者: AI Assistant

使用方法:
1. 确保模拟器运行并且在Instagram私信界面
2. 运行: python test_simple_recall.py
3. 程序会自动测试撤回功能

注意: 此程序会实际操作Instagram，请确保在测试环境中运行
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.instagram_task import InstagramDMTask
from core.simple_config import get_config_manager
from core.logger_manager import log_info, log_error, log_warning


class SimpleRecallTester:
    """简单撤回测试器"""

    def __init__(self, emulator_id: int = 2):
        """初始化测试器"""
        self.emulator_id = emulator_id
        self.config_manager = get_config_manager()
        self.instagram_task = None
        
    async def setup_tester(self):
        """设置测试器"""
        try:
            log_info(f"[简单撤回测试器] 设置测试器", component="SimpleRecallTester")
            
            # 创建Instagram任务实例
            self.instagram_task = InstagramDMTask(self.emulator_id, self.config_manager)
            
            # 验证模拟器状态
            if not self.instagram_task.ld.is_running(self.emulator_id):
                log_error(f"[简单撤回测试器] 模拟器{self.emulator_id}未运行", component="SimpleRecallTester")
                return False
            
            log_info(f"[简单撤回测试器] ✅ 测试器设置完成", component="SimpleRecallTester")
            return True
            
        except Exception as e:
            log_error(f"[简单撤回测试器] 设置测试器异常: {e}", component="SimpleRecallTester")
            return False

    async def test_recall_function(self):
        """测试撤回功能"""
        try:
            log_info(f"[简单撤回测试器] 开始测试撤回功能", component="SimpleRecallTester")
            
            # 调用撤回功能
            result = await self.instagram_task.recall_messages()
            
            if result:
                log_info(f"[简单撤回测试器] ✅ 撤回功能测试成功", component="SimpleRecallTester")
                return True
            else:
                log_warning(f"[简单撤回测试器] ⚠️ 撤回功能测试失败", component="SimpleRecallTester")
                return False
                
        except Exception as e:
            log_error(f"[简单撤回测试器] 测试撤回功能异常: {e}", component="SimpleRecallTester")
            return False

    async def run_test(self):
        """运行完整的测试流程"""
        try:
            print("🔍 Instagram撤回功能测试")
            print("=" * 60)
            print("此测试会验证撤回功能是否正常工作")
            print("=" * 60)
            
            # 1. 设置测试器
            if not await self.setup_tester():
                print("❌ 测试器设置失败")
                return
            
            # 2. 测试撤回功能
            recall_success = await self.test_recall_function()
            
            # 3. 显示测试结果
            print("\n" + "=" * 60)
            print("测试结果:")
            print("=" * 60)
            
            if recall_success:
                print("✅ 撤回功能测试完全成功!")
                print("✅ 撤回功能正常工作")
                print("✅ 图色识别正常工作")
                print("✅ 点击操作正常工作")
                print("\n建议:")
                print("  撤回功能已经可以在实际的Instagram私信任务中使用")
                print("  可以在配置中启用 recall_before_dm 选项")
            else:
                print("❌ 撤回功能测试失败")
                print("❌ 可能的问题:")
                print("  1. 界面不在正确的Instagram私信页面")
                print("  2. 没有可撤回的消息")
                print("  3. 撤回按钮图片模板需要更新")
                print("  4. 模拟器分辨率或界面有变化")
                print("\n建议:")
                print("  1. 确保在Instagram私信界面")
                print("  2. 先发送一条测试消息")
                print("  3. 检查撤回按钮图片模板")
            
        except Exception as e:
            log_error(f"[简单撤回测试器] 运行测试异常: {e}", component="SimpleRecallTester")


async def main():
    """主函数"""
    try:
        print("Instagram撤回功能测试")
        print("此测试会验证撤回功能是否正常工作")
        print("⚠️  注意: 此程序会实际操作Instagram")
        
        confirm = input("确认继续？(y/N): ").strip().lower()
        if confirm != 'y':
            print("测试已取消")
            return
        
        tester = SimpleRecallTester(emulator_id=2)
        await tester.run_test()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
