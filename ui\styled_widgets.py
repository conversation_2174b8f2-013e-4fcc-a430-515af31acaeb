#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
企业级雷电模拟器中控系统 - 自定义样式化UI控件模块

本模块提供了一系列预设了特定样式的自定义UI控件，旨在统一应用程序的视觉风格，提升美观度。
这些控件是对原生PyQt6控件的封装和扩展，完全复刻原有界面样式。

主要控件包括:
- StyledButton: 带有圆角、背景色和悬停效果的标准按钮
- StyledLineEdit: 带有圆角、内边距和统一样式的单行文本输入框
- NoWheelSpinBox: 禁用滚轮事件的数字输入框
- GradientFrame: 支持水平或垂直渐变背景的QFrame
- RoundedWidget: 圆角背景小部件
- DonutWidget: 环形图/甜甜圈图控件
- ModernToggleSwitch: 现代开关组件
- StatusCard: 状态卡片组件
- StyleManager: 统一样式管理器（新增）
"""

from PyQt6.QtWidgets import (QPushButton, QLineEdit, QFrame, QWidget, QSpinBox,
                           QVBoxLayout, QLabel, QHBoxLayout, QCheckBox)
from PyQt6.QtCore import Qt, QTimer, QPoint, pyqtSignal, QRectF
from PyQt6.QtGui import QColor, QLinearGradient, QBrush, QPainter, QPainterPath, QPen


# ========================================
# 🎨 统一样式管理器
# ========================================
# 📝 功能描述: 提供统一的样式定义和管理，消除代码重复，确保UI一致性
# 🔧 主要方法: get_button_style(), get_groupbox_style(), get_progressbar_style()
# 📞 调用关系: 被所有UI组件调用，提供标准化样式
# ⚠️  注意事项: 修改样式会影响整个应用，需要全面测试
# 📚 使用场景: 统一控件样式、减少代码重复、维护视觉一致性
# ========================================

class StyleManager:
    """统一样式管理器 - 消除重复样式代码"""

    # 颜色主题定义
    COLORS = {
        'primary': '#8a56ac',
        'primary_hover': '#9b67bd',
        'primary_pressed': '#7a4b9c',
        'secondary': '#2196F3',
        'secondary_hover': '#1976D2',
        'secondary_pressed': '#1565C0',
        'success': '#4CAF50',
        'success_hover': '#45a049',
        'success_pressed': '#3d8b40',
        'warning': '#FF9800',
        'warning_hover': '#F57C00',
        'warning_pressed': '#E65100',
        'danger': '#e91e63',
        'danger_hover': '#c2185b',
        'danger_pressed': '#ad1457',
        'disabled': '#cccccc',
        'disabled_text': '#999999',
        'background_light': '#f8f9fa',
        'background_medium': '#ecf0f1',
        'border_light': '#e0e0e0',
        'text_primary': '#333',
        'text_secondary': '#666'
    }

    @classmethod
    def get_button_style(cls, color_type='primary', size='medium'):
        """
        获取标准化按钮样式

        Args:
            color_type: 颜色类型 ('primary', 'secondary', 'success', 'warning', 'danger')
            size: 尺寸类型 ('small', 'medium', 'large')
        """
        colors = {
            'primary': (cls.COLORS['primary'], cls.COLORS['primary_hover'], cls.COLORS['primary_pressed']),
            'secondary': (cls.COLORS['secondary'], cls.COLORS['secondary_hover'], cls.COLORS['secondary_pressed']),
            'success': (cls.COLORS['success'], cls.COLORS['success_hover'], cls.COLORS['success_pressed']),
            'warning': (cls.COLORS['warning'], cls.COLORS['warning_hover'], cls.COLORS['warning_pressed']),
            'danger': (cls.COLORS['danger'], cls.COLORS['danger_hover'], cls.COLORS['danger_pressed'])
        }

        sizes = {
            'small': {'padding': '4px 8px', 'font_size': '11px', 'border_radius': '4px', 'height': '24px'},
            'medium': {'padding': '8px 16px', 'font_size': '13px', 'border_radius': '6px', 'height': '36px'},
            'large': {'padding': '12px 24px', 'font_size': '15px', 'border_radius': '8px', 'height': '44px'}
        }

        color_set = colors.get(color_type, colors['primary'])
        size_set = sizes.get(size, sizes['medium'])

        return f"""
            QPushButton {{
                background-color: {color_set[0]};
                color: white;
                border: none;
                border-radius: {size_set['border_radius']};
                padding: {size_set['padding']};
                font-size: {size_set['font_size']};
                font-weight: bold;
                min-height: {size_set['height']};
            }}
            QPushButton:hover {{
                background-color: {color_set[1]};
            }}
            QPushButton:pressed {{
                background-color: {color_set[2]};
            }}
            QPushButton:disabled {{
                background-color: {cls.COLORS['disabled']};
                color: {cls.COLORS['disabled_text']};
            }}
        """

    @classmethod
    def get_groupbox_style(cls, background_color=None, title_color=None):
        """
        获取标准化GroupBox样式

        Args:
            background_color: 背景颜色，默认为浅蓝色
            title_color: 标题颜色，默认为主色调
        """
        bg_color = background_color or '#f3f9ff'
        title_color = title_color or cls.COLORS['secondary']

        return f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: {cls.COLORS['text_primary']};
                border: 2px solid {cls.COLORS['border_light']};
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: {bg_color};
                min-height: 200px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: {bg_color};
                color: {title_color};
            }}
        """

    @classmethod
    def get_progressbar_style(cls, color_type='primary'):
        """
        获取标准化进度条样式

        Args:
            color_type: 颜色类型 ('primary', 'success', 'warning')
        """
        color_gradients = {
            'primary': 'stop:0 #8a56ac, stop:1 #c59de0',
            'success': 'stop:0 #4CAF50, stop:1 #81C784',
            'warning': 'stop:0 #FF9800, stop:1 #FFB74D',
            'info': 'stop:0 #a0e9ff, stop:1 #a1c4fd'
        }

        gradient = color_gradients.get(color_type, color_gradients['primary'])

        return f"""
            QProgressBar {{
                background-color: #f0f0f0;
                border: none;
                border-radius: 8px;
                height: 16px;
                text-align: center;
                color: {cls.COLORS['text_primary']};
            }}
            QProgressBar::chunk {{
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, {gradient});
                border-radius: 8px;
            }}
        """

    @classmethod
    def get_textedit_style(cls, background_color=None):
        """
        获取标准化文本编辑器样式

        Args:
            background_color: 背景颜色，默认为白色
        """
        bg_color = background_color or '#ffffff'

        return f"""
            QTextEdit {{
                background-color: {bg_color};
                border: 1px solid {cls.COLORS['border_light']};
                border-radius: 6px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                color: {cls.COLORS['text_primary']};
            }}
            QTextEdit:focus {{
                border-color: {cls.COLORS['primary']};
            }}
        """


# ========================================
# 🎯 基础输入控件
# ========================================
# 📝 功能描述: 提供统一样式的基础输入控件，确保用户输入体验的一致性和可用性
# 🔧 主要方法: NoWheelSpinBox, StyledButton, StyledLineEdit
# 📞 调用关系: 被所有UI模块广泛使用，是整个UI系统的基础组件
# ⚠️  注意事项: 修改这些控件会影响整个应用的外观和行为，需要全面测试所有使用场景
# 📚 使用场景: 统一控件样式、添加新的输入控件类型、修复控件行为问题、优化用户交互体验
# ========================================

class NoWheelSpinBox(QSpinBox):
    """禁用滚轮事件的QSpinBox - 确保只能通过键盘输入数值"""
    
    def wheelEvent(self, e):
        """重写滚轮事件，完全禁用滚轮功能"""
        # 完全忽略滚轮事件，不传递给父控件
        e.ignore()


class StyledButton(QPushButton):
    """自定义样式按钮 - 完全复刻原有样式"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.init_style()

    def init_style(self):
        """初始化样式 - 保持原有的18px圆角样式"""
        self.setStyleSheet("""
            QPushButton {
                background-color: #8a56ac;
                color: white;
                border-radius: 18px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #9b67bd;
            }
            QPushButton:pressed {
                background-color: #7a4b9c;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #999999;
            }
        """)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setMinimumHeight(36)


# ========================================
# 🎯 便捷按钮创建函数
# ========================================
# 📝 功能描述: 提供快速创建常用按钮的便捷函数，减少重复代码
# 🔧 主要函数: create_primary_button(), create_success_button(), create_warning_button()
# 📞 调用关系: 被UI组件调用，快速创建标准化按钮
# ⚠️  注意事项: 所有按钮都使用统一的样式管理器
# 📚 使用场景: 快速创建标准按钮、减少样式重复代码
# ========================================

def create_primary_button(text, size='medium', callback=None):
    """创建主要按钮（紫色主题）- 使用原有StyledButton样式"""
    _ = size  # size参数保留以保持接口一致性
    button = StyledButton(text)
    if callback:
        button.clicked.connect(callback)
    return button

def create_secondary_button(text, size='medium', callback=None):
    """创建次要按钮（蓝色主题）"""
    button = StyledButton(text)
    # 覆盖为蓝色样式
    button.setStyleSheet(StyleManager.get_button_style('secondary', size))
    if callback:
        button.clicked.connect(callback)
    return button

def create_success_button(text, size='medium', callback=None):
    """创建成功按钮（绿色主题）"""
    button = StyledButton(text)
    # 覆盖为绿色样式
    button.setStyleSheet(StyleManager.get_button_style('success', size))
    if callback:
        button.clicked.connect(callback)
    return button

def create_warning_button(text, size='medium', callback=None):
    """创建警告按钮（橙色主题）"""
    button = StyledButton(text)
    # 覆盖为橙色样式
    button.setStyleSheet(StyleManager.get_button_style('warning', size))
    if callback:
        button.clicked.connect(callback)
    return button

def create_danger_button(text, size='medium', callback=None):
    """创建危险按钮（红色主题）"""
    button = StyledButton(text)
    # 覆盖为红色样式
    button.setStyleSheet(StyleManager.get_button_style('danger', size))
    if callback:
        button.clicked.connect(callback)
    return button


class StyledLineEdit(QLineEdit):
    """自定义样式输入框 - 完全复刻原有样式"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_style()

    def init_style(self):
        """初始化现代优雅的极简主义样式 - 修复字体显示问题"""
        self.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #fafbfc);
                border: 2px solid #e8ecf0;
                border-radius: 12px;
                padding: 10px 16px;
                font-size: 13px;
                font-weight: 400;
                font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
                selection-background-color: rgba(33, 150, 243, 0.3);
                color: #2c3e50;
                line-height: 1.4;
                min-height: 16px;
            }
            QLineEdit:focus {
                border: 2px solid #FF9800;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #fff8f0);
                outline: none;
            }
            QLineEdit:hover {
                border: 2px solid #FFB74D;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #fff9f5);
            }
            QLineEdit:disabled {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #6c757d;
                border: 2px solid #dee2e6;
            }
        """)
        self.setMinimumHeight(44)


# ========================================
# 🎯 容器和背景控件
# ========================================
# 📝 功能描述: 提供各种容器和背景效果控件，用于创建美观的界面布局和视觉效果
# 🔧 主要方法: GradientFrame, RoundedWidget
# 📞 调用关系: 被主窗口和各种页面用作容器，提供渐变背景和圆角效果
# ⚠️  注意事项: 背景绘制要考虑性能影响，避免过度复杂的绘制操作影响界面流畅度
# 📚 使用场景: 创建导航栏背景、页面容器、卡片式布局、美化界面视觉效果
# ========================================

class GradientFrame(QFrame):
    """渐变背景框架 - 用于导航栏背景"""

    def __init__(self, start_color=QColor("#8a56ac"), end_color=QColor("#e196e6"),
                 direction=Qt.Orientation.Vertical, parent=None):
        super().__init__(parent)
        self.start_color = start_color
        self.end_color = end_color
        self.direction = direction

    def paintEvent(self, a0):
        """绘制渐变背景"""
        _ = a0  # 事件参数未使用
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        gradient = QLinearGradient()
        if self.direction == Qt.Orientation.Vertical:
            gradient.setStart(0, 0)
            gradient.setFinalStop(0, self.height())
        else:
            gradient.setStart(0, 0)
            gradient.setFinalStop(self.width(), 0)

        gradient.setColorAt(0, self.start_color)
        gradient.setColorAt(1, self.end_color)

        painter.fillRect(self.rect(), gradient)


class RoundedWidget(QWidget):
    """圆角背景小部件 - 用作容器"""

    def __init__(self, parent=None, radius=15, bg_color=QColor(255, 255, 255, 230)):
        super().__init__(parent)
        self.radius = radius
        self.bg_color = bg_color

        # 设置属性
        self.setAutoFillBackground(False)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

    def paintEvent(self, a0):
        """绘制圆角背景"""
        _ = a0  # 事件参数未使用
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 创建圆角路径
        path = QPainterPath()
        path.addRoundedRect(0, 0, self.width(), self.height(), self.radius, self.radius)

        # 填充背景
        painter.fillPath(path, self.bg_color)

        # 绘制边框
        painter.setPen(QColor(224, 224, 224))
        painter.drawPath(path)


# ========================================
# 🎯 数据可视化控件
# ========================================
# 📝 功能描述: 提供数据可视化组件，用于直观显示系统状态、进度信息和统计数据
# 🔧 主要方法: DonutWidget
# 📞 调用关系: 被状态面板和监控界面使用，显示模拟器状态统计等信息
# ⚠️  注意事项: 绘制操作要优化性能，数据更新要平滑过渡，避免闪烁和卡顿
# 📚 使用场景: 显示模拟器运行状态、任务完成进度、系统资源使用情况、统计图表
# ========================================

class DonutWidget(QWidget):
    """环形图控件 - 用于显示模拟器状态统计"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.value = 0
        self.total = 100
        self.progress_color = QColor("#8a56ac")
        self.background_color = QColor("#f0f0f0")
        self.text_color = QColor("#333333")
        self.setMinimumSize(80, 80)

    def set_values(self, value, total):
        """设置数值"""
        if total == 0:
            self.value = 0
            self.total = 100
        else:
            self.value = value
            self.total = total
        self.update()

    def paintEvent(self, a0):
        """绘制环形图"""
        _ = a0  # 事件参数未使用
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        rect = self.rect()
        side = min(rect.width(), rect.height())

        inset = 6
        thickness = 12

        arc_rect = QRectF(
            rect.center().x() - (side / 2) + inset,
            rect.center().y() - (side / 2) + inset,
            side - (inset * 2),
            side - (inset * 2)
        )

        # 绘制背景环
        painter.setPen(QPen(self.background_color, float(thickness), Qt.PenStyle.SolidLine))
        painter.drawArc(arc_rect, 0, 360 * 16)

        # 绘制进度环
        painter.setPen(QPen(self.progress_color, float(thickness), Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        angle = (self.value / self.total) * 360 if self.total > 0 else 0
        painter.drawArc(arc_rect, 90 * 16, -int(angle * 16))

        # 绘制中心文本
        font = self.font()
        font.setPointSize(14)
        font.setBold(True)
        painter.setFont(font)
        painter.setPen(self.text_color)
        text = f"{self.value}"
        painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, text)


# ========================================
# 🎯 交互控件 - 开关和按钮
# ========================================
# 📝 功能描述: 提供现代化的交互控件，包括动画开关、导航按钮等，提升用户交互体验
# 🔧 主要方法: ModernToggleSwitch, NavButton
# 📞 调用关系: 被设置页面和导航栏使用，提供用户交互功能
# ⚠️  注意事项: 动画效果要流畅自然，交互响应要及时，避免延迟和卡顿影响用户体验
# 📚 使用场景: 设置页面的开关控制、导航栏的页面切换、用户偏好设置、功能开关控制
# ========================================

class ModernToggleSwitch(QWidget):
    """现代开关组件 - 用于设置页面"""

    toggled = pyqtSignal(bool)

    def __init__(self, parent=None, checked=False):
        super().__init__(parent)
        self.setFixedSize(50, 24)
        self.setCursor(Qt.CursorShape.PointingHandCursor)

        self._checked = checked
        self._circle_position = 26 if checked else 2
        self._bg_color = QColor("#8a56ac") if checked else QColor("#e0e0e0")

        # 使用定时器实现简单动画
        self._animation_timer = QTimer()
        self._animation_timer.timeout.connect(self._update_animation)
        self._animation_step = 0
        self._animation_steps = 10
        self._target_position = self._circle_position
        self._target_color = self._bg_color

    def is_checked(self):
        return self._checked

    def set_checked(self, checked):
        if self._checked == checked:
            return

        self._checked = checked
        self._animate_toggle()
        self.toggled.emit(checked)

    def toggle(self):
        self.set_checked(not self._checked)

    def _animate_toggle(self):
        # 设置目标值
        self._target_position = 26 if self._checked else 2
        self._target_color = QColor("#8a56ac") if self._checked else QColor("#e0e0e0")

        # 启动动画
        self._animation_step = 0
        self._animation_timer.start(20)  # 20ms间隔

    def _update_animation(self):
        self._animation_step += 1
        progress = self._animation_step / self._animation_steps

        if progress >= 1.0:
            # 动画完成
            self._circle_position = self._target_position
            self._bg_color = self._target_color
            self._animation_timer.stop()
        else:
            # 插值计算
            start_pos = 26 if not self._checked else 2
            self._circle_position = start_pos + (self._target_position - start_pos) * progress

            # 颜色插值
            start_color = QColor("#e0e0e0") if not self._checked else QColor("#8a56ac")
            r = int(start_color.red() + (self._target_color.red() - start_color.red()) * progress)
            g = int(start_color.green() + (self._target_color.green() - start_color.green()) * progress)
            b = int(start_color.blue() + (self._target_color.blue() - start_color.blue()) * progress)
            self._bg_color = QColor(r, g, b)

        self.update()

    def mousePressEvent(self, a0):
        if a0.button() == Qt.MouseButton.LeftButton:
            self.toggle()
        super().mousePressEvent(a0)

    def paintEvent(self, a0):
        _ = a0  # 事件参数未使用
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制背景轨道
        track_rect = QRectF(0, 0, self.width(), self.height())
        painter.setBrush(QBrush(self._bg_color))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(track_rect, 12, 12)

        # 绘制滑块
        circle_rect = QRectF(self._circle_position, 2, 20, 20)
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        painter.drawEllipse(circle_rect)


# ========================================
# 🎯 信息展示控件
# ========================================
# 📝 功能描述: 提供信息展示和状态显示控件，用于清晰地向用户展示系统状态和数据
# 🔧 主要方法: StatusCard
# 📞 调用关系: 被监控页面和状态面板使用，显示各种系统状态信息
# ⚠️  注意事项: 信息更新要及时准确，显示格式要清晰易读，支持动态数据更新
# 📚 使用场景: 显示系统状态、监控数据、统计信息、实时指标、警告提示
# ========================================

class StatusCard(RoundedWidget):
    """状态卡片组件 - 用于显示系统状态信息"""

    def __init__(self, title="", value="", icon="", parent=None):
        super().__init__(parent)
        self.setFixedHeight(80)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(10)

        # 图标区域
        if icon:
            icon_label = QLabel(icon)
            icon_label.setStyleSheet("font-size: 24px; color: #8a56ac;")
            icon_label.setFixedSize(40, 40)
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(icon_label)

        # 文本区域
        text_layout = QVBoxLayout()
        text_layout.setSpacing(2)

        self.title_label = QLabel(title)
        self.title_label.setStyleSheet("font-size: 12px; color: #666; font-weight: bold;")

        self.value_label = QLabel(value)
        self.value_label.setStyleSheet("font-size: 18px; color: #333; font-weight: bold;")

        text_layout.addWidget(self.title_label)
        text_layout.addWidget(self.value_label)
        text_layout.addStretch()

        layout.addLayout(text_layout)
        layout.addStretch()

    def update_value(self, value):
        """更新数值"""
        self.value_label.setText(str(value))

    def update_title(self, title):
        """更新标题"""
        self.title_label.setText(title)


class NavButton(QPushButton):
    """导航按钮 - 用于左侧导航栏"""

    def __init__(self, text, is_active=False, parent=None):
        super().__init__(text, parent)
        self.is_active = is_active
        self.init_style()

    def init_style(self):
        """初始化样式"""
        self.setFixedHeight(45)
        self.setStyleSheet(self.get_style())
        self.setCursor(Qt.CursorShape.PointingHandCursor)

    def get_style(self):
        """获取样式字符串"""
        if self.is_active:
            return """
                QPushButton {
                    background-color: rgba(255, 255, 255, 0.2);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    text-align: left;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.3);
                }
            """
        else:
            return """
                QPushButton {
                    background-color: transparent;
                    color: rgba(255, 255, 255, 0.8);
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    text-align: left;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                    color: white;
                }
            """

    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.setStyleSheet(self.get_style())


# ========================================
# 🎯 动画和指示器控件
# ========================================
# 📝 功能描述: 提供动画效果和状态指示器，用于直观显示系统运行状态和活动状态
# 🔧 主要方法: HeartbeatIndicator
# 📞 调用关系: 被监控界面使用，显示系统心跳状态和活动指示
# ⚠️  注意事项: 动画要流畅自然，不能消耗过多CPU资源，要支持启动和停止控制
# 📚 使用场景: 显示系统心跳状态、活动指示器、加载动画、状态变化提示
# ========================================

class HeartbeatIndicator(QWidget):
    """心跳指示器 - 带脉冲动画的圆点"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(20, 20)

        self._is_active = False
        self._pulse_radius = 5
        self._max_radius = 10
        self._color = QColor("#4CAF50")  # 绿色表示正常

        # 脉冲动画
        self._pulse_timer = QTimer()
        self._pulse_timer.timeout.connect(self._update_pulse)
        self._pulse_direction = 1

    def set_active(self, active):
        """设置激活状态"""
        self._is_active = active
        if active:
            self._pulse_timer.start(50)  # 50ms更新一次
        else:
            self._pulse_timer.stop()
            self._pulse_radius = 5
        self.update()

    def set_color(self, color):
        """设置颜色"""
        self._color = color
        self.update()

    def _update_pulse(self):
        """更新脉冲动画"""
        self._pulse_radius += self._pulse_direction * 0.5

        if self._pulse_radius >= self._max_radius:
            self._pulse_direction = -1
        elif self._pulse_radius <= 5:
            self._pulse_direction = 1

        self.update()

    def paintEvent(self, a0):
        _ = a0  # 事件参数未使用
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        center = QPoint(self.width() // 2, self.height() // 2)

        if self._is_active:
            # 绘制脉冲外圈
            pulse_color = QColor(self._color)
            pulse_color.setAlpha(100)
            painter.setBrush(QBrush(pulse_color))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawEllipse(center, int(self._pulse_radius), int(self._pulse_radius))

        # 绘制核心圆点
        painter.setBrush(QBrush(self._color))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(center, 4, 4)


# ========================================
# 🎯 复合输入控件
# ========================================
# 📝 功能描述: 提供复合型输入控件，结合多种输入方式和显示效果，提供更丰富的用户交互
# 🔧 主要方法: ModernSpinBox
# 📞 调用关系: 被设置页面广泛使用，提供数值输入和单位显示功能
# ⚠️  注意事项: 要禁用滚轮操作避免误操作，数值范围要合理限制，后缀显示要清晰
# 📚 使用场景: 设置页面的数值输入、带单位的参数设置、范围限制的数值控制
# ========================================

class ModernSpinBox(QWidget):
    """现代化数字输入框组件 - 无箭头按钮的扁平化设计，禁用滚轮"""

    valueChanged = pyqtSignal(int)

    def __init__(self, min_val=0, max_val=2147483647, suffix="", parent=None):
        super().__init__(parent)
        self.min_val = min_val
        self.max_val = max_val
        self.suffix = suffix
        self.current_value = min_val

        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # 主输入框 - 使用NoWheelSpinBox确保禁用滚轮
        self.spinbox = NoWheelSpinBox()
        self.spinbox.setRange(self.min_val, self.max_val)
        self.spinbox.setValue(self.current_value)
        self.spinbox.setFixedHeight(36)
        self.spinbox.setStyleSheet("""
            QSpinBox {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 6px 12px 6px 12px;
                font-size: 14px;
                background-color: white;
                color: #333;
                font-weight: 500;
            }
            QSpinBox:focus {
                border-color: #8a56ac;
                background-color: #fafafa;
                outline: none;
            }
            QSpinBox:hover {
                border-color: #9b67bd;
                background-color: #f9f9f9;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 0px;
                border: none;
            }
        """)

        layout.addWidget(self.spinbox)

        # 后缀标签
        if self.suffix:
            self.suffix_label = QLabel(self.suffix)
            self.suffix_label.setStyleSheet("""
                QLabel {
                    color: #666;
                    font-size: 13px;
                    font-weight: bold;
                    padding-left: 8px;
                    min-width: 25px;
                }
            """)
            layout.addWidget(self.suffix_label)

    def connect_signals(self):
        """连接信号"""
        self.spinbox.valueChanged.connect(self._on_value_changed)

    def _on_value_changed(self, value):
        """值改变处理"""
        self.current_value = value
        self.valueChanged.emit(value)

    def value(self):
        """获取当前值"""
        return self.spinbox.value()

    def setValue(self, value):
        """设置值"""
        self.spinbox.setValue(value)
        self.current_value = value

    def setRange(self, min_val, max_val):
        """设置范围"""
        self.min_val = min_val
        self.max_val = max_val
        self.spinbox.setRange(min_val, max_val)


class ModernCheckBox(QCheckBox):
    """
    现代化复选框组件 - 带清晰勾选图标
    """
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        # 使用更简单的样式，通过CSS实现勾选效果
        self.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                color: #333;
                spacing: 8px;
                font-weight: 500;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid #ddd;
                background-color: white;
            }
            QCheckBox::indicator:hover {
                border-color: #FF9800;
                background-color: #fff8f0;
            }
            QCheckBox::indicator:checked {
                background-color: #FF9800;
                border-color: #FF9800;
                color: white;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #F57C00;
                border-color: #F57C00;
            }
        """)

        # 使用文本方式显示勾选状态
        self.stateChanged.connect(self._update_display)
        self._update_display()

    def _update_display(self):
        """更新显示状态"""
        if self.isChecked():
            # 在文本前添加勾选符号
            original_text = self.text().replace("✓ ", "")  # 移除已有的勾选符号
            self.setText(f"✓ {original_text}")
        else:
            # 移除勾选符号
            original_text = self.text().replace("✓ ", "")
            self.setText(original_text)
