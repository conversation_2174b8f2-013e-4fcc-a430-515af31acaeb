#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无缓存消息检测工具
========================================
功能描述: 强制刷新UI状态，避免缓存问题
创建时间: 2025-07-23
作者: AI Assistant

使用方法:
1. 确保在正确的Instagram私信界面
2. 运行: python test_no_cache.py
3. 程序会强制刷新并检测当前消息
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.instagram_task import InstagramDMTask
from core.simple_config import get_config_manager
from core.logger_manager import log_info, log_error


class NoCacheMessageTester:
    """无缓存消息测试器"""

    def __init__(self, emulator_id: int = 2):
        """初始化测试器"""
        self.emulator_id = emulator_id
        self.config_manager = get_config_manager()
        self.instagram_task = None
        
    async def setup_tester(self):
        """设置测试器"""
        try:
            print(f"🔧 设置测试器 (模拟器 {self.emulator_id})")
            
            # 创建新的Instagram任务实例
            self.instagram_task = InstagramDMTask(self.emulator_id, self.config_manager)
            
            # 验证模拟器状态
            if not self.instagram_task.ld.is_running(self.emulator_id):
                print(f"❌ 模拟器 {self.emulator_id} 未运行")
                return False
            
            screen_size = self.instagram_task.ld.get_screen_size(self.emulator_id)
            print(f"✅ 模拟器 {self.emulator_id} 运行中 (分辨率: {screen_size[0]}×{screen_size[1]})")
            return True
            
        except Exception as e:
            print(f"❌ 设置测试器失败: {e}")
            return False

    def force_refresh_ui(self):
        """强制刷新UI状态"""
        try:
            print("🔄 强制刷新UI状态...")
            
            # 方法1：轻微滑动屏幕来刷新UI
            screen_width, screen_height = self.instagram_task.ld.get_screen_size(self.emulator_id)
            center_x = screen_width // 2
            center_y = screen_height // 2
            
            # 轻微向下滑动再滑回来
            swipe_command = f"input swipe {center_x} {center_y} {center_x} {center_y + 10} 100"
            self.instagram_task.ld.execute_ld(self.emulator_id, swipe_command)
            time.sleep(0.2)
            
            swipe_command = f"input swipe {center_x} {center_y + 10} {center_x} {center_y} 100"
            self.instagram_task.ld.execute_ld(self.emulator_id, swipe_command)
            time.sleep(0.5)
            
            print("✅ UI刷新完成")
            return True
            
        except Exception as e:
            print(f"❌ UI刷新失败: {e}")
            return False

    async def detect_messages_fresh(self):
        """重新检测消息（无缓存）"""
        try:
            print("🔍 重新检测消息 (强制刷新)...")
            
            # 等待UI稳定
            await asyncio.sleep(1.0)
            
            # 直接调用底层API，避免可能的缓存
            message_types = [
                ("text_message", "com.instagram.android:id/direct_text_message_text_view"),
                ("caption_container", "com.instagram.android:id/caption_container"),
                ("media_container", "com.instagram.android:id/media_container"),
                ("content_layout", "com.instagram.android:id/message_content_horizontal_linear_layout")
            ]
            
            all_elements = []
            
            for msg_type, resource_id in message_types:
                try:
                    # 每次都重新查找，不使用缓存
                    elements = self.instagram_task.ld.find_nodes(resource_id=resource_id)
                    
                    if elements:
                        for element in elements:
                            element['message_type'] = msg_type
                            all_elements.append(element)
                        print(f"  {msg_type}: 找到 {len(elements)} 个")
                    else:
                        print(f"  {msg_type}: 未找到")
                        
                except Exception as e:
                    print(f"  {msg_type}: 查找失败 ({e})")
            
            return all_elements
            
        except Exception as e:
            print(f"❌ 检测消息失败: {e}")
            return []

    def analyze_fresh_messages(self, message_elements):
        """分析新检测的消息"""
        try:
            if not message_elements:
                print("📭 当前界面无消息")
                return
            
            print(f"\n📨 当前界面消息分析 (共 {len(message_elements)} 条):")
            print("=" * 70)
            
            screen_width, screen_height = self.instagram_task.ld.get_screen_size(self.emulator_id)
            screen_center = screen_width / 2
            
            for i, element in enumerate(message_elements):
                message_type = element.get('message_type', 'unknown')
                bounds = element.get('bounds', '')
                text = element.get('text', '')[:25] + '...' if len(element.get('text', '')) > 25 else element.get('text', '')
                center_x = element.get('center_x', 'N/A')
                center_y = element.get('center_y', 'N/A')
                
                # 解析边界计算中心点
                if bounds and center_x == 'N/A':
                    try:
                        import re
                        coords = re.findall(r'\d+', bounds)
                        if len(coords) >= 4:
                            left = int(coords[0])
                            right = int(coords[2])
                            center_x = (left + right) / 2
                    except:
                        pass
                
                # 判断消息归属
                if isinstance(center_x, (int, float)):
                    if center_x > screen_center:
                        owner = "🟦 自己的消息"
                        offset = f"+{center_x - screen_center:.1f}"
                    else:
                        owner = "🟨 对方的消息"
                        offset = f"{center_x - screen_center:.1f}"
                else:
                    owner = "❓ 无法判断"
                    offset = "N/A"
                
                print(f"消息 {i+1:2d}: {owner}")
                print(f"         类型: {message_type}")
                if text:
                    print(f"         文本: '{text}'")
                print(f"         边界: {bounds}")
                print(f"         中心: ({center_x}, {center_y})")
                if isinstance(center_x, (int, float)):
                    print(f"         位置: 中心X={center_x:.1f}, 屏幕中心={screen_center}, 偏移={offset}")
                print("-" * 70)
            
        except Exception as e:
            print(f"❌ 分析消息失败: {e}")

    async def run_test(self):
        """运行无缓存测试"""
        try:
            print("🔍 无缓存消息检测工具")
            print("=" * 60)
            print("此工具会强制刷新UI状态并重新检测消息")
            print("=" * 60)
            
            # 1. 设置测试器
            if not await self.setup_tester():
                return
            
            # 2. 强制刷新UI
            if not self.force_refresh_ui():
                print("⚠️ UI刷新失败，继续检测...")
            
            # 3. 重新检测消息
            message_elements = await self.detect_messages_fresh()
            
            # 4. 分析结果
            self.analyze_fresh_messages(message_elements)
            
            print("\n" + "=" * 60)
            print("✅ 无缓存检测完成")
            print("💡 如果仍然检测到意外的消息，可能是:")
            print("  1. 界面不在正确的Instagram私信页面")
            print("  2. 有其他UI元素被误识别为消息")
            print("  3. Instagram界面布局发生了变化")
            
        except Exception as e:
            print(f"❌ 运行测试异常: {e}")


async def main():
    """主函数"""
    try:
        print("无缓存消息检测工具")
        print("此工具会强制刷新UI状态并重新检测消息")
        
        # 询问模拟器ID
        emulator_input = input("请输入模拟器ID (默认2): ").strip()
        emulator_id = 2 if not emulator_input else int(emulator_input)
        
        confirm = input(f"确认检测模拟器 {emulator_id}？(y/N): ").strip().lower()
        if confirm != 'y':
            print("检测已取消")
            return
        
        tester = NoCacheMessageTester(emulator_id=emulator_id)
        await tester.run_test()
        
    except KeyboardInterrupt:
        print("\n检测被用户中断")
    except Exception as e:
        print(f"检测异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
