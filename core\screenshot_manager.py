"""
🎯 截图管理器
功能：提供模拟器异常截图功能
使用雷电原生API，不依赖ADB
"""

import asyncio
from pathlib import Path
from datetime import datetime
from typing import Optional

from .simple_config import get_config_manager
from .logger_manager import log_info, log_error


class ScreenshotManager:
    """🎯 截图管理器 - 统一管理模拟器截图功能"""

    def __init__(self):
        self.config_manager = get_config_manager()
        self._native_tool = None  # 延迟初始化原生截图工具
    
    def _get_native_tool(self):
        """获取原生截图工具实例 - 延迟初始化"""
        if self._native_tool is None:
            try:
                # 导入统一的原生截图引擎
                from .native import NativeScreenshotEngine

                # 获取模拟器路径和截图保存路径
                emulator_path = self.config_manager.get("emulator_path", "")
                screenshot_dir = self._get_screenshot_path()

                # 初始化原生截图引擎
                self._native_tool = NativeScreenshotEngine(
                    emulator_path=emulator_path if emulator_path else None,
                    screenshot_dir=str(screenshot_dir)
                )

                log_info("原生截图引擎初始化成功", component="ScreenshotManager")

            except Exception as e:
                log_error(f"初始化原生截图引擎失败: {e}", component="ScreenshotManager")
                self._native_tool = None

        return self._native_tool

    def _get_screenshot_path(self) -> Path:
        """获取截图保存路径"""
        # 检查用户是否配置了自定义路径
        user_path = self.config_manager.get("monitoring.screenshot_path", "").strip()

        if user_path:
            # 用户指定了路径，使用用户路径
            return Path(user_path)
        else:
            # 用户未指定路径，使用程序目录下的默认路径
            program_dir = Path(__file__).parent.parent  # 程序根目录
            return program_dir / "test_screenshots"
    
    async def capture_failure_screenshot(self, emulator_id: int, error_type: str, failure_count: int) -> Optional[str]:
        """
        捕获异常时的截图 - 使用雷电原生API

        Args:
            emulator_id: 模拟器ID
            error_type: 错误类型
            failure_count: 失败次数

        Returns:
            截图文件路径，失败时返回None
        """
        try:
            # 获取原生截图工具
            native_tool = self._get_native_tool()
            if not native_tool:
                log_error("原生截图工具初始化失败，无法截图", component="ScreenshotManager")
                return None

            # 获取截图保存路径
            screenshot_path = self._get_screenshot_path()
            screenshot_path.mkdir(parents=True, exist_ok=True)

            # 生成文件名 - 保持与原有格式一致
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"emulator_{emulator_id}_{error_type}_{failure_count}_{timestamp}.png"
            output_file = screenshot_path / filename

            # 使用原生截图引擎
            result = await native_tool.capture_emulator_screenshot(
                emulator_id=emulator_id,
                output_file=str(output_file)
            )

            if result:
                log_info(f"异常截图已保存: {result}", component="ScreenshotManager")
                return result
            else:
                log_error(f"原生截图引擎截图失败", component="ScreenshotManager")
                return None

        except Exception as e:
            log_error(f"捕获异常截图失败: {e}", component="ScreenshotManager")
            return None
    
    async def capture_manual_screenshot(self, emulator_id: int, description: str = "手动截图") -> Optional[str]:
        """
        手动截图
        
        Args:
            emulator_id: 模拟器ID
            description: 截图描述
            
        Returns:
            截图文件路径，失败时返回None
        """
        return await self.capture_failure_screenshot(emulator_id, description, 0)


# 全局实例
_screenshot_manager = None

def get_screenshot_manager() -> ScreenshotManager:
    """获取截图管理器实例"""
    global _screenshot_manager
    if _screenshot_manager is None:
        _screenshot_manager = ScreenshotManager()
    return _screenshot_manager
