#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单Instagram跳转测试
========================================
功能描述: 测试回滚版本后的简单修复是否有效
创建时间: 2025-07-25
作者: AI Assistant
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入核心模块
from core.instagram_follow_task import InstagramFollowTask
from core.logger_manager import log_info, log_error


async def test_simple_navigation(emulator_id: int = 2, user_id: str = "katoco0326"):
    """测试简单的跳转功能"""
    try:
        print(f"🚀 测试简单Instagram跳转功能")
        print(f"模拟器ID: {emulator_id}")
        print(f"目标用户: {user_id}")
        print("=" * 50)
        
        # 创建Instagram关注任务实例
        instagram_task = InstagramFollowTask(emulator_id)
        
        # 验证环境
        if not instagram_task.is_ld_available():
            print("❌ 雷电API不可用")
            return False
            
        # 检查模拟器运行状态
        is_running, is_android, _ = instagram_task.ld.is_running(emulator_id)
        if not is_running or not is_android:
            print(f"❌ 模拟器{emulator_id}状态异常")
            return False
        
        print(f"✅ 环境检查通过")
        
        # 记录初始状态
        initial_activity = instagram_task.ld.get_activity_name()
        print(f"📱 初始Activity: {initial_activity}")
        
        # 执行跳转
        print(f"\n🎯 开始跳转到用户: {user_id}")
        start_time = time.time()
        
        result = await instagram_task.navigate_to_profile(user_id)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出结果
        print(f"\n📊 测试结果:")
        print(f"跳转结果: {'✅ 成功' if result else '❌ 失败'}")
        print(f"总耗时: {duration:.2f}秒")
        
        # 记录最终状态
        final_activity = instagram_task.ld.get_activity_name()
        print(f"📱 最终Activity: {final_activity}")
        print(f"Activity变化: {'✅ 是' if initial_activity != final_activity else '❌ 否'}")
        
        # 详细验证
        if result:
            print(f"\n🔍 详细验证:")
            
            # 检查关键元素
            title_element = instagram_task.ld.find_node(resource_id="com.instagram.android:id/action_bar_title")
            followers_element = instagram_task.ld.find_node(resource_id="com.instagram.android:id/row_profile_header_textview_followers_count")
            follow_button = instagram_task.ld.find_node(resource_id="com.instagram.android:id/profile_header_follow_button")
            
            print(f"标题栏: {'✅ 找到' if title_element else '❌ 未找到'}")
            if title_element:
                title_text = title_element.get('text', '')
                print(f"  标题文本: '{title_text}'")
                if title_text == user_id:
                    print(f"  🎉 用户名匹配！")
                else:
                    print(f"  ⚠️ 用户名不匹配 (期望: {user_id})")
            
            print(f"粉丝数元素: {'✅ 找到' if followers_element else '❌ 未找到'}")
            print(f"关注按钮: {'✅ 找到' if follow_button else '❌ 未找到'}")
            
            # 最终判断
            if title_element and title_element.get('text', '') == user_id:
                print(f"\n🎉 跳转完全成功！")
                return True
            elif followers_element and follow_button:
                print(f"\n✅ 跳转基本成功（在资料页但可能不是目标用户）")
                return True
            else:
                print(f"\n⚠️ 跳转状态不明确")
                return False
        else:
            print(f"\n❌ 跳转失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


async def test_adb_command_directly(emulator_id: int = 2):
    """直接测试ADB命令执行"""
    try:
        print(f"\n🔧 直接测试ADB命令执行")
        print("=" * 50)
        
        instagram_task = InstagramFollowTask(emulator_id)
        
        # 测试命令
        cmd = 'am start -a android.intent.action.VIEW -d "instagram://user?username=katoco0326"'
        print(f"测试命令: {cmd}")
        
        # 执行命令
        success, output = instagram_task.ld.execute_ld(emulator_id, cmd, silence=False)
        
        print(f"执行结果: {'✅ 成功' if success else '❌ 失败'}")
        print(f"输出: {output}")
        
        # 检查输出
        if success and output:
            if "Starting:" in output:
                print(f"✅ 命令正确执行，Intent已启动")
                return True
            elif "not found" in output or "error" in output.lower():
                print(f"❌ 命令执行有错误")
                return False
            else:
                print(f"⚠️ 命令执行状态不明")
                return success
        else:
            print(f"❌ 命令执行失败")
            return False
        
    except Exception as e:
        print(f"❌ 直接测试异常: {e}")
        return False


async def main():
    """主测试函数"""
    try:
        print("简单Instagram跳转测试工具")
        print("=" * 60)
        
        # 获取用户输入
        emulator_input = input("请输入模拟器ID (默认2): ").strip()
        emulator_id = int(emulator_input) if emulator_input else 2
        
        user_input = input("请输入要测试的用户名 (默认katoco0326): ").strip()
        user_id = user_input if user_input else "katoco0326"
        
        test_choice = input("选择测试类型 (1=完整跳转测试, 2=直接ADB测试, 3=全部, 默认1): ").strip()
        
        success = False
        
        if test_choice == "2":
            success = await test_adb_command_directly(emulator_id)
        elif test_choice == "3":
            adb_success = await test_adb_command_directly(emulator_id)
            if adb_success:
                await asyncio.sleep(2)
                success = await test_simple_navigation(emulator_id, user_id)
            else:
                success = False
        else:
            success = await test_simple_navigation(emulator_id, user_id)
        
        print(f"\n{'='*60}")
        print(f"最终结果: {'✅ 测试通过' if success else '❌ 测试失败'}")
        print(f"{'='*60}")
        
        if success:
            print(f"\n🎉 简单修复成功！")
            print(f"✅ Instagram跳转功能正常工作")
            print(f"💡 可以在实际关注流程中使用")
        else:
            print(f"\n🔧 需要进一步调试:")
            print(f"1. 检查Instagram应用是否已安装并登录")
            print(f"2. 确认用户名是否存在")
            print(f"3. 检查网络连接")
            print(f"4. 验证雷电模拟器ADB功能")
        
    except Exception as e:
        print(f"❌ 主程序异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
