# Instagram 私信流程步骤记录

## 流程概述
基于雷电模拟器原生API实现Instagram粉丝私信功能，从模拟器启动、V2Ray节点连接到私信发送的完整自动化流程。

## 完整步骤流程

### 阶段一：模拟器启动和环境准备 ✅ 已完成

#### 1. 启动雷电模拟器
- **API调用**: `unified_emulator_manager.start_emulator(emulator_id)`
- **验证启动**: 使用 `native_api.is_running(emulator_id)` 检查模拟器进程状态
- **等待桌面**: 等待Android桌面完全加载
- **实现状态**: ✅ 已完成（由统一模拟器管理器处理）

#### 2. 验证模拟器桌面稳定性
- **方法名**: `_step_verify_desktop_stable()`
- **API调用**: `emulator_manager.get_emulator_status(emulator_id)`
- **验证方法**: 检查模拟器运行状态和Android系统状态
- **技术实现**: 使用统一模拟器管理器的状态检测
- **检测速度**: 快速检测（不使用ADB）
- **实现状态**: ✅ 已完成

#### 3. 检测应用安装状态
- **方法名**: `_step_check_app_installation()`
- **V2Ray检测**: 包名 `com.v2ray.ang`
- **Instagram检测**: 包名 `com.instagram.android`
- **API调用**: `ld.has_install(emulator_id, package_name)`
- **检测方法**: 基于包名的应用安装状态检测
- **返回结果**: 应用安装状态和版本信息
- **实现状态**: ✅ 已完成

### 阶段二：V2Ray节点连接 ✅ 已完成

#### 4. 启动V2Ray应用
- **方法名**: `_step_app_launch_v2ray()`
- **包名**: `com.v2ray.ang`
- **启动API**: `ld.runApp(emulator_id, package_name)`
- **验证启动**: 通过Activity检测验证启动成功
- **等待时间**: 3秒等待应用启动
- **验证策略**: Activity名称包含"v2ray"关键字
- **实现状态**: ✅ 已完成

#### 5. 检查节点列表状态
- **方法名**: `_step_check_node_list()`
- **查找节点**: `ld.find_node(resource_id="com.v2ray.ang:id/tv_name")`
- **如果列表不为空**: 直接进行节点连接流程
- **如果列表为空**: 需要导入节点（暂时返回失败，待实现导入流程）
- **实现状态**: ✅ 已完成（基础检测逻辑）

#### 6. 连接V2Ray节点
- **方法名**: `_step_connect_v2ray_node()`
- **检查连接状态**: `ld.find_node(resource_id="com.v2ray.ang:id/tv_test_state")`
- **连接按钮**: `ld.find_node(resource_id="com.v2ray.ang:id/fab")`
- **连接逻辑**: 
  - "已连接" → 无需重复连接，直接返回成功
  - "未连接" → 点击连接按钮，等待连接完成
- **等待连接**: 最多等待10秒，每秒检查一次状态
- **实现状态**: ✅ 已完成

#### 7. 测试节点延迟
- **方法名**: `_step_test_node_latency()`
- **测试状态元素**: `ld.find_node(resource_id="com.v2ray.ang:id/tv_test_state")`
- **测试逻辑**: 
  - 点击"已连接，点击测试连接"开始测试
  - 监控测试结果（最多等待30秒）
  - "连接成功" → 测试成功，等待1秒后返回
  - "失败" → 失败计数+1，未达到最大次数时点击重置UI继续
  - "测试中" → 继续等待测试完成
- **失败处理**: 最多3次失败后触发节点切换
- **节点切换**: 最多8次节点切换尝试
- **实现状态**: ✅ 已完成

#### 8. V2Ray节点切换（辅助功能）
- **方法名**: `_switch_to_new_node()`
- **执行步骤**:
  1. 检查雷电API可用性
  2. 执行智能滑动浏览更多节点
  3. 执行随机节点选择
  4. 重新连接V2Ray节点
- **智能滑动**: `_perform_intelligent_swipe()` - 随机滑动1-5次
- **随机选择**: `_select_random_node()` - 从可见节点中随机选择
- **实现状态**: ✅ 已完成

### 阶段三：Instagram应用启动 ✅ 已完成

#### 9. 启动Instagram应用
- **方法名**: `_step_app_launch_instagram()`
- **包名**: `com.instagram.android`
- **启动API**: `ld.runApp(emulator_id, package_name)`
- **验证启动**:
  - Activity检测：检查是否包含"instagram"关键字
  - UI元素检测：查找主页标识或登录页面元素
- **等待时间**: 3秒等待应用启动
- **实现状态**: ✅ 已完成

#### 10. Instagram页面状态检测
- **方法名**: `_step_detect_instagram_page_status()`
- **检测方式**:
  - Activity状态检测 (MainActivity, ChallengeActivity等)
  - UI元素检测 (主页特征、登录元素、异常状态)
- **状态分类**:
  - **正常状态**:
    - "正常-在主页面" → 可继续执行任务
    - "正常-启动加载中" → Instagram启动过程中，需要等待
  - **异常状态**:
    - "异常-需要登录" → 需要手动登录
    - "异常-账号申诉" → 账号申诉状态
    - "异常-身份验证" → 需要身份验证
    - "异常-需要继续操作" → 需要用户操作
    - "异常-未知状态" → 无法识别的状态
    - "异常-检测失败" → 检测过程异常
  - **预留异常状态** (待扩展):
    - "异常-账号被封" → 账号被封禁
    - "异常-网络异常" → 网络连接问题
    - "异常-需要更新" → 应用需要更新
    - "异常-权限请求" → 权限请求页面
    - "异常-服务器维护" → 服务器维护中
- **重试机制**:
  - 检测到"正常-启动加载中"时，等待5秒后重试
  - 最多重试3次，总等待时间15秒
  - 超时后返回"Instagram启动超时"
- **实现状态**: ✅ 已完成

#### 11. 导航到个人主页
- **查找个人主页标识**: `ld.find_node(resource_id="com.instagram.android:id/profile_tab")`
- **点击进入**: `ld.click_node(profile_tab)`
- **等待加载**: 等待个人主页完全显示
- **实现状态**: ❌ 待实现

#### 12. 获取粉丝数量信息
- **查找粉丝数标识**: `ld.find_node(resource_id="com.instagram.android:id/row_profile_header_textview_followers_count")`
- **解析数量**: 处理K、M等单位转换
- **验证有效性**: 确保有足够粉丝进行私信
- **实现状态**: ❌ 待实现

#### 13. 打开粉丝列表
- **方法名**: `_step_business_navigate_to_followers()` (TODO)
- **点击粉丝数**: `ld.click_node(followers_count_node)`
- **等待列表加载**: 确认粉丝列表界面显示
- **验证列表**: 检查是否有可见粉丝项目
- **实现状态**: ❌ 待实现

### 阶段四：批量私信发送 🚧 待开发

#### 14. 初始化私信任务
- **加载去重记录**: 读取 `sent_users.txt` 文件
- **初始化计数器**: `sent_count = 0`
- **设置任务参数**: 目标数量、超时时间等
- **实现状态**: ❌ 待实现

#### 15. 批量私信发送循环
- **方法名**: `_step_business_send_mass_dm()` (TODO)
- **主循环逻辑**: 
  ```python
  while sent_count < 私信任务数量 and not stop_flag:
      # 获取当前屏幕可见粉丝
      followers = ld.find_nodes(resource_id="com.instagram.android:id/follow_list_container")
      
      # 遍历每个粉丝
      for follower in followers:
          # 提取粉丝信息
          username_node = ld.find_children(follower, resource_id="com.instagram.android:id/follow_list_username")
          username = username_node[0].get('text', '')
          
          # 检查去重（避免重复发送）
          if username in sent_users:
              continue
          
          # 点击粉丝头像进入主页
          # 点击"发消息"按钮
          # 输入私信内容
          # 点击发送按钮
          # 记录已发送用户
          # 返回粉丝列表
          
          sent_count += 1
          if sent_count >= 私信任务数量:
              break
      
      # 滚动加载更多粉丝
      # 检查是否到达底部
  ```
- **实现状态**: ❌ 待实现

#### 16. 发送记录管理
- **方法名**: `_step_business_manage_sent_records()` (TODO)
- **去重机制**: 使用 `sent_users.txt` 文件记录已发送用户
- **记录格式**: 每行一个用户名
- **实现状态**: ❌ 待实现

## 已实现的核心方法总结

### 阶段一方法 ✅
- `_step_verify_desktop_stable()` - 验证模拟器桌面稳定性
- `_step_check_app_installation()` - 检测应用安装状态

### 阶段二方法 ✅  
- `_step_app_launch_v2ray()` - 启动V2Ray应用
- `_step_check_node_list()` - 检查节点列表状态
- `_step_connect_v2ray_node()` - 连接V2Ray节点
- `_step_test_node_latency()` - 测试节点延迟
- `_switch_to_new_node()` - V2Ray节点切换
- `_perform_intelligent_swipe()` - 智能滑动浏览节点
- `_select_random_node()` - 随机节点选择

### 阶段三方法 ✅ (已完成基础功能)
- `_step_app_launch_instagram()` - 启动Instagram应用 ✅
- `_step_detect_instagram_page_status()` - Instagram页面状态检测 ✅
- `_step_business_navigate_to_followers()` - 导航到粉丝列表 ❌ (待实现)

### 阶段四方法 ❌ (待实现)
- `_step_business_send_mass_dm()` - 批量私信发送循环
- `_step_business_manage_sent_records()` - 发送记录管理

## 接下来的开发重点

### 🎯 阶段三开发任务
1. **Instagram应用启动**: 实现应用启动和验证逻辑
2. **登录状态检测**: 处理各种登录状态和权限请求
3. **页面导航**: 实现从主页到粉丝列表的导航流程
4. **粉丝列表解析**: 实现粉丝信息提取和列表处理

### 🎯 阶段四开发任务
1. **私信发送循环**: 实现核心的批量私信发送逻辑
2. **去重机制**: 实现用户去重和记录管理
3. **错误处理**: 处理各种异常情况和重试机制
4. **进度反馈**: 实现任务进度统计和状态反馈

## 技术架构特点

### 已实现的技术优势
1. **原生API集成**: 完全基于雷电模拟器原生API，性能优异
2. **异步执行**: 全异步设计，支持并发处理
3. **错误处理**: 完善的异常处理和重试机制
4. **状态管理**: 清晰的任务状态和进度管理
5. **配置热加载**: 支持运行时配置更新

### 待完善的功能
1. **节点导入流程**: V2Ray节点订阅导入功能
2. **Instagram UI自动化**: 完整的Instagram操作流程
3. **批量处理优化**: 大规模私信发送的性能优化
4. **监控和日志**: 更详细的执行监控和日志记录
