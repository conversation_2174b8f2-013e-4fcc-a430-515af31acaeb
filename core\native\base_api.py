#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 雷电模拟器基础API统一封装
========================================
功能描述: 封装所有雷电模拟器的基础操作API
实现方式: 基于ldconsole.exe命令行工具，参考LeiDian模块逻辑

主要功能:
1. 模拟器控制 - 启动、停止、重启、状态检测
2. 应用管理 - 安装、卸载、启动、停止应用
3. 基础交互 - 点击、滑动、输入文本
4. 状态检测 - 获取窗口句柄、Activity等
5. 基础截图 - 窗口截图功能

技术实现:
- 使用subprocess调用ldconsole.exe
- 异步命令执行支持
- 完整的错误处理和日志记录
- 基于参考代码逻辑但完全独立实现

调用关系: 被各种专业引擎和管理器调用
注意事项: 不依赖任何参考代码，纯净实现
========================================
"""

import asyncio
import subprocess
import logging
import time
from pathlib import Path
from typing import Optional, Tuple, List
from dataclasses import dataclass

# 导入配置管理器
from ..simple_config import get_config_manager
from ..logger_manager import log_info, log_error


@dataclass
class EmulatorInfo:
    """模拟器信息数据类"""
    index: int
    title: str
    top_hwnd: int
    bind_hwnd: int
    is_android: bool
    pid: int
    vbox_pid: int
    width: int
    height: int
    dpi: int


class LeiDianNativeAPI:
    """雷电模拟器基础API统一封装"""
    
    def __init__(self, emulator_path: str = None):
        """
        初始化雷电原生API
        
        Args:
            emulator_path: 雷电模拟器安装路径，如果为None则自动检测
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config_manager = get_config_manager()
        
        # 设置雷电模拟器路径
        self.emulator_path = emulator_path or self._detect_emulator_path()
        if not self.emulator_path:
            raise Exception("未找到雷电模拟器安装路径")
        
        self.ldconsole_path = Path(self.emulator_path) / "ldconsole.exe"
        if not self.ldconsole_path.exists():
            raise Exception(f"ldconsole.exe不存在: {self.ldconsole_path}")
        
        log_info(f"雷电原生API初始化成功: {self.emulator_path}", component="LeiDianNativeAPI")
    
    def _detect_emulator_path(self) -> Optional[str]:
        """自动检测雷电模拟器路径"""
        # 首先从配置获取
        config_path = self.config_manager.get("emulator_path", "")
        if config_path and Path(config_path, "ldconsole.exe").exists():
            return config_path
        
        # 自动检测常见路径
        common_paths = [
            "G:/leidian/LDPlayer9",
            "D:/LDPlayer9", 
            "C:/LDPlayer9",
            "E:/LDPlayer9",
            "F:/LDPlayer9",
            "C:/Program Files/LDPlayer9",
            "D:/Program Files/LDPlayer9"
        ]
        
        for path in common_paths:
            if Path(path, "ldconsole.exe").exists():
                return path
        
        return None
    
    async def _execute_command(self, command: List[str], timeout: int = 30) -> Tuple[bool, str]:
        """
        执行ldconsole命令
        
        Args:
            command: 命令参数列表
            timeout: 超时时间（秒）
            
        Returns:
            (成功标志, 输出内容或错误信息)
        """
        try:
            full_command = [str(self.ldconsole_path)] + command
            
            process = await asyncio.create_subprocess_exec(
                *full_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                creationflags=0x08000000  # CREATE_NO_WINDOW
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), 
                timeout=timeout
            )
            
            if process.returncode == 0:
                output = stdout.decode('utf-8', errors='ignore').strip()
                return True, output
            else:
                error = stderr.decode('utf-8', errors='ignore').strip()
                return False, error
                
        except asyncio.TimeoutError:
            return False, f"命令执行超时({timeout}秒)"
        except Exception as e:
            return False, f"命令执行异常: {str(e)}"
    
    # ========================================================================
    # 🎯 模拟器控制API
    # ========================================================================
    
    async def launch(self, emulator_id: int) -> bool:
        """
        启动模拟器
        
        Args:
            emulator_id: 模拟器ID
            
        Returns:
            是否启动成功
        """
        try:
            log_info(f"启动模拟器 {emulator_id}", component="LeiDianNativeAPI")
            
            success, output = await self._execute_command(['launch', '--index', str(emulator_id)])
            
            if success:
                log_info(f"模拟器 {emulator_id} 启动命令执行成功", component="LeiDianNativeAPI")
                return True
            else:
                log_error(f"模拟器 {emulator_id} 启动失败: {output}", component="LeiDianNativeAPI")
                return False
                
        except Exception as e:
            log_error(f"启动模拟器 {emulator_id} 异常: {e}", component="LeiDianNativeAPI")
            return False
    
    async def quit(self, emulator_id: int) -> bool:
        """
        关闭模拟器
        
        Args:
            emulator_id: 模拟器ID
            
        Returns:
            是否关闭成功
        """
        try:
            log_info(f"关闭模拟器 {emulator_id}", component="LeiDianNativeAPI")
            
            success, output = await self._execute_command(['quit', '--index', str(emulator_id)])
            
            if success:
                log_info(f"模拟器 {emulator_id} 关闭成功", component="LeiDianNativeAPI")
                return True
            else:
                log_error(f"模拟器 {emulator_id} 关闭失败: {output}", component="LeiDianNativeAPI")
                return False
                
        except Exception as e:
            log_error(f"关闭模拟器 {emulator_id} 异常: {e}", component="LeiDianNativeAPI")
            return False
    
    async def reboot(self, emulator_id: int) -> bool:
        """
        重启模拟器
        
        Args:
            emulator_id: 模拟器ID
            
        Returns:
            是否重启成功
        """
        try:
            log_info(f"重启模拟器 {emulator_id}", component="LeiDianNativeAPI")
            
            success, output = await self._execute_command(['reboot', '--index', str(emulator_id)])
            
            if success:
                log_info(f"模拟器 {emulator_id} 重启成功", component="LeiDianNativeAPI")
                return True
            else:
                log_error(f"模拟器 {emulator_id} 重启失败: {output}", component="LeiDianNativeAPI")
                return False
                
        except Exception as e:
            log_error(f"重启模拟器 {emulator_id} 异常: {e}", component="LeiDianNativeAPI")
            return False
    
    async def is_running(self, emulator_id: int) -> Tuple[bool, bool, Optional[EmulatorInfo]]:
        """
        检查模拟器运行状态
        
        Args:
            emulator_id: 模拟器ID
            
        Returns:
            (是否运行, 是否Android已启动, 模拟器信息)
        """
        try:
            success, output = await self._execute_command(['list2'])
            
            if not success:
                return False, False, None
            
            # 解析list2输出
            for line in output.split('\n'):
                if line.strip() and ',' in line:
                    parts = line.split(',')
                    if len(parts) >= 10:
                        try:
                            current_id = int(parts[0])
                            if current_id == emulator_id:
                                info = EmulatorInfo(
                                    index=current_id,
                                    title=parts[1],
                                    top_hwnd=int(parts[2]) if parts[2] != '0' else 0,
                                    bind_hwnd=int(parts[3]) if parts[3] != '0' else 0,
                                    is_android=parts[4] == '1',
                                    pid=int(parts[5]) if parts[5] != '-1' else -1,
                                    vbox_pid=int(parts[6]) if parts[6] != '-1' else -1,
                                    width=int(parts[7]),
                                    height=int(parts[8]),
                                    dpi=int(parts[9])
                                )
                                
                                is_running = info.pid != -1
                                is_android = info.is_android
                                
                                return is_running, is_android, info
                                
                        except (ValueError, IndexError):
                            continue
            
            return False, False, None
            
        except Exception as e:
            log_error(f"检查模拟器 {emulator_id} 状态异常: {e}", component="LeiDianNativeAPI")
            return False, False, None

    # ========================================================================
    # 🎯 应用管理API
    # ========================================================================

    async def install_app(self, emulator_id: int, apk_path: str) -> bool:
        """
        安装APK应用

        Args:
            emulator_id: 模拟器ID
            apk_path: APK文件路径

        Returns:
            是否安装成功
        """
        try:
            log_info(f"模拟器 {emulator_id} 安装应用: {apk_path}", component="LeiDianNativeAPI")

            success, output = await self._execute_command([
                'installapp', '--index', str(emulator_id), '--filename', apk_path
            ], timeout=120)  # 安装可能需要更长时间

            if success:
                log_info(f"模拟器 {emulator_id} 应用安装成功", component="LeiDianNativeAPI")
                return True
            else:
                log_error(f"模拟器 {emulator_id} 应用安装失败: {output}", component="LeiDianNativeAPI")
                return False

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 安装应用异常: {e}", component="LeiDianNativeAPI")
            return False

    async def uninstall_app(self, emulator_id: int, package_name: str) -> bool:
        """
        卸载应用

        Args:
            emulator_id: 模拟器ID
            package_name: 应用包名

        Returns:
            是否卸载成功
        """
        try:
            log_info(f"模拟器 {emulator_id} 卸载应用: {package_name}", component="LeiDianNativeAPI")

            success, output = await self._execute_command([
                'uninstallapp', '--index', str(emulator_id), '--packagename', package_name
            ])

            if success:
                log_info(f"模拟器 {emulator_id} 应用卸载成功", component="LeiDianNativeAPI")
                return True
            else:
                log_error(f"模拟器 {emulator_id} 应用卸载失败: {output}", component="LeiDianNativeAPI")
                return False

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 卸载应用异常: {e}", component="LeiDianNativeAPI")
            return False

    async def app_version(self, emulator_id: int, package_name: str) -> str:
        """
        获取应用版本信息
        ========================================
        功能描述: 使用雷电模拟器原生API获取应用版本信息
        实现方式: 使用ldconsole的原生命令，避免使用ADB
        ========================================

        Args:
            emulator_id: 模拟器ID
            package_name: 应用包名

        Returns:
            应用版本字符串，如果应用未安装则返回空字符串
        """
        try:
            log_info(f"模拟器 {emulator_id} 获取应用版本: {package_name}", component="LeiDianNativeAPI")

            # 🎯 使用雷电模拟器原生API获取应用版本
            # 方法1：尝试使用action命令获取应用信息
            success, output = await self._execute_command([
                'action', '--index', str(emulator_id),
                '--key', 'call.app.info', '--value', package_name
            ])

            if success and output.strip():
                # 解析输出中的版本信息
                if 'version' in output.lower():
                    lines = output.split('\n')
                    for line in lines:
                        if 'version' in line.lower():
                            version = line.split(':')[-1].strip()
                            if version:
                                log_info(f"模拟器 {emulator_id} 应用版本: {package_name} = {version}", component="LeiDianNativeAPI")
                                return version

            # 🎯 方法2：通过检查应用是否能启动来判断是否安装
            # 如果应用已安装，runapp命令通常会成功
            test_success, _ = await self._execute_command([
                'runapp', '--index', str(emulator_id), '--packagename', package_name
            ])

            if test_success:
                # 应用启动成功，说明已安装，返回一个标识版本
                log_info(f"模拟器 {emulator_id} 应用已安装: {package_name}", component="LeiDianNativeAPI")

                # 立即关闭应用避免影响后续操作
                await self._execute_command([
                    'killapp', '--index', str(emulator_id), '--packagename', package_name
                ])

                return "installed"  # 返回标识表示已安装
            else:
                log_info(f"模拟器 {emulator_id} 应用未安装: {package_name}", component="LeiDianNativeAPI")
                return ""

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 获取应用版本异常: {e}", component="LeiDianNativeAPI")
            return ""

    async def run_app(self, emulator_id: int, package_name: str) -> bool:
        """
        启动应用

        Args:
            emulator_id: 模拟器ID
            package_name: 应用包名

        Returns:
            是否启动成功
        """
        try:
            log_info(f"模拟器 {emulator_id} 启动应用: {package_name}", component="LeiDianNativeAPI")

            success, output = await self._execute_command([
                'runapp', '--index', str(emulator_id), '--packagename', package_name
            ])

            if success:
                log_info(f"模拟器 {emulator_id} 应用启动成功", component="LeiDianNativeAPI")
                return True
            else:
                log_error(f"模拟器 {emulator_id} 应用启动失败: {output}", component="LeiDianNativeAPI")
                return False

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 启动应用异常: {e}", component="LeiDianNativeAPI")
            return False

    async def kill_app(self, emulator_id: int, package_name: str) -> bool:
        """
        关闭应用

        Args:
            emulator_id: 模拟器ID
            package_name: 应用包名

        Returns:
            是否关闭成功
        """
        try:
            log_info(f"模拟器 {emulator_id} 关闭应用: {package_name}", component="LeiDianNativeAPI")

            success, output = await self._execute_command([
                'killapp', '--index', str(emulator_id), '--packagename', package_name
            ])

            if success:
                log_info(f"模拟器 {emulator_id} 应用关闭成功", component="LeiDianNativeAPI")
                return True
            else:
                log_error(f"模拟器 {emulator_id} 应用关闭失败: {output}", component="LeiDianNativeAPI")
                return False

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 关闭应用异常: {e}", component="LeiDianNativeAPI")
            return False

    async def is_app_running(self, emulator_id: int, package_name: str) -> bool:
        """
        检查应用是否正在运行
        ========================================
        功能描述: 使用纯原生API检查应用运行状态，不使用ADB
        实现方式: 通过模拟器状态和应用安装状态推断
        ========================================

        Args:
            emulator_id: 模拟器ID
            package_name: 应用包名

        Returns:
            是否正在运行
        """
        try:
            log_info(f"模拟器 {emulator_id} 检查应用运行状态: {package_name}", component="LeiDianNativeAPI")

            # 🎯 不使用ADB，通过模拟器状态推断应用运行状态
            # 1. 检查模拟器是否运行且Android已启动
            is_running, is_android, _ = await self.is_running(emulator_id)

            if not is_running or not is_android:
                log_info(f"模拟器 {emulator_id} 未运行或Android未启动", component="LeiDianNativeAPI")
                return False

            # 2. 检查应用是否已安装
            app_version = await self.app_version(emulator_id, package_name)
            if not app_version:
                log_info(f"模拟器 {emulator_id} 应用未安装: {package_name}", component="LeiDianNativeAPI")
                return False

            # 3. 如果模拟器运行且应用已安装，假设应用可能在运行
            # 这是一个宽松的检测策略，避免ADB依赖
            log_info(f"模拟器 {emulator_id} 应用状态推断为运行中: {package_name}", component="LeiDianNativeAPI")
            return True

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 检查应用运行状态异常: {e}", component="LeiDianNativeAPI")
            return False

    async def detect_app_by_image(self, emulator_id: int, image_path: str) -> Optional[Tuple[int, int, float, float]]:
        """
        通过图像识别检测应用安装状态
        ========================================
        功能描述: 使用图色识别技术检测应用图标，替代传统的包名检测方式
        实现方式: 基于图像识别引擎，支持多尺度模板匹配
        技术特点: 置信度≥0.75，平均耗时30ms，支持0.1-1.9倍缩放范围

        Args:
            emulator_id: 模拟器ID
            image_path: 应用图标图片路径（相对于项目根目录）

        Returns:
            检测结果元组 (x, y, elapsed_time, confidence) 或 None
            - x, y: 图标中心点坐标
            - elapsed_time: 检测耗时（秒）
            - confidence: 置信度（0-1）
        """
        try:
            log_info(f"模拟器 {emulator_id} 开始图像识别检测: {image_path}", component="LeiDianNativeAPI")

            # 🎯 导入图像识别引擎
            from .image_recognition_engine import detect_app_by_image

            # 🎯 执行图像识别
            result = await detect_app_by_image(emulator_id, image_path)

            if result:
                x, y, elapsed_time, confidence = result
                log_info(f"模拟器 {emulator_id} 图像识别成功: 位置({x}, {y}), 置信度{confidence:.3f}, 耗时{elapsed_time:.3f}秒", component="LeiDianNativeAPI")
                return result
            else:
                log_info(f"模拟器 {emulator_id} 图像识别失败: 未找到匹配的应用图标", component="LeiDianNativeAPI")
                return None

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 图像识别异常: {e}", component="LeiDianNativeAPI")
            return None

    # ========================================================================
    # 🎯 基础交互API
    # ========================================================================

    async def touch(self, emulator_id: int, x: int, y: int) -> bool:
        """
        点击操作

        Args:
            emulator_id: 模拟器ID
            x: X坐标
            y: Y坐标

        Returns:
            是否点击成功
        """
        try:
            success, output = await self._execute_command([
                'action', '--index', str(emulator_id), '--key', 'call.touch', '--value', f'{x},{y}'
            ])

            if success:
                return True
            else:
                log_error(f"模拟器 {emulator_id} 点击失败: {output}", component="LeiDianNativeAPI")
                return False

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 点击异常: {e}", component="LeiDianNativeAPI")
            return False

    async def swipe(self, emulator_id: int, start_x: int, start_y: int, end_x: int, end_y: int, duration: int = 1000) -> bool:
        """
        滑动操作

        Args:
            emulator_id: 模拟器ID
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration: 滑动持续时间(毫秒)

        Returns:
            是否滑动成功
        """
        try:
            success, output = await self._execute_command([
                'action', '--index', str(emulator_id), '--key', 'call.swipe',
                '--value', f'{start_x},{start_y},{end_x},{end_y},{duration}'
            ])

            if success:
                return True
            else:
                log_error(f"模拟器 {emulator_id} 滑动失败: {output}", component="LeiDianNativeAPI")
                return False

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 滑动异常: {e}", component="LeiDianNativeAPI")
            return False

    async def input_text(self, emulator_id: int, text: str) -> bool:
        """
        输入文本

        Args:
            emulator_id: 模拟器ID
            text: 要输入的文本

        Returns:
            是否输入成功
        """
        try:
            success, output = await self._execute_command([
                'action', '--index', str(emulator_id), '--key', 'call.input', '--value', text
            ])

            if success:
                return True
            else:
                log_error(f"模拟器 {emulator_id} 输入文本失败: {output}", component="LeiDianNativeAPI")
                return False

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 输入文本异常: {e}", component="LeiDianNativeAPI")
            return False

    # ========================================================================
    # 🎯 状态检测API
    # ========================================================================

    async def get_hwnd(self, emulator_id: int) -> Optional[Tuple[int, int]]:
        """
        获取窗口句柄

        Args:
            emulator_id: 模拟器ID

        Returns:
            (顶层句柄, 绑定句柄) 或 None
        """
        try:
            _, _, info = await self.is_running(emulator_id)

            if info:
                return info.top_hwnd, info.bind_hwnd
            else:
                return None

        except Exception as e:
            log_error(f"获取模拟器 {emulator_id} 句柄异常: {e}", component="LeiDianNativeAPI")
            return None

    async def get_activity_name(self, emulator_id: int) -> Optional[str]:
        """
        获取当前Activity名称

        注意: 该功能暂时禁用，因为没有调用者且原实现依赖ADB
        如需启用，可以使用雷电原生命令替代ADB实现

        Args:
            emulator_id: 模拟器ID

        Returns:
            Activity名称或None（当前总是返回None）
        """
        # Activity检测功能已禁用
        log_info(f"Activity检测功能已禁用（模拟器 {emulator_id}）", component="LeiDianNativeAPI")
        return None

    async def get_emulator_info(self, emulator_id: int) -> Optional[EmulatorInfo]:
        """
        获取模拟器详细信息
        ========================================
        功能描述: 获取指定模拟器的完整状态信息
        实现方式: 使用ldconsole list2命令，完全不依赖ADB
        ========================================

        Args:
            emulator_id: 模拟器ID

        Returns:
            模拟器信息对象或None
        """
        try:
            _, _, info = await self.is_running(emulator_id)
            return info
        except Exception as e:
            log_error(f"获取模拟器 {emulator_id} 信息异常: {e}", component="LeiDianNativeAPI")
            return None

    async def is_desktop_stable(self, emulator_id: int, timeout: int = 30) -> bool:
        """
        检测模拟器桌面稳定性
        ========================================
        功能描述: 使用窗口状态检测桌面是否稳定，完全不使用ADB
        验证方法: Android启动状态 + 窗口可见性
        ========================================

        Args:
            emulator_id: 模拟器ID
            timeout: 超时时间（秒）

        Returns:
            是否桌面稳定
        """
        try:
            import win32gui
            import time

            log_info(f"模拟器 {emulator_id} 开始桌面稳定性检测，超时{timeout}秒", component="LeiDianNativeAPI")

            start_time = time.time()
            check_interval = 2  # 每2秒检查一次

            while time.time() - start_time < timeout:
                try:
                    # 获取模拟器信息
                    info = await self.get_emulator_info(emulator_id)

                    if info:
                        android_started = info.is_android
                        top_hwnd = info.top_hwnd

                        log_info(f"模拟器 {emulator_id} 状态: Android启动={android_started}, 窗口句柄={top_hwnd}", component="LeiDianNativeAPI")

                        if android_started and top_hwnd > 0:
                            # 检查窗口可见性
                            try:
                                is_visible = win32gui.IsWindowVisible(top_hwnd)
                                window_title = win32gui.GetWindowText(top_hwnd)

                                log_info(f"模拟器 {emulator_id} 窗口状态: 可见={is_visible}, 标题='{window_title}'", component="LeiDianNativeAPI")

                                if is_visible:
                                    elapsed_time = time.time() - start_time
                                    log_info(f"模拟器 {emulator_id} 桌面稳定性验证成功，耗时: {elapsed_time:.1f}秒", component="LeiDianNativeAPI")
                                    return True

                            except Exception as e:
                                log_error(f"模拟器 {emulator_id} 窗口状态检测异常: {e}", component="LeiDianNativeAPI")
                    else:
                        log_info(f"模拟器 {emulator_id} 未获取到模拟器信息，继续等待", component="LeiDianNativeAPI")

                    # 等待后重试
                    await asyncio.sleep(check_interval)

                except Exception as e:
                    log_error(f"模拟器 {emulator_id} 桌面检测异常: {e}", component="LeiDianNativeAPI")
                    await asyncio.sleep(check_interval)

            log_error(f"模拟器 {emulator_id} 桌面稳定性检测超时", component="LeiDianNativeAPI")
            return False

        except Exception as e:
            log_error(f"模拟器 {emulator_id} 桌面稳定性检测异常: {e}", component="LeiDianNativeAPI")
            return False


# ========================================================================
# 🎯 全局实例管理
# ========================================================================

_native_api_instance = None

def get_native_api(emulator_path: str = None) -> LeiDianNativeAPI:
    """
    获取雷电原生API全局实例

    Args:
        emulator_path: 模拟器路径，仅在首次调用时有效

    Returns:
        LeiDianNativeAPI实例
    """
    global _native_api_instance

    if _native_api_instance is None:
        _native_api_instance = LeiDianNativeAPI(emulator_path)

    return _native_api_instance
