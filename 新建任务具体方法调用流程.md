# 🎯 具体方法调用流程

## 📋 Instagram私信任务完整调用链

### **详细方法调用流程：**
```
UI点击"Instagram私信" 
    ↓
MainWindowV2._on_instagram_dm_start()
    ↓
异步桥梁.execute_operation('instagram_dm_task', task_data)
    ↓
FixedAsyncBridge._execute_async_operation('instagram_dm_task', data)
    ↓
FixedAsyncBridge._handle_instagram_dm_task(data)
    ↓
统一模拟器管理器.batch_start_emulators(emulator_ids) [启动模拟器]
    ↓
FixedAsyncBridge._schedule_instagram_tasks_after_startup(emulator_ids, data) [安排任务]
    ↓
FixedAsyncBridge._wait_and_execute_instagram_task(emulator_id, task_data) [等待启动完成]
    ↓
FixedAsyncBridge._execute_instagram_task(emulator_id, task_data) [执行Instagram任务]
    ↓
InstagramDMTask.__init__(emulator_id) [✅优化：任务类内部获取配置]
    ↓
InstagramDMTask._load_config() [从统一配置系统加载配置] ✅已实现
    ↓
InstagramDMTask._register_config_observer() [注册配置热加载观察者] ✅已实现
    ↓
InstagramDMTask.execute() [具体业务逻辑]
    ↓
InstagramDMTask._stage1_app_detection() [阶段一：应用检测]
    ↓
InstagramDMTask._stage2_v2ray_connection() [阶段二：V2Ray连接] ✅已实现
    ↓
InstagramDMTask._start_v2ray_app() [V2Ray启动] ✅已实现
    ↓
InstagramDMTask._verify_v2ray_started() [V2Ray验证] ✅已实现
    ↓
InstagramDMTask._stage3_instagram_launch() [阶段三：Instagram启动]
    ↓
InstagramDMTask._stage4_mass_dm() [阶段四：批量私信发送]
```

---

## 📋 抖音关注任务完整调用链

### **详细方法调用流程：**
```
UI点击"抖音关注"
    ↓
MainWindowV2._on_douyin_follow_start()
    ↓
异步桥梁.execute_operation('douyin_follow_task', task_data)
    ↓
FixedAsyncBridge._execute_async_operation('douyin_follow_task', data)
    ↓
FixedAsyncBridge._handle_douyin_follow_task(data)
    ↓
统一模拟器管理器.batch_start_emulators(emulator_ids) [启动模拟器]
    ↓
FixedAsyncBridge._schedule_douyin_follow_tasks_after_startup(emulator_ids, data) [安排任务]
    ↓
FixedAsyncBridge._wait_and_execute_douyin_follow_task(emulator_id, task_data) [等待启动完成]
    ↓
FixedAsyncBridge._execute_douyin_follow_task(emulator_id, task_data) [执行抖音任务]
    ↓
DouyinFollowTask.execute() [具体业务逻辑]
    ↓
DouyinFollowTask._stage1_app_detection() [阶段一：应用检测]
    ↓
DouyinFollowTask._stage2_network_connection() [阶段二：网络连接检查]
    ↓
DouyinFollowTask._stage3_douyin_launch() [阶段三：抖音应用启动]
    ↓
DouyinFollowTask._stage4_mass_follow() [阶段四：批量用户关注]
```

---

## 📋 文件和方法对应关系

### **UI层方法：**
```
文件：ui/main_window_v2.py
方法：
├── _on_instagram_dm_start()     # Instagram私信任务触发
├── _on_douyin_follow_start()    # 抖音关注任务触发
├── _on_tiktok_like_start()      # TikTok点赞任务触发
└── get_selected_emulators()     # 获取选中的模拟器
```

### **异步桥梁方法：**
```
文件：core/async_bridge.py
方法：
├── execute_operation(operation, data)                    # 统一操作入口
├── _execute_async_operation(operation, data)             # 异步操作分发
├── _handle_instagram_dm_task(data)                       # Instagram任务处理
├── _schedule_instagram_tasks_after_startup(ids, data)    # Instagram任务安排
├── _wait_and_execute_instagram_task(id, data)           # Instagram任务等待执行
├── _execute_instagram_task(id, data)                     # Instagram任务执行
├── _handle_douyin_follow_task(data)                      # 抖音任务处理
├── _schedule_douyin_follow_tasks_after_startup(ids, data) # 抖音任务安排
├── _wait_and_execute_douyin_follow_task(id, data)       # 抖音任务等待执行
└── _execute_douyin_follow_task(id, data)                 # 抖音任务执行
```

### **模拟器管理器方法：**
```
文件：core/unified_emulator_manager.py
方法：
├── batch_start_emulators(emulator_ids)    # 批量启动模拟器
├── get_emulator_status(emulator_id)       # 获取模拟器状态
├── get_native_api()                       # 获取原生API
└── start_emulator(emulator_id)            # 启动单个模拟器
```

### **Instagram任务方法：**
```
文件：core/instagram_task.py
方法：
├── execute()                              # 主执行方法
├── _stage1_app_detection()                # 阶段一：应用检测
├── _stage2_v2ray_connection()             # 阶段二：V2Ray连接 ✅已实现
├── _start_v2ray_app()                     # V2Ray启动 ✅已实现
├── _verify_v2ray_started()                # V2Ray验证 ✅已实现
├── _stage3_instagram_launch()             # 阶段三：Instagram启动
└── _stage4_mass_dm()                      # 阶段四：批量私信发送
```

### **抖音任务方法：**
```
文件：core/douyin_follow_task.py
方法：
├── execute()                              # 主执行方法
├── _stage1_app_detection()                # 阶段一：应用检测
├── _stage2_network_connection()           # 阶段二：网络连接检查
├── _stage3_douyin_launch()                # 阶段三：抖音应用启动
└── _stage4_mass_follow()                  # 阶段四：批量用户关注
```

---

## 📋 关键API调用

### **模拟器原生API调用：**
```
文件：core/native/base_api.py
方法：
├── run_app(emulator_id, package_name)     # 启动应用
├── is_app_running(emulator_id, package)   # 检查应用运行状态
├── kill_app(emulator_id, package_name)    # 关闭应用
└── install_app(emulator_id, apk_path)     # 安装应用
```

### **V2Ray启动调用链：**
```
InstagramDMTask._start_v2ray_app()
    ↓
emulator_manager.get_native_api()
    ↓
native_api.run_app(emulator_id, "com.v2ray.ang")
    ↓
LeiDianNativeAPI._execute_command(['runapp', '--index', str(emulator_id), '--packagename', package_name])
    ↓
雷电模拟器原生API执行
```

---

## 📋 状态管理调用

### **模拟器状态变化：**
```
统一模拟器管理器._update_emulator_state(emulator_id, new_state)
    ↓
状态变化：排队中 → 启动中 → 运行中
    ↓
发射信号：emulator_state_changed.emit(emulator_id, old_state, new_state)
    ↓
UI更新：MainWindowV2接收状态变化信号
```

### **任务完成处理：**
```
InstagramDMTask.execute() 返回结果
    ↓
FixedAsyncBridge._emit_task_completed(emulator_id, 'instagram_dm', result)
    ↓
发射信号：task_finished.emit(emulator_id, task_type, result)
    ↓
UI更新：MainWindowV2接收任务完成信号
```

---

## 📋 错误处理调用

### **异常处理流程：**
```
任务执行异常
    ↓
InstagramDMTask.execute() 返回 {'status': 'failed', 'message': error}
    ↓
FixedAsyncBridge._emit_task_failed(emulator_id, 'instagram_dm', error_message)
    ↓
发射信号：task_failed.emit(emulator_id, task_type, error_message)
    ↓
UI更新：MainWindowV2接收任务失败信号
```

---

## 📋 配置获取调用

### **配置管理调用：**
```
任务类初始化
    ↓
config_manager.get("配置项名称", 默认值)
    ↓
UnifiedConfigManager.get(key, default)
    ↓
返回配置值
```

---

## 📋 V2Ray启动功能技术细节

### **关键参数和配置：**
```
V2Ray包名：com.v2ray.ang
雷电模拟器路径：G:/leidian/LDPlayer9/ldconsole.exe
启动命令格式：ldconsole.exe runapp --index {模拟器ID} --packagename {包名}
验证命令格式：ldconsole.exe adb --index {模拟器ID} --command "shell ps | grep {包名}"
```

### **实际执行的系统命令：**
```
启动V2Ray：
G:/leidian/LDPlayer9/ldconsole.exe runapp --index 1 --packagename com.v2ray.ang

验证V2Ray：
G:/leidian/LDPlayer9/ldconsole.exe adb --index 1 --command "shell ps | grep com.v2ray.ang"
```

### **测试结果验证：**
```
✅ 模拟器1启动：成功
✅ V2Ray启动命令执行：成功
✅ 雷电原生API调用：正常工作
✅ 命令行参数传递：正确
✅ 异步执行流程：无阻塞
⚠️ 应用启动验证：需要优化（进程检查可能需要更多时间）
```

### **错误处理机制：**
```
InstagramDMTask._start_v2ray_app() 异常处理
    ↓
捕获异常：except Exception as e
    ↓
记录错误：log_error(f"[模拟器{self.emulator_id}] 启动V2Ray应用异常: {str(e)}")
    ↓
返回失败：return False
    ↓
上层处理：_stage2_v2ray_connection() 返回 False
    ↓
任务终止：execute() 返回 {'status': 'failed', 'message': 'V2Ray连接失败'}
```

### **日志输出示例：**
```
2025-07-09 12:16:48 - InstagramDMTask - INFO - [模拟器1] 步骤2.1：启动V2Ray应用
2025-07-09 12:16:48 - InstagramDMTask - INFO - [模拟器1] 正在启动V2Ray应用: com.v2ray.ang
2025-07-09 12:16:48 - LeiDianNativeAPI - INFO - 模拟器 1 启动应用: com.v2ray.ang
2025-07-09 12:16:50 - InstagramDMTask - INFO - [模拟器1] V2Ray应用启动命令执行成功，等待界面加载
2025-07-09 12:16:53 - InstagramDMTask - INFO - [模拟器1] 验证V2Ray应用启动状态
2025-07-09 12:16:53 - InstagramDMTask - INFO - [模拟器1] 阶段二：V2Ray连接完成
```

---

## 🎯 总结

**关键调用特点：**
1. **UI触发** - 每个任务都有对应的UI触发方法
2. **异步桥梁** - 统一的任务处理和调度
3. **模拟器管理** - 集中的模拟器启动和状态管理
4. **任务执行** - 标准化的任务执行流程
5. **状态反馈** - 完整的状态变化和结果反馈

**V2Ray启动功能已实现的详细调用链：**
```
UI点击"Instagram私信"
    ↓
MainWindowV2._on_instagram_dm_start() [ui/main_window_v2.py:1663]
    ↓
获取选中模拟器：self.get_selected_emulators()
    ↓
准备任务数据：task_data = {'emulators': selected_emulators, 'task_type': 'instagram_dm'}
    ↓
异步桥梁调用：self.async_bridge.execute_operation('instagram_dm_task', task_data)
    ↓
FixedAsyncBridge.execute_operation() [core/async_bridge.py:165]
    ↓
FixedAsyncBridge._execute_async_operation('instagram_dm_task', data) [core/async_bridge.py:190]
    ↓
FixedAsyncBridge._handle_instagram_dm_task(data) [core/async_bridge.py:270]
    ↓
解析模拟器ID：emulator_ids = [emulator['id'] for emulator in data.get('emulators', [])]
    ↓
启动模拟器：await self.emulator_manager.batch_start_emulators(emulator_ids)
    ↓
UnifiedEmulatorManager.batch_start_emulators() [core/unified_emulator_manager.py:155]
    ↓
安排任务：self._schedule_instagram_tasks_after_startup(emulator_ids, data)
    ↓
FixedAsyncBridge._schedule_instagram_tasks_after_startup() [core/async_bridge.py:295]
    ↓
创建异步任务：asyncio.create_task(self._wait_and_execute_instagram_task(emulator_id, task_data))
    ↓
FixedAsyncBridge._wait_and_execute_instagram_task() [core/async_bridge.py:373]
    ↓
等待模拟器启动：await self._wait_for_emulator_startup_completion(emulator_id)
    ↓
模拟器启动完成后执行：await self._execute_instagram_task(emulator_id, task_data)
    ↓
FixedAsyncBridge._execute_instagram_task() [core/async_bridge.py:450]
    ↓
创建Instagram任务实例：task = InstagramDMTask(emulator_id) [✅已优化：任务类内部获取配置]
    ↓
任务初始化配置：task._load_config() [自动加载配置] ✅已实现
    ↓
注册配置观察者：task._register_config_observer() [支持热加载] ✅已实现
    ↓
执行任务：result = await task.execute()
    ↓
InstagramDMTask.execute() [core/instagram_task.py:60]
    ↓
阶段二V2Ray连接：await self._stage2_v2ray_connection()
    ↓
InstagramDMTask._stage2_v2ray_connection() [core/instagram_task.py:143]
    ↓
启动V2Ray应用：await self._start_v2ray_app()
    ↓
InstagramDMTask._start_v2ray_app() [core/instagram_task.py:245]
    ↓
获取原生API：native_api = self.emulator_manager.get_native_api()
    ↓
UnifiedEmulatorManager.get_native_api() [core/unified_emulator_manager.py:704]
    ↓
初始化原生API：self._native_api = LeiDianNativeAPI(ld_base_path)
    ↓
LeiDianNativeAPI.__init__() [core/native/base_api.py:45]
    ↓
启动V2Ray应用：start_result = await native_api.run_app(self.emulator_id, self.v2ray_package)
    ↓
LeiDianNativeAPI.run_app() [core/native/base_api.py:350]
    ↓
执行雷电命令：await self._execute_command(['runapp', '--index', str(emulator_id), '--packagename', package_name])
    ↓
LeiDianNativeAPI._execute_command() [core/native/base_api.py:100]
    ↓
构建完整命令：full_command = [self.ld_console_path] + command
    ↓
异步执行：process = await asyncio.create_subprocess_exec(*full_command, ...)
    ↓
雷电模拟器原生API执行：G:/leidian/LDPlayer9/ldconsole.exe runapp --index 1 --packagename com.v2ray.ang
    ↓
V2Ray应用启动成功 ✅
    ↓
验证V2Ray启动：await self._verify_v2ray_started()
    ↓
InstagramDMTask._verify_v2ray_started() [core/instagram_task.py:275]
    ↓
检查应用运行状态：is_running = await native_api.is_app_running(self.emulator_id, self.v2ray_package)
    ↓
LeiDianNativeAPI.is_app_running() [core/native/base_api.py:393]
    ↓
执行ADB命令：await self._execute_command(['adb', '--index', str(emulator_id), '--command', f'shell ps | grep {package_name}'])
    ↓
返回验证结果：V2Ray启动验证完成
```

---

## 📋 配置获取和热加载详细调用链（✅已优化）

### **配置获取调用链（任务类内部）：**
```
InstagramDMTask.__init__(emulator_id, config_manager=None)
    ↓
获取配置管理器：self.config_manager = config_manager or get_config_manager()
    ↓
加载配置：self._load_config()
    ↓
InstagramDMTask._load_config() [core/instagram_task.py:67]
    ↓
获取基础参数：self.message_count = self.config_manager.get("instagram_dm.message_count", 10)
    ↓
获取延迟参数：self.delay_min = self.config_manager.get("instagram_dm.delay_min", 5000)
    ↓
获取控制参数：self.recall_before_dm = self.config_manager.get("instagram_dm.recall_before_dm", False)
    ↓
获取消息内容：self.message_content_1 = self.config_manager.get("instagram_dm.message_content_1", "hi|hello|nice to meet you")
    ↓
配置加载完成：log_info(f"Instagram任务配置加载完成: 私信数量={self.message_count}")
```

### **配置热加载注册调用链：**
```
InstagramDMTask._register_config_observer() [core/instagram_task.py:125]
    ↓
注册观察者：self.config_manager.register_observer(self._on_config_changed)
    ↓
UnifiedConfigManager.register_observer() [core/simple_config.py]
    ↓
添加到观察者列表：self._observers.append(callback)
    ↓
观察者注册完成：log_info("Instagram任务配置热加载观察者已注册")
```

### **配置变化热加载调用链：**
```
配置文件变化：app_config.json 或 config_manager.set()
    ↓
ConfigHotReloadService检测到变化 [core/config_hot_reload.py]
    ↓
UnifiedConfigManager.reload() [core/simple_config.py]
    ↓
通知所有观察者：self._notify_observers(key, old_value, new_value)
    ↓
InstagramDMTask._on_config_changed(key, old_value, new_value) [core/instagram_task.py:143]
    ↓
检查配置键：if key.startswith("instagram_dm.")
    ↓
重新加载配置：self._load_config()
    ↓
配置热加载完成：log_info("Instagram任务配置已热加载更新")
```

### **任务结束时观察者注销调用链：**
```
InstagramDMTask.execute() 任务完成或异常
    ↓
InstagramDMTask.unregister_config_observer() [core/instagram_task.py:175]
    ↓
注销观察者：self.config_manager.unregister_observer(self._on_config_changed)
    ↓
UnifiedConfigManager.unregister_observer() [core/simple_config.py]
    ↓
从观察者列表移除：self._observers.remove(callback)
    ↓
观察者注销完成：log_info("Instagram任务配置观察者已注销")
```

---
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
## 📋 创建新任务的创建文件和方法对应关系

### **UI设计和界面文件：**
```
文件：ui/新任务名_ui.py
方法：
├── __init__(self, config_manager=None)     # 初始化UI界面
├── setup_ui()                              # 设置UI布局和控件
├── _on_start_clicked()                     # 开始按钮点击事件
├── _on_stop_clicked()                      # 停止按钮点击事件
├── update_status(status: str)              # 更新状态显示
├── get_task_config() -> dict               # 获取任务配置参数
└── 信号定义：
    ├── task_start_requested = pyqtSignal() # 任务开始请求信号
    └── task_stop_requested = pyqtSignal()  # 任务停止请求信号
```

### **参数配置和热加载文件（✅已优化模式）：**
```
文件：core/simple_config.py（统一配置管理）
方法：
├── get(key, default_value)                 # 获取配置参数
├── set(key, value)                         # 设置配置参数并自动保存
├── register_observer(callback)             # 注册配置变化观察者 ✅已实现
├── unregister_observer(callback)           # 注销配置变化观察者 ✅已实现
├── _notify_observers(key, old, new)        # 通知所有观察者 ✅已实现
├── save()                                  # 保存配置到app_config.json
├── load()                                  # 从app_config.json加载配置
└── reload()                                # 热加载配置（重新读取文件）

文件：core/新任务名_task.py（任务类内部配置管理）
方法：
├── _load_config()                          # 从统一配置系统加载配置 ✅已实现
├── _register_config_observer()             # 注册配置热加载观察者 ✅已实现
├── _on_config_changed(key, old, new)       # 配置变化处理回调 ✅已实现
├── reload_config()                         # 手动重新加载配置 ✅已实现
└── unregister_config_observer()            # 注销配置观察者 ✅已实现

文件：ui/新任务名_ui.py（参照instagram_dm_ui.py）
方法：
├── load_settings()                         # 加载任务配置到UI（从config_manager）
├── _on_setting_changed(key, value)         # 配置变化处理（自动保存）
├── 控件值变化信号连接到_on_setting_changed
└── 配置格式："新任务名.参数名" 如："douyin_follow.follow_count"
```

### **UI更新和状态管理文件：**
```
文件：ui/main_window_v2.py
方法：
├── _create_新任务名_page()                 # 创建新任务页面
├── _on_新任务名_start()                    # 新任务开始事件处理
├── _on_新任务名_stop()                     # 新任务停止事件处理
├── _update_新任务名_status()               # 更新新任务状态显示
├── _show_log(message)                      # 显示日志信息
└── 信号连接：
    ├── 新任务页面.task_start_requested.connect(self._on_新任务名_start)
    └── 新任务页面.task_stop_requested.connect(self._on_新任务名_stop)

文件：core/async_bridge.py（状态反馈）
方法：
├── _emit_task_completed()                  # 发射任务完成信号
├── _emit_task_failed()                     # 发射任务失败信号
├── _emit_task_progress()                   # 发射任务进度信号
└── 信号定义：
    ├── task_finished = pyqtSignal(int, str, dict)  # 任务完成信号
    ├── task_failed = pyqtSignal(int, str, str)     # 任务失败信号
    └── task_progress = pyqtSignal(int, str, dict)  # 任务进度信号
```

### **统一配置管理文件（参照Instagram私信模式）：**
```
文件：core/simple_config.py（添加新任务配置验证规则）
添加内容：
├── "新任务名.参数1": {"type": int, "default": 10, "min": 1, "max": 100}
├── "新任务名.参数2": {"type": str, "default": "默认值"}
├── "新任务名.参数3": {"type": bool, "default": False}
└── 在DEFAULT_CONFIG中添加新任务默认配置

文件：app_config.json（自动保存，无需手动创建）
自动添加：
└── "新任务名": {
    "参数1": 10,
    "参数2": "默认值",
    "参数3": false
}
```

### **新任务创建标准文件结构（✅已优化模式）：**
```
创建新任务只需要2个文件：
├── core/
│   ├── 新任务名_task.py                    # 任务业务逻辑实现（✅包含配置管理和热加载）
│   └── async_bridge.py                     # 添加异步处理方法（简化，不再处理配置）
└── ui/
    ├── 新任务名_ui.py                      # 任务UI界面设计
    └── main_window_v2.py                   # 集成到主窗口

配置管理（✅已优化）：
├── 任务类内部获取配置                        # 减少耦合，提高内聚
├── 自动注册配置热加载观察者                   # 支持运行时配置更新
├── 任务结束时自动注销观察者                   # 避免内存泄漏
├── core/simple_config.py                   # 统一配置管理（已存在）
└── app_config.json                         # 统一配置存储（自动保存）

配置热加载支持：
├── core/config_hot_reload.py               # 文件监控服务（已存在）
├── core/simple_config.py                  # 观察者模式（已存在）
└── 任务类自动热加载                         # 配置变化时自动更新（✅已实现）
```

---

## 📋 现有文件和方法对应关系

### **UI层方法：**
```
文件：ui/main_window_v2.py
方法：
├── _on_instagram_dm_start()                # Instagram私信任务触发
├── get_selected_emulators()                # 获取选中的模拟器
├── _create_instagram_dm_page()             # 创建Instagram私信页面
├── _show_log(message)                      # 显示日志信息
└── _get_current_timestamp()                # 获取当前时间戳
```

### **异步桥梁方法：**
```
文件：core/async_bridge.py
方法：
├── execute_operation(operation, data)                    # 统一操作入口
├── _execute_async_operation(operation, data)             # 异步操作分发
├── _handle_instagram_dm_task(data)                       # Instagram任务处理
├── _schedule_instagram_tasks_after_startup(ids, data)    # Instagram任务安排
├── _wait_and_execute_instagram_task(id, data)           # Instagram任务等待执行
├── _execute_instagram_task(id, data)                     # Instagram任务执行
├── _emit_task_completed()                                # 发射任务完成信号
├── _emit_task_failed()                                   # 发射任务失败信号
└── _wait_for_emulator_startup_completion()               # 等待模拟器启动完成
```

### **模拟器管理器方法：**
```
文件：core/unified_emulator_manager.py
方法：
├── batch_start_emulators(emulator_ids)                   # 批量启动模拟器
├── get_emulator_status(emulator_id)                      # 获取模拟器状态
├── get_native_api()                                      # 获取原生API
├── start_emulator(emulator_id)                           # 启动单个模拟器
├── stop_emulator(emulator_id)                            # 停止单个模拟器
└── release_emulator_slot(emulator_id)                    # 释放模拟器槽位
```

### **Instagram任务方法（✅已优化配置管理）：**
```
文件：core/instagram_task.py
方法：
├── __init__(emulator_id, config_manager=None)            # 初始化（✅优化：内部获取配置）
├── _load_config()                                        # 加载任务配置 ✅已实现
├── _register_config_observer()                           # 注册配置热加载观察者 ✅已实现
├── _on_config_changed(key, old_value, new_value)         # 配置变化处理回调 ✅已实现
├── reload_config()                                       # 手动重新加载配置 ✅已实现
├── unregister_config_observer()                          # 注销配置观察者 ✅已实现
├── execute()                                             # 主执行方法
├── _stage1_app_detection()                               # 阶段一：应用检测
├── _stage2_v2ray_connection()                            # 阶段二：V2Ray连接 ✅已实现
├── _start_v2ray_app()                                    # V2Ray启动 ✅已实现
├── _verify_v2ray_started()                               # V2Ray验证 ✅已实现
├── _stage3_instagram_launch()                            # 阶段三：Instagram启动
└── _stage4_mass_dm()                                     # 阶段四：批量私信发送
```

### **原生API方法：**
```
文件：core/native/base_api.py
方法：
├── run_app(emulator_id, package_name)                    # 启动应用 ✅已实现
├── is_app_running(emulator_id, package)                  # 检查应用运行状态 ✅已实现
├── kill_app(emulator_id, package_name)                   # 关闭应用
├── install_app(emulator_id, apk_path)                    # 安装应用
├── _execute_command(command)                             # 执行雷电命令 ✅已实现
└── get_emulator_list()                                   # 获取模拟器列表
```

### **配置管理方法（✅已优化热加载支持）：**
```
文件：core/simple_config.py
方法：
├── get(key, default_value)                               # 获取配置参数
├── set(key, value)                                       # 设置配置参数并自动保存
├── register_observer(callback)                           # 注册配置变化观察者 ✅已实现
├── unregister_observer(callback)                         # 注销配置变化观察者 ✅已实现
├── _notify_observers(key, old_value, new_value)          # 通知所有观察者 ✅已实现
├── save()                                                # 保存配置到app_config.json
├── load()                                                # 从app_config.json加载配置
├── reload()                                              # 热加载配置（重新读取文件）
└── get_all_configs()                                     # 获取所有配置

文件：core/config_hot_reload.py
方法：
├── start()                                               # 启动配置文件监控服务
├── stop()                                                # 停止配置文件监控服务
├── _check_config_changes()                               # 检查配置文件变化
└── _on_config_file_changed()                             # 配置文件变化处理
```
