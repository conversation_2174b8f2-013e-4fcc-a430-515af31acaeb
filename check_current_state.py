#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
当前状态检查工具
========================================
功能描述: 检查当前模拟器状态和界面信息
创建时间: 2025-07-23
作者: AI Assistant

使用方法:
1. 运行: python check_current_state.py
2. 程序会显示当前模拟器状态和界面信息
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.instagram_task import InstagramDMTask
from core.simple_config import get_config_manager
from core.logger_manager import log_info, log_error


class CurrentStateChecker:
    """当前状态检查器"""

    def __init__(self):
        """初始化检查器"""
        self.config_manager = get_config_manager()
        
    def check_emulator_status(self):
        """检查所有模拟器状态"""
        try:
            print("🔍 检查模拟器状态:")
            print("=" * 50)
            
            # 检查常用的模拟器ID
            emulator_ids = [1, 2, 3, 4, 5]
            
            for emulator_id in emulator_ids:
                try:
                    instagram_task = InstagramDMTask(emulator_id, self.config_manager)
                    is_running = instagram_task.ld.is_running(emulator_id)
                    
                    if is_running:
                        screen_size = instagram_task.ld.get_screen_size(emulator_id)
                        print(f"  模拟器 {emulator_id}: ✅ 运行中 (分辨率: {screen_size[0]}×{screen_size[1]})")
                    else:
                        print(f"  模拟器 {emulator_id}: ❌ 未运行")
                        
                except Exception as e:
                    print(f"  模拟器 {emulator_id}: ❌ 检查失败 ({e})")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查模拟器状态失败: {e}")
            return False

    async def check_messages_on_emulator(self, emulator_id):
        """检查指定模拟器上的消息"""
        try:
            print(f"\n🔍 检查模拟器 {emulator_id} 的消息:")
            print("=" * 50)
            
            instagram_task = InstagramDMTask(emulator_id, self.config_manager)
            
            # 检查模拟器是否运行
            if not instagram_task.ld.is_running(emulator_id):
                print(f"❌ 模拟器 {emulator_id} 未运行")
                return False
            
            # 获取屏幕信息
            screen_width, screen_height = instagram_task.ld.get_screen_size(emulator_id)
            print(f"📱 屏幕信息: {screen_width} × {screen_height}")
            
            # 获取消息元素
            message_elements = await instagram_task._find_all_message_elements()
            
            if not message_elements:
                print("📭 未找到任何消息")
                return True
            
            print(f"📨 找到 {len(message_elements)} 条消息:")
            print("-" * 50)
            
            for i, element in enumerate(message_elements):
                message_type = element.get('message_type', 'unknown')
                bounds = element.get('bounds', '')
                text = element.get('text', '')[:20] + '...' if len(element.get('text', '')) > 20 else element.get('text', '')
                center_x = element.get('center_x', 'N/A')
                center_y = element.get('center_y', 'N/A')
                
                print(f"  消息 {i+1}:")
                print(f"    类型: {message_type}")
                print(f"    边界: {bounds}")
                print(f"    中心: ({center_x}, {center_y})")
                if text:
                    print(f"    文本: '{text}'")
                print()
            
            return True
            
        except Exception as e:
            print(f"❌ 检查模拟器 {emulator_id} 消息失败: {e}")
            return False

    async def run_check(self):
        """运行完整检查"""
        try:
            print("🔍 当前状态检查工具")
            print("=" * 60)
            
            # 1. 检查所有模拟器状态
            self.check_emulator_status()
            
            # 2. 询问用户要检查哪个模拟器
            print("\n" + "=" * 60)
            emulator_input = input("请输入要检查的模拟器ID (1-5，默认2): ").strip()
            
            if not emulator_input:
                emulator_id = 2
            else:
                try:
                    emulator_id = int(emulator_input)
                    if emulator_id < 1 or emulator_id > 5:
                        print("❌ 模拟器ID必须在1-5之间")
                        return
                except ValueError:
                    print("❌ 请输入有效的数字")
                    return
            
            # 3. 检查指定模拟器的消息
            await self.check_messages_on_emulator(emulator_id)
            
            print("\n" + "=" * 60)
            print("✅ 检查完成")
            
        except Exception as e:
            print(f"❌ 运行检查异常: {e}")


async def main():
    """主函数"""
    try:
        checker = CurrentStateChecker()
        await checker.run_check()
        
    except KeyboardInterrupt:
        print("\n检查被用户中断")
    except Exception as e:
        print(f"检查异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
