# 🚀 企业级雷电模拟器中控系统

> 🎯 **专业的Android模拟器管理平台** - 为企业用户提供批量管理、自动化操作、性能监控的完整解决方案1

[![Python Version](https://img.shields.io/badge/python-3.12.9+-blue.svg)](https://python.org)
[![PyQt6](https://img.shields.io/badge/PyQt6-6.0+-green.svg)](https://pypi.org/project/PyQt6/)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Code Quality](https://img.shields.io/badge/code%20quality-enterprise-brightgreen.svg)](DEVELOPMENT_GUIDE.md)

---

## 📋 项目概述

### 🎯 核心价值
企业级雷电模拟器中控系统是一个专业的Android模拟器管理平台，专为企业用户设计，解决大规模模拟器管理的复杂性，提供**零卡顿**的用户体验和企业级的稳定性保障。

### ⭐ 核心功能
- **🎯 模拟器批量管理** - 支持数百台模拟器的统一管理和监控
- **🚀 Instagram自动化操作** - 智能化的社交媒体自动化解决方案
- **🔧 代理服务器管理** - 企业级代理池管理和智能切换
- **📊 性能监控与统计** - 实时性能监控和详细数据分析
- **🛡️ 企业级安全保障** - 多层安全防护和数据保护机制

### 🏗️ 技术特性
- **🎨 现代化架构** - 基于Python + PyQt6的企业级架构设计
- **⚡ 事件驱动** - 异步事件总线确保系统响应性和可扩展性
- **🛡️ UI线程安全** - 严格的UI线程保护，确保零卡顿体验（< 16ms响应）
- **🔧 模块化设计** - 清晰的分层架构，易于维护和扩展
- **📝 完善监控** - 全方位的日志记录和错误处理机制

---

## 🚀 快速开始

### 📋 环境要求
- **操作系统**: Windows 10/11 (64位)
- **Python版本**: Python 3.12.9+ 
- **模拟器**: 雷电模拟器 9.0+
- **内存**: 建议8GB以上
- **存储**: 至少2GB可用空间

### ⚡ 快速安装

#### 1. 获取项目
```bash
git clone [项目地址]
cd leidainzhong
```

#### 2. 一键初始化（推荐）
```bash
# 🚀 运行快速启动脚本（自动完成所有设置）
python quick_start.py
```

#### 3. 手动配置（可选）
```bash
# 安装Python依赖
pip install -r requirements.txt

# 运行开发前检查
python pre_development_check.py

# 设置Git钩子（确保代码质量）
python setup_mandatory_hooks.py
```

#### 4. 开始开发（强制性入口）
```bash
# 🚀 强制性开发入口（每次开发前必须运行）
python start_development.py "开发任务描述"

# 示例：
python start_development.py "实现模拟器管理功能"
python start_development.py "修复UI阻塞问题"
python start_development.py "添加代理管理模块"
```

#### 📋 开发入口功能
- ✅ **强制检查开发标准文档** - 确保已阅读DEVELOPMENT_GUIDE.md和DEV_PLAN.md
- ✅ **验证开发环境** - 自动运行pre_development_check.py
- ✅ **检查质量工具** - 验证所有质量检查工具可用
- ✅ **验证Git钩子** - 确保自动化质量检查正常
- ✅ **启动实时监控** - 自动启动UI阻塞和功能分组监控
- ✅ **记录开发会话** - 跟踪开发活动和标准遵循情况

---

## 🏗️ 项目架构

### 📁 完整文件结构
```
📁 企业级雷电模拟器中控系统/
├── 📁 ui/                   # 用户界面层
│   ├── main_window_v2.py       # 主窗口界面
│   ├── emulator_management_ui.py # 模拟器管理UI
│   ├── settings_ui.py          # 系统设置界面
│   ├── styled_widgets.py       # 自定义控件库
│   ├── state_manager.py        # UI状态管理器
│   └── ui_integration.py       # UI集成管理器
├── 📁 business/             # 业务逻辑层
│   ├── emulator_operation_handler.py # 模拟器操作处理器
│   ├── 📁 instagram/           # Instagram自动化模块
│   │   ├── follow_engine.py       # 关注引擎
│   │   ├── message_engine.py      # 私信引擎
│   │   ├── register_engine.py     # 注册引擎
│   │   └── automation_coordinator.py # 自动化协调器
│   └── 📁 v2ray/              # V2Ray代理模块
│       ├── proxy_engine.py        # 代理引擎
│       ├── node_manager.py        # 节点管理器
│       └── connection_pool.py     # 连接池管理
├── 📁 data/                 # 数据访问层
│   ├── database_manager.py     # 数据库管理器
│   ├── 📁 models/              # 数据模型
│   │   ├── emulator_model.py      # 模拟器数据模型
│   │   ├── instagram_model.py     # Instagram数据模型
│   │   └── proxy_model.py         # 代理数据模型
│   └── 📁 repositories/        # 数据仓库
│       ├── emulator_repository.py # 模拟器数据仓库
│       └── config_repository.py   # 配置数据仓库
├── 📁 system/               # 系统调用层
│   ├── unified_emulator_manager.py # 统一模拟器管理器
│   ├── path_validator.py       # 路径验证服务
│   ├── leidian_manager.py      # 底层命令执行工具
│   ├── process_manager.py      # 进程管理器
│   ├── file_manager.py         # 文件系统操作
│   └── network_manager.py      # 网络通信管理
├── 📁 core/                 # 核心工具层
│   ├── event_bus.py            # 企业级事件总线
│   ├── logger_manager.py       # 日志管理器
│   ├── performance_monitor.py  # 性能监控器
│   ├── thread_manager.py       # 线程管理器
│   ├── config_manager.py       # 配置管理器
│   ├── ui_update_manager.py    # UI更新管理器
│   ├── async_business_layer.py # 异步业务层
│   └── emulator_status_monitor.py # 模拟器状态监控
├── 📁 utils/                # 工具类
│   ├── error_diagnostic_logger.py # 错误诊断日志
│   ├── startup_flow_diagnostics.py # 启动流程诊断
│   ├── constants.py            # 常量定义
│   └── (其他工具类)
├── 📁 tests/                # 测试文件
├── 📁 config/               # 配置文件
├── 📁 logs/                 # 日志目录
├── 📁 docs/                 # 项目文档
│   ├── README.md               # 项目总览和用户指南
│   ├── DEVELOPMENT_GUIDE.md    # 完整开发指南
│   ├── DEV_PLAN.md            # 开发计划和路线图
│   └── ARCHITECTURE_PLAN.md    # 架构设计和计划
├── main.py                  # 主程序入口
├── 📁 质量保障工具/          # 企业级质量检查工具
│   ├── ui_blocking_detector.py     # 实时UI阻塞检测工具
│   ├── check_code_cleanup.py       # 代码清理检查工具
│   ├── check_function_grouping.py  # 功能分组检查工具
│   ├── check_layer_compliance.py   # 分层架构检查工具
│   ├── ui_blocking_ci_check.py     # 提交前UI阻塞检查工具
│   ├── setup_mandatory_hooks.py    # Git钩子设置工具
│   ├── git_quality_enforcer.py     # Git质量执行器
│   └── git_hooks_manager.py        # Git钩子管理器
├── requirements.txt         # 依赖列表
└── requirements_python312.txt # Python 3.12专用依赖
```

### 分层架构设计
```
┌─────────────────┐
│   UI层 (ui/)    │ ← 用户界面，事件处理
├─────────────────┤
│ 业务层(business/)│ ← 业务逻辑，流程控制  
├─────────────────┤
│ 数据层 (data/)  │ ← 数据访问，持久化
├─────────────────┤
│ 系统层(system/) │ ← 系统调用，外部接口
├─────────────────┤
│ 核心层 (core/)  │ ← 基础设施，工具类
└─────────────────┘
```

---

## 🛠️ 开发指南

### 📚 必读文档
在开始开发前，请务必完整阅读以下文档：

1. **[完整开发指南](DEVELOPMENT_GUIDE.md)** 
   - 强制性开发检查清单
   - 代码质量标准和规范
   - UI线程安全指南
   - 架构合规性要求

2. **[开发计划](DEV_PLAN.md)** 
   - 详细的开发路线图
   - 任务分解和优先级
   - 里程碑和时间规划
   - 技术实现方案

### 🎯 代码质量要求

#### 🚨 UI线程安全标准（最高优先级 - 零容忍政策）
- **🚫 绝对禁止**: 在UI线程中使用任何阻塞操作（time.sleep、subprocess.run、同步网络请求等）
- **⚡ 响应时间**: UI响应必须 < 16ms（60 FPS标准）- **确保UI阻塞问题永远不会再次出现**
- **🔄 强制异步**: 所有耗时操作必须使用异步处理，通过事件总线或线程池执行
- **🛡️ 实时检测**: 每次修改UI代码后必须运行UI阻塞检测，100%通过检测
- **⚠️ 零容忍**: 任何导致UI卡顿的代码将被立即拒绝，无例外

#### 🏗️ 代码结构与组织标准（强制执行）

##### 1. 📁 对应代码放在对应文件
- **UI层代码**: 只能放在 `ui/` 目录，禁止在其他层写UI相关代码
- **业务逻辑**: 只能放在 `business/` 目录，禁止在UI层写业务逻辑
- **数据访问**: 只能放在 `data/` 目录，禁止跨层直接访问数据库
- **系统调用**: 只能放在 `system/` 目录，禁止在上层直接调用系统API
- **工具函数**: 只能放在 `core/` 或 `utils/` 目录

##### 2. 🎯 结构清晰：所有文件都有清晰的功能分组
```python
# ========================================
# 🎯 [功能组名称] - 必须标注
# ========================================
# 功能描述: [详细描述该功能组的作用和职责]
# 主要方法: [列出核心方法名称]
# 调用关系: [说明与其他组件的调用关系]
# 注意事项: [重要的使用注意事项和限制]
# ========================================
```

##### 3. 📊 层级分明：方法按功能和调用关系组织
- **初始化方法组**: `__init__`, `setup_*`, `init_*`
- **核心业务方法组**: 按业务功能分组
- **事件处理方法组**: `on_*`, `handle_*`
- **UI更新方法组**: `update_*`, `refresh_*`
- **工具方法组**: `_private_methods`, `utility_methods`

##### 4. 🔧 易于维护：开发者可以快速定位和修改代码
- **统一命名规范**: 方法名必须清晰表达功能
- **完整文档字符串**: 每个方法必须有详细的参数说明和返回值说明
- **清晰的依赖关系**: 明确标注方法间的调用关系
- **错误处理**: 每个方法必须有适当的异常处理

#### 🚫 修改代码注意：禁止只增不删（强制要求）
- **删除冗余代码**: 修改时必须删除不再使用的代码
- **合并重复逻辑**: 发现重复代码必须提取为公共方法
- **清理无用导入**: 删除不再使用的import语句
- **移除调试代码**: 删除所有print、console.log等调试语句
- **统一验证逻辑**: 避免多套检查逻辑，使用统一的验证服务

#### 强制性标准
- **📏 函数长度**: ≤ 50行（建议），≤ 100行（强制）
- **🏗️ 分层架构**: 严格遵循UI→业务→数据→系统→核心的分层设计
- **📝 完整文档**: 所有函数必须有完整的文档字符串和注释
- **🔧 功能分组**: 所有代码文件必须使用标准化的功能分组注释格式

### 🔧 开发工具链

#### 🚀 强制性开发流程（确保遵循企业级标准）
```bash
# 🚀 每次开发前必须运行（强制性入口）
python start_development.py "任务描述"

# 示例：
python start_development.py "实现模拟器管理功能"
python start_development.py "修复UI阻塞问题"

# 📊 管理开发会话
python development_session_manager.py --status    # 查看活跃会话
python development_session_manager.py --report 7  # 生成7天报告
python development_session_manager.py --stop-all  # 停止所有会话
```

#### 🛠️ 手动质量检查工具（可选）
```bash
# 🔧 开发前检查（确保环境正确）
python pre_development_check.py

# 🚨 UI阻塞检测（极其重要 - 确保UI阻塞问题永远不会再次出现）
python ui_blocking_detector.py --ci  # 持续集成模式
python ui_blocking_detector.py --watch  # 实时监控模式

# 🚫 代码清理检查（禁止只增不删）
python check_code_cleanup.py

# 🎯 功能分组检查（确保所有文件都有清晰的功能分组）
python check_function_grouping.py
python check_function_grouping.py --watch  # 实时监控模式

# 🏗️ 分层架构检查（确保对应代码放在对应文件）
python check_layer_compliance.py

# 🚨 提交前UI阻塞检查（零容忍政策）
python ui_blocking_ci_check.py
```

#### 🛡️ Git钩子工具（自动化质量保障）
```bash
# 🔧 设置Git钩子（一次性设置）
python setup_mandatory_hooks.py

# 🔧 管理Git钩子
python git_hooks_manager.py --install  # 安装钩子
python git_hooks_manager.py --check    # 检查健康状态
python git_hooks_manager.py --repair   # 修复损坏的钩子

# 🛡️ 质量执行器（钩子核心）
python git_quality_enforcer.py --stage pre-commit
python git_quality_enforcer.py --stage pre-push --report
```

#### 🛡️ 企业级开发流程（强制执行）
```bash
# 1. 项目初始化（一次性）
python quick_start.py  # 或者手动执行下面的命令
python pre_development_check.py
python setup_mandatory_hooks.py

# 2. 每次开发前（强制性）
python start_development.py "具体任务描述"
# 此命令会：
# - 检查开发标准文档（DEVELOPMENT_GUIDE.md, DEV_PLAN.md）
# - 验证开发环境和质量工具
# - 确认遵循企业级标准
# - 自动启动实时监控
# - 记录开发会话

# 3. Git操作（自动执行质量检查）
# git commit  # 自动执行: UI阻塞检测 + 代码清理 + 功能分组检查
# git push    # 自动执行: 分层架构检查 + UI阻塞CI检查

# 4. 结束开发（Ctrl+C停止监控）
# 自动停止实时监控，记录开发会话统计
```

#### ⚠️ 违规检测与处理
- **UI阻塞违规**: 立即停止开发，修复后才能继续（零容忍政策）
- **结构违规**: 代码必须重新组织，确保对应代码放在对应文件
- **功能分组缺失**: 必须添加标准化功能分组注释
- **只增不删违规**: 必须清理冗余代码，删除重复逻辑
- **架构违规**: Git推送被阻止，必须修复架构问题

---

## 📖 用户指南

### 🎯 模拟器管理
- **🚀 批量启动/停止** - 一键管理多个模拟器实例
- **📊 实时状态监控** - 实时显示每个模拟器的运行状态
- **🔧 自动配置管理** - 智能检测和配置模拟器参数
- **📝 操作日志记录** - 详细记录所有操作历史

### 🚀 Instagram自动化
- **🔐 智能登录管理** - 安全的账号登录和会话管理
- **🤖 批量操作处理** - 支持多账号并发自动化操作
- **🛡️ 防检测机制** - 智能模拟人工操作，降低检测风险
- **📊 操作统计分析** - 详细的操作数据统计和分析

### 🔧 代理管理
- **🌐 多协议支持** - 支持HTTP、HTTPS、SOCKS5等多种代理协议
- **🔄 智能切换机制** - 自动检测代理质量并智能切换
- **📊 质量监控评估** - 实时监控代理速度、稳定性和可用性
- **⚡ 故障自动转移** - 代理失效时自动切换到备用代理

---

## 📞 技术支持

### 问题反馈
- **GitHub Issues**: 提交bug报告和功能请求
- **技术交流**: 加入开发者社区讨论
- **文档贡献**: 帮助完善项目文档

### 文档资源
- **开发指南**: [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md)
- **开发计划**: [DEV_PLAN.md](DEV_PLAN.md)
- **架构设计**: [ARCHITECTURE_PLAN.md](ARCHITECTURE_PLAN.md)
- **项目总览**: [README.md](README.md)（本文档）

---

**🎯 记住：质量是我们的生命线，用户体验是我们的追求！**
