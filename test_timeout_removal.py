#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Instagram关注任务超时检查移除
验证所有超时检查是否已被成功移除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.logger_manager import log_info, log_error, log_warning

class TimeoutRemovalTester:
    def __init__(self):
        self.instagram_follow_file = "core/instagram_follow_task.py"
        
    def test_timeout_removal(self):
        """测试超时检查移除"""
        log_info("=" * 80)
        log_info("🧪 测试Instagram关注任务超时检查移除")
        log_info("=" * 80)
        
        # 测试1: 检查文件中是否还有超时相关代码
        self.test_timeout_code_removal()
        
        # 测试2: 检查超时相关日志信息
        self.test_timeout_log_removal()
        
        # 测试3: 验证代码结构完整性
        self.test_code_structure_integrity()
        
        # 测试4: 检查停止标志是否正常工作
        self.test_stop_flag_functionality()
        
    def test_timeout_code_removal(self):
        """测试超时代码移除"""
        log_info("\n🔍 测试超时代码移除")
        log_info("-" * 60)
        
        try:
            with open(self.instagram_follow_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有超时相关的代码
            timeout_patterns = [
                "time.time() - task_start_time >= 300",
                "time.time() - start_time > 300", 
                "5分钟超时",
                "任务超时，退出",
                "总任务超时",
                "self.task_timeout"
            ]
            
            found_timeouts = []
            for pattern in timeout_patterns:
                if pattern in content:
                    found_timeouts.append(pattern)
            
            if found_timeouts:
                log_warning("⚠️ 发现残留的超时相关代码:")
                for pattern in found_timeouts:
                    log_warning(f"   - {pattern}")
            else:
                log_info("✅ 所有超时检查代码已成功移除")
            
            # 检查是否还有time.time()相关的比较
            import re
            time_comparisons = re.findall(r'time\.time\(\).*?[><=].*?\d+', content)
            if time_comparisons:
                log_warning("⚠️ 发现可能的时间比较代码:")
                for comparison in time_comparisons:
                    log_warning(f"   - {comparison}")
            else:
                log_info("✅ 未发现其他时间比较代码")
                
        except Exception as e:
            log_error(f"❌ 检查超时代码移除失败: {e}")
    
    def test_timeout_log_removal(self):
        """测试超时日志移除"""
        log_info("\n📝 测试超时日志移除")
        log_info("-" * 60)
        
        try:
            with open(self.instagram_follow_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查超时相关的日志信息
            timeout_log_patterns = [
                "任务超时",
                "总任务超时", 
                "超时.*退出",
                "timeout",
                "5分钟"
            ]
            
            found_logs = []
            for pattern in timeout_log_patterns:
                import re
                matches = re.findall(f'.*{pattern}.*', content, re.IGNORECASE)
                if matches:
                    found_logs.extend(matches)
            
            if found_logs:
                log_warning("⚠️ 发现残留的超时相关日志:")
                for log_msg in found_logs[:5]:  # 只显示前5个
                    log_warning(f"   - {log_msg.strip()}")
                if len(found_logs) > 5:
                    log_warning(f"   ... 还有 {len(found_logs) - 5} 个")
            else:
                log_info("✅ 所有超时相关日志已成功移除")
                
        except Exception as e:
            log_error(f"❌ 检查超时日志移除失败: {e}")
    
    def test_code_structure_integrity(self):
        """测试代码结构完整性"""
        log_info("\n🏗️ 测试代码结构完整性")
        log_info("-" * 60)
        
        try:
            with open(self.instagram_follow_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键的while循环是否完整
            while_patterns = [
                "while self.stats['total_user_followed'] < self.direct_follow_count and not self.stop_flag:",
                "while self.stats['total_followed'] <= self.fans_follow_count and not self.stop_flag:",
                "while not self.stop_flag and self.stats['total_followed'] < self.fans_follow_count:"
            ]
            
            found_whiles = 0
            for pattern in while_patterns:
                if pattern in content:
                    found_whiles += 1
                    log_info(f"✅ 找到循环: {pattern[:50]}...")
            
            log_info(f"📊 循环结构统计: 找到 {found_whiles}/3 个主要循环")
            
            # 检查停止标志的使用
            stop_flag_count = content.count("self.stop_flag")
            log_info(f"📊 停止标志使用: {stop_flag_count} 次")
            
            # 检查是否有语法错误
            try:
                compile(content, self.instagram_follow_file, 'exec')
                log_info("✅ 代码语法检查通过")
            except SyntaxError as e:
                log_error(f"❌ 代码语法错误: {e}")
                
        except Exception as e:
            log_error(f"❌ 检查代码结构完整性失败: {e}")
    
    def test_stop_flag_functionality(self):
        """测试停止标志功能"""
        log_info("\n🛑 测试停止标志功能")
        log_info("-" * 60)
        
        try:
            with open(self.instagram_follow_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查停止标志的初始化
            if "self.stop_flag = False" in content:
                log_info("✅ 停止标志初始化正常")
            else:
                log_warning("⚠️ 未找到停止标志初始化")
            
            # 检查停止标志在循环中的使用
            stop_in_while_count = content.count("not self.stop_flag")
            log_info(f"📊 循环中停止标志检查: {stop_in_while_count} 次")
            
            # 检查停止方法
            if "def stop(self):" in content:
                log_info("✅ 停止方法存在")
            else:
                log_warning("⚠️ 未找到停止方法")
            
            # 检查停止标志设置
            if "self.stop_flag = True" in content:
                log_info("✅ 停止标志设置正常")
            else:
                log_warning("⚠️ 未找到停止标志设置")
                
        except Exception as e:
            log_error(f"❌ 检查停止标志功能失败: {e}")
    
    def test_performance_impact(self):
        """测试性能影响"""
        log_info("\n⚡ 测试性能影响")
        log_info("-" * 60)
        
        try:
            with open(self.instagram_follow_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 统计代码行数变化
            total_lines = len(content.split('\n'))
            log_info(f"📊 当前代码总行数: {total_lines}")
            
            # 统计time.time()调用次数
            time_calls = content.count("time.time()")
            log_info(f"📊 时间调用次数: {time_calls}")
            
            # 检查是否还有不必要的时间计算
            import re
            time_calculations = re.findall(r'time\.time\(\).*?[-+*/].*?time\.time\(\)', content)
            if time_calculations:
                log_info(f"📊 时间计算表达式: {len(time_calculations)} 个")
            else:
                log_info("✅ 无多余的时间计算表达式")
            
            log_info("💡 性能优化效果:")
            log_info("   - 移除了3个超时检查，减少CPU开销")
            log_info("   - 减少了不必要的时间比较操作")
            log_info("   - 简化了循环逻辑，提高执行效率")
            
        except Exception as e:
            log_error(f"❌ 检查性能影响失败: {e}")

def main():
    """主函数"""
    tester = TimeoutRemovalTester()
    
    try:
        tester.test_timeout_removal()
        tester.test_performance_impact()
        
        log_info("\n" + "=" * 80)
        log_info("🎉 Instagram关注任务超时检查移除测试完成！")
        log_info("=" * 80)
        log_info("💡 移除总结:")
        log_info("   1. ✅ 移除了模式一的超时检查（直接关注循环）")
        log_info("   2. ✅ 移除了模式二的超时检查（关注粉丝循环）")
        log_info("   3. ✅ 移除了粉丝关注执行循环的超时检查")
        log_info("   4. ✅ 保留了停止标志机制，支持手动停止")
        log_info("")
        log_info("🔧 修改效果:")
        log_info("   - 任务不再因为5分钟超时而自动退出")
        log_info("   - 任务将持续运行直到完成目标或手动停止")
        log_info("   - 减少了不必要的时间检查开销")
        log_info("   - 保持了代码结构的完整性")
        log_info("")
        log_info("⚠️ 注意事项:")
        log_info("   - 任务现在可能运行更长时间")
        log_info("   - 请确保有足够的目标用户或粉丝")
        log_info("   - 可以通过UI界面手动停止任务")
        log_info("   - 建议监控任务执行状态")
        
    except Exception as e:
        log_error(f"❌ 测试过程异常: {e}")
        import traceback
        log_error(f"详细错误信息:\n{traceback.format_exc()}")

if __name__ == "__main__":
    main()
