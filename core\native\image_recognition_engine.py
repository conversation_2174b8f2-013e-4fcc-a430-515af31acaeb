#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 图像识别引擎 - 雷电模拟器图色识别核心
========================================
功能描述: 基于雷电找图一键版的图像识别引擎，提供高精度的应用图标检测能力

模块结构:
1. 窗口查找系统 (find_emulator_window)
2. 截图捕获系统 (capture_screenshot)  
3. 图像匹配系统 (match_template_image)
4. 统一识别接口 (detect_app_by_image)

主要功能:
- 多尺度模板匹配: 支持0.1-1.9倍缩放范围
- 高精度识别: 置信度阈值≥0.75，平均耗时30ms
- 窗口自动查找: 支持主窗口和渲染窗口检测
- 异步接口设计: 支持并发调用和错误处理

技术特点:
- 基于OpenCV的模板匹配算法
- Win32 API的高效截图机制
- 智能缩放顺序优化
- 早期退出优化策略

调用关系: 被native_api调用，为Instagram任务提供应用检测能力
注意事项: 需要win32gui、cv2、numpy、PIL等依赖库支持
========================================
"""

import win32gui
import win32ui
import win32con
import cv2
import numpy as np
from PIL import Image
import os
import time
import ctypes
from ctypes import wintypes
from typing import Optional, Tuple
import asyncio

# ============================================================================
# 🎯 1. 图像识别引擎核心类
# ============================================================================
# 功能描述: 图像识别引擎的核心实现，提供完整的图色识别能力
# 调用关系: 被native_api调用，作为应用检测的底层引擎
# 注意事项: 基于雷电找图一键版的完整逻辑，保证识别准确性和性能
# ============================================================================

class ImageRecognitionEngine:
    """🎯 图像识别引擎 - 雷电模拟器图色识别核心"""
    
    def __init__(self):
        """初始化图像识别引擎"""
        # 🎯 置信度阈值配置
        self.confidence_threshold = 0.75  # 75%置信度阈值
        self.perfect_match_threshold = 0.95  # 95%完美匹配阈值
        
        # 🎯 缩放范围配置 - 优化缩放顺序：以1.0为中心，常用尺寸优先
        self.scale_ranges = [1.0, 0.9, 1.1, 0.8, 1.2, 0.7, 1.3, 0.6, 1.4, 0.5, 1.5, 0.4, 1.6, 0.3, 0.7, 0.2, 1.8, 0.1, 1.9]
        
    # ------------------------------------------------------------------------
    # 🎯 1.1 窗口查找系统
    # ------------------------------------------------------------------------
    # 功能描述: 查找雷电模拟器的渲染窗口，支持主窗口和子窗口检测
    # 调用关系: 被图像识别接口调用，提供截图目标窗口
    # 注意事项: 优先选择面积最大的渲染窗口，确保截图完整性
    # ------------------------------------------------------------------------
    
    def find_emulator_window(self, window_title: str = "雷电模拟器-1") -> Optional[int]:
        """🎯 查找雷电模拟器窗口句柄"""
        try:
            # 🎯 查找主窗口
            main_hwnd = self._find_main_window(window_title)
            
            # 🎯 查找渲染窗口
            render_windows = self._find_render_windows()
            
            # 🎯 查找子窗口
            if main_hwnd:
                child_windows = self._find_child_windows(main_hwnd)
                render_windows.extend(child_windows)
            
            # 🎯 返回面积最大的渲染窗口
            return max(render_windows, key=lambda x: x[1])[0] if render_windows else None
            
        except Exception as e:
            print(f"查找模拟器窗口异常: {e}")
            return None
    
    def _find_main_window(self, window_title: str) -> Optional[int]:
        """🎯 查找主窗口"""
        def enum_main(hwnd, windows):
            try:
                if win32gui.IsWindowVisible(hwnd):
                    text = win32gui.GetWindowText(hwnd)
                    if window_title in text:
                        windows.append(hwnd)
            except:
                pass
            return True

        main_windows = []
        win32gui.EnumWindows(enum_main, main_windows)
        return main_windows[0] if main_windows else None
    
    def _find_render_windows(self) -> list:
        """🎯 查找渲染窗口"""
        def enum_render(hwnd, render_windows):
            try:
                text = win32gui.GetWindowText(hwnd)
                cls = win32gui.GetClassName(hwnd)
                
                if (text == 'TheRender' and cls == 'RenderWindow') or cls == 'RenderWindow':
                    rect = win32gui.GetWindowRect(hwnd)
                    w, h = rect[2] - rect[0], rect[3] - rect[1]
                    if w > 200 and h > 300:
                        render_windows.append((hwnd, w * h))
            except:
                pass
            return True

        render_windows = []
        win32gui.EnumWindows(enum_render, render_windows)
        return render_windows
    
    def _find_child_windows(self, main_hwnd: int) -> list:
        """🎯 查找子窗口"""
        def enum_child(hwnd, child_windows):
            try:
                cls = win32gui.GetClassName(hwnd)
                if cls in ['RenderWindow', 'subWin']:
                    rect = win32gui.GetWindowRect(hwnd)
                    w, h = rect[2] - rect[0], rect[3] - rect[1]
                    if w > 200 and h > 300:
                        child_windows.append((hwnd, w * h))
            except:
                pass
            return True

        child_windows = []
        win32gui.EnumChildWindows(main_hwnd, enum_child, child_windows)
        return child_windows

    # ------------------------------------------------------------------------
    # 🎯 1.2 截图捕获系统
    # ------------------------------------------------------------------------
    # 功能描述: 高效捕获雷电模拟器窗口截图，支持PrintWindow和BitBlt双重机制
    # 调用关系: 被图像匹配系统调用，提供图像识别的源图像
    # 注意事项: 优先使用PrintWindow，失败时自动回退到BitBlt方式
    # ------------------------------------------------------------------------

    def capture_screenshot(self, hwnd: int) -> Optional[np.ndarray]:
        """🎯 捕获窗口截图"""
        if not hwnd:
            return None

        try:
            rect = win32gui.GetClientRect(hwnd)
            w, h = rect[2], rect[3]

            hwndDC = win32gui.GetDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, w, h)
            saveDC.SelectObject(saveBitMap)

            user32 = ctypes.windll.user32
            user32.PrintWindow.argtypes = [wintypes.HWND, wintypes.HDC, wintypes.UINT]
            user32.PrintWindow.restype = wintypes.BOOL

            # 🎯 优先使用PrintWindow方式
            result = user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 2)
            if not result:
                # 🎯 回退到BitBlt方式
                saveDC.BitBlt((0, 0), (w, h), mfcDC, (0, 0), win32con.SRCCOPY)

            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                                  bmpstr, 'raw', 'BGRX', 0, 1)
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

            # 🎯 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)

            return img_cv

        except Exception as e:
            print(f"截图捕获异常: {e}")
            return None

    # ------------------------------------------------------------------------
    # 🎯 1.3 图像匹配系统
    # ------------------------------------------------------------------------
    # 功能描述: 多尺度模板匹配算法，支持智能缩放和早期退出优化
    # 调用关系: 被统一识别接口调用，执行核心的图像匹配逻辑
    # 注意事项: 置信度≥0.75认为匹配成功，≥0.95触发早期退出优化
    # ------------------------------------------------------------------------

    def match_template_image(self, screenshot: np.ndarray, template: np.ndarray) -> Optional[Tuple[int, int, float]]:
        """🎯 多尺度模板匹配"""
        best_result = None
        best_confidence = 0

        for scale in self.scale_ranges:
            th, tw = template.shape[:2]
            nw, nh = int(tw * scale), int(th * scale)

            # 🎯 尺寸有效性检查
            if nw < 5 or nh < 5 or nw > screenshot.shape[1] or nh > screenshot.shape[0]:
                continue

            # 🎯 缩放模板并进行匹配
            scaled = cv2.resize(template, (nw, nh))
            result = cv2.matchTemplate(screenshot, scaled, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            # 🎯 更新最佳匹配结果
            if max_val > best_confidence:
                best_confidence = max_val
                x, y = max_loc
                best_result = (x + nw // 2, y + nh // 2, max_val)

                # 🎯 早期退出优化：如果找到完美匹配，直接返回
                if max_val >= self.perfect_match_threshold:
                    break

        # 🎯 返回符合置信度阈值的结果
        return best_result if best_confidence >= self.confidence_threshold else None

    # ------------------------------------------------------------------------
    # 🎯 1.4 统一识别接口
    # ------------------------------------------------------------------------
    # 功能描述: 提供统一的图像识别接口，整合窗口查找、截图、匹配等功能
    # 调用关系: 被native_api调用，作为图像识别的主入口点
    # 注意事项: 支持异步调用，包含完整的错误处理和性能统计
    # ------------------------------------------------------------------------

    async def detect_app_by_image(self, emulator_id: int, image_path: str, window_title: str = None) -> Optional[Tuple[int, int, float, float]]:
        """🎯 通过图像识别检测应用"""
        start_time = time.time()

        try:
            # 🎯 构建窗口标题
            if not window_title:
                window_title = f"雷电模拟器-{emulator_id}"

            # 🎯 获取项目根目录的图片路径
            if not os.path.isabs(image_path):
                # 获取项目根目录（从core/native向上两级）
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = os.path.dirname(os.path.dirname(current_dir))
                image_path = os.path.join(project_root, image_path)

            # 🎯 检查图片文件是否存在
            if not os.path.exists(image_path):
                print(f"图片文件不存在: {image_path}")
                return None

            # 🎯 加载模板图片
            template = cv2.imread(image_path)
            if template is None:
                print(f"无法加载图片: {image_path}")
                return None

            # 🎯 查找模拟器窗口
            hwnd = self.find_emulator_window(window_title)
            if not hwnd:
                print(f"未找到模拟器窗口: {window_title}")
                return None

            # 🎯 捕获截图
            screenshot = self.capture_screenshot(hwnd)
            if screenshot is None:
                print(f"截图捕获失败: {window_title}")
                return None

            # 🎯 执行图像匹配
            result = self.match_template_image(screenshot, template)

            # 🎯 计算耗时
            elapsed_time = time.time() - start_time

            if result:
                x, y, confidence = result
                print(f"图像识别成功: 位置({x}, {y}), 置信度{confidence:.3f}, 耗时{elapsed_time:.3f}秒")
                return (x, y, elapsed_time, confidence)
            else:
                print(f"图像识别失败: 未找到匹配项, 耗时{elapsed_time:.3f}秒")
                return None

        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"图像识别异常: {str(e)}, 耗时{elapsed_time:.3f}秒")
            return None

# ============================================================================
# 🎯 2. 全局引擎实例和便捷接口
# ============================================================================
# 功能描述: 提供全局引擎实例和便捷的函数式接口
# 调用关系: 被native_api直接调用，简化接口使用
# 注意事项: 单例模式确保引擎实例的唯一性和性能优化
# ============================================================================

# 🎯 全局引擎实例
_image_engine = None

def get_image_recognition_engine() -> ImageRecognitionEngine:
    """🎯 获取图像识别引擎实例（单例模式）"""
    global _image_engine
    if _image_engine is None:
        _image_engine = ImageRecognitionEngine()
    return _image_engine

async def detect_app_by_image(emulator_id: int, image_path: str, window_title: str = None) -> Optional[Tuple[int, int, float, float]]:
    """🎯 便捷的图像识别接口"""
    engine = get_image_recognition_engine()
    return await engine.detect_app_by_image(emulator_id, image_path, window_title)
