"""
🎯 任务活动检测心跳管理器 - 简化版
功能：基于任务活动的智能检测，只使用软重启
核心原则：
1. 检测任务活动状态，两阶段确认异常
2. 失败计数永久保留，不重置
3. 软重启后恢复任务执行
4. 达到失败次数上限执行熔断
"""

import asyncio
import time
import subprocess
import os
from pathlib import Path
from typing import Dict, Any
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

from .simple_config import get_config_manager
from .logger_manager import log_info, log_error, log_emulator
from .status_converter import HeartbeatStatus


class TaskActivityHeartbeatManager(QObject):
    """🎯 任务活动检测心跳管理器 - 简化版"""

    # 信号定义 - 保持现有信号兼容性
    heartbeat_success = pyqtSignal(int)  # 任务活动正常信号
    heartbeat_failed = pyqtSignal(int, str, int)  # 任务活动异常信号 (emulator_id, reason, failure_count)
    emulator_recovered = pyqtSignal(int, str)  # 模拟器恢复信号
    emulator_switched = pyqtSignal(int, int)  # 模拟器切换信号 (old_id, new_id)

    def __init__(self):
        super().__init__()
        self.config_manager = get_config_manager()

        # 🎯 任务活动检测配置参数
        self.check_interval = self.config_manager.get("monitoring.task_check_interval", 10)  # 检测间隔10秒
        self.response_timeout = self.config_manager.get("monitoring.task_response_timeout", 11)  # 响应超时11秒
        self.max_failures = self.config_manager.get("monitoring.failure_count", 3)  # 失败触发次数

        # 🎯 监控状态 - 核心数据结构
        self.running = False
        self.monitored_emulators: Dict[int, Dict] = {}  # {emulator_id: {failure_count, last_activity_time, status, observation_start_time}}

        # 🎯 任务活动检测定时器
        self.activity_timer = QTimer()
        self.activity_timer.timeout.connect(self._check_all_emulators)

        # 🎯 注册配置观察者 - 统一配置变更处理，支持热加载
        self.config_manager.register_observer(self._on_config_changed)

        # 🎯 基于日志的心跳检测
        self.log_based_detection = self.config_manager.get("monitoring.log_based_detection", True)
        if self.log_based_detection:
            self._setup_log_based_heartbeat()

        log_info("任务活动检测心跳管理器初始化完成", component="TaskActivityHeartbeatManager")

    def _on_config_changed(self, key: str, old_value: Any, new_value: Any):
        """🎯 配置变更处理 - 统一配置热加载，确保UI线程安全"""
        try:
            # 🎯 任务活动监控开关变更处理
            if key == "monitoring.task_activity_enabled":
                log_info(f"检测到任务活动监控配置变更: {old_value} -> {new_value}", component="TaskActivityHeartbeatManager")

                # 🎯 确保在主线程中执行定时器操作
                from PyQt6.QtCore import QTimer, QThread
                if QThread.currentThread() != self.thread():
                    QTimer.singleShot(0, lambda: self._apply_activity_enabled_change(new_value))
                else:
                    self._apply_activity_enabled_change(new_value)

            # 🎯 检测间隔变更处理
            elif key == "monitoring.task_check_interval":
                log_info(f"检测间隔配置变更: {old_value}s -> {new_value}s", component="TaskActivityHeartbeatManager")
                self.check_interval = new_value
                if self.running:
                    # 重启定时器以应用新间隔
                    self.activity_timer.stop()
                    # 限制最大值防止溢出
                    interval_ms = min(int(self.check_interval * 1000), 2147483647)
                    self.activity_timer.start(interval_ms)

            # 🎯 响应超时变更处理
            elif key == "monitoring.task_response_timeout":
                log_info(f"响应超时配置变更: {old_value}s -> {new_value}s", component="TaskActivityHeartbeatManager")
                self.response_timeout = new_value

        except Exception as e:
            log_error(f"处理配置变更失败: {key}, 错误: {e}", component="TaskActivityHeartbeatManager")

    def _setup_log_based_heartbeat(self):
        """设置基于日志的心跳检测 - 根据模拟器ID跟踪日志活动"""
        try:
            from .logger_manager import get_logger_manager
            logger_manager = get_logger_manager()

            # 🎯 连接模拟器日志信号 - 按模拟器ID分别跟踪
            logger_manager.emulator_log_updated.connect(self._on_emulator_log_activity)

            log_info("心跳检测已启用", component="TaskActivityHeartbeatManager")
        except Exception as e:
            log_error(f"设置基于日志的心跳检测失败: {e}", component="TaskActivityHeartbeatManager")

    def _on_emulator_log_activity(self, emulator_id: int, log_message: str):
        """处理模拟器日志活动 - 根据模拟器ID自动更新心跳"""
        try:
            # 🎯 只处理正在监控的模拟器
            if emulator_id > 0 and emulator_id in self.monitored_emulators:
                # 🎯 过滤心跳检测自身的日志，避免循环
                if "心跳" not in log_message and "heartbeat" not in log_message.lower():
                    # 🎯 根据模拟器ID更新对应的心跳活动时间
                    self.update_heartbeat_activity(emulator_id)

        except Exception as e:
            # 静默处理，避免日志循环
            pass

    def _apply_activity_enabled_change(self, enabled: bool):
        """🎯 应用任务活动监控开关变更 - 统一控制逻辑"""
        if enabled and not self.running:
            self.start_monitoring()
            log_info("任务活动监控已启用（配置热加载）", component="TaskActivityHeartbeatManager")
        elif not enabled and self.running:
            self.stop_monitoring()
            log_info("任务活动监控已禁用（配置热加载）", component="TaskActivityHeartbeatManager")
        else:
            current_state = "启用" if self.running else "禁用"
            log_info(f"任务活动监控状态无变化，当前: {current_state}", component="TaskActivityHeartbeatManager")

    # ========================================================================
    # 🎯 监控服务管理
    # ========================================================================

    def start_monitoring(self):
        """启动任务活动监控服务"""
        if self.running:
            return
        self.running = True
        # 限制最大值防止溢出 (2147483647 / 1000 = 2147483)
        interval_ms = min(int(self.check_interval * 1000), 2147483647)
        self.activity_timer.start(interval_ms)
        log_info(f"心跳监控服务已启动，检测间隔: {self.check_interval}秒", component="TaskActivityHeartbeatManager")

    def stop_monitoring(self):
        """停止任务活动监控服务"""
        self.running = False
        self.activity_timer.stop()
        self.monitored_emulators.clear()
        log_info("心跳监控服务已停止", component="TaskActivityHeartbeatManager")

    def add_emulator_monitoring(self, emulator_id: int):
        """添加模拟器到监控列表 - 保持现有失败计数"""
        # 保持现有的失败计数，永不重置
        existing_failure_count = 0
        if emulator_id in self.monitored_emulators:
            existing_failure_count = self.monitored_emulators[emulator_id].get('failure_count', 0)

        current_time = time.time()
        self.monitored_emulators[emulator_id] = {
            'failure_count': existing_failure_count,  # 保持历史失败计数
            'last_activity_time': current_time,  # 最后活动时间
            'status': HeartbeatStatus.NORMAL,  # 当前状态
            'observation_start_time': None,  # 观察期开始时间
            'in_recovery': False  # 是否在恢复中
        }
        log_info(f"模拟器 {emulator_id} 已添加到任务活动监控，失败计数: {existing_failure_count}",
                component="TaskActivityHeartbeatManager")

    def remove_emulator_monitoring(self, emulator_id: int):
        """从监控列表移除模拟器"""
        if emulator_id in self.monitored_emulators:
            del self.monitored_emulators[emulator_id]
        log_info(f"模拟器 {emulator_id} 已从任务活动监控移除", component="TaskActivityHeartbeatManager")

    def update_heartbeat_activity(self, emulator_id: int):
        """更新模拟器心跳活动时间 - 在所有心跳操作中调用"""
        if emulator_id in self.monitored_emulators:
            current_time = time.time()
            emulator_info = self.monitored_emulators[emulator_id]
            emulator_info['last_activity_time'] = current_time

            # 如果之前是疑似异常状态，恢复为正常
            if emulator_info['status'] == HeartbeatStatus.SUSPECTED:
                emulator_info['status'] = HeartbeatStatus.NORMAL
                emulator_info['observation_start_time'] = None
                log_info(f"模拟器 {emulator_id} 心跳活动恢复正常", component="TaskActivityHeartbeatManager")

            # 移除常规心跳更新日志，减少冗余输出
            # log_info(f"模拟器 {emulator_id} 心跳活动时间已更新", component="TaskActivityHeartbeatManager")

    # ========================================================================
    # 🎯 心跳检测核心逻辑
    # ========================================================================

    def _check_all_emulators(self):
        """检查所有监控中的模拟器心跳状态"""
        if not self.running or not self.monitored_emulators:
            return

        log_info(f"开始检测 {len(self.monitored_emulators)} 个模拟器的心跳状态", component="TaskActivityHeartbeatManager")

        # 使用异步桥梁执行检测
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(0, lambda: self._start_async_check())

    def _start_async_check(self):
        """启动异步检测任务"""
        try:
            from .async_bridge import get_async_bridge
            async_bridge = get_async_bridge()
            async_bridge.execute_operation("heartbeat_check", self._async_check_emulators)
        except Exception as e:
            log_error(f"启动异步检测失败: {e}", component="TaskActivityHeartbeatManager")

    async def _async_check_emulators(self):
        """异步检测所有模拟器的心跳状态"""
        current_time = time.time()

        for emulator_id in list(self.monitored_emulators.keys()):
            if emulator_id not in self.monitored_emulators:
                continue

            emulator_info = self.monitored_emulators[emulator_id]

            # 跳过正在恢复中的模拟器
            if emulator_info.get('in_recovery', False):
                continue

            # 执行心跳状态检测
            await self._check_heartbeat_status(emulator_id, emulator_info, current_time)

    async def _check_heartbeat_status(self, emulator_id: int, emulator_info: Dict, current_time: float):
        """检测模拟器心跳状态 - 两阶段检测逻辑"""
        try:
            last_activity_time = emulator_info.get('last_activity_time', current_time)
            status = emulator_info.get('status', HeartbeatStatus.NORMAL)
            observation_start_time = emulator_info.get('observation_start_time')

            # 计算距离最后活动的时间
            time_since_last_activity = current_time - last_activity_time

            if status == HeartbeatStatus.NORMAL:
                # 正常状态：检查是否超过检测间隔没有活动
                if time_since_last_activity > self.check_interval:
                    # 进入疑似异常状态，开始观察期
                    emulator_info['status'] = HeartbeatStatus.SUSPECTED
                    emulator_info['observation_start_time'] = current_time
                    log_info(f"模拟器 {emulator_id} 疑似心跳异常，进入观察期 | 无活动时间: {time_since_last_activity:.1f}秒",
                            component="TaskActivityHeartbeatManager")
                else:
                    # 正常状态，发送成功信号
                    self.heartbeat_success.emit(emulator_id)

            elif status == HeartbeatStatus.SUSPECTED:
                # 疑似异常状态：检查观察期是否结束
                observation_duration = current_time - observation_start_time

                if observation_duration >= self.response_timeout:
                    # 观察期结束，仍无活动，确认异常
                    emulator_info['status'] = HeartbeatStatus.CONFIRMED_ABNORMAL
                    log_error(f"模拟器 {emulator_id} 确认心跳异常 | 总无活动时间: {time_since_last_activity:.1f}秒",
                             component="TaskActivityHeartbeatManager")
                    await self._handle_heartbeat_failure(emulator_id)
                else:
                    # 仍在观察期内
                    remaining_time = self.response_timeout - observation_duration
                    log_info(f"模拟器 {emulator_id} 观察期中 | 剩余观察时间: {remaining_time:.1f}秒",
                            component="TaskActivityHeartbeatManager")

        except Exception as e:
            log_error(f"心跳检测异常 | emulator_id: {emulator_id} | error: {e}", component="TaskActivityHeartbeatManager")

    async def _handle_heartbeat_failure(self, emulator_id: int):
        """处理心跳异常 - 只使用软重启"""
        if emulator_id not in self.monitored_emulators:
            return

        emulator_info = self.monitored_emulators[emulator_id]
        emulator_info['failure_count'] += 1  # 失败计数递增，永不重置
        emulator_info['in_recovery'] = True
        emulator_info['status'] = HeartbeatStatus.RECOVERING

        failure_count = emulator_info['failure_count']

        log_emulator(f"心跳异常处理开始，失败次数: {failure_count}/{self.max_failures}",
                    emulator_id=emulator_id, component="TaskActivityHeartbeatManager")

        # 发送心跳失败信号
        self.heartbeat_failed.emit(emulator_id, "心跳异常", failure_count)

        # 截图记录异常状态
        await self._capture_failure_screenshot(emulator_id, f"心跳异常_第{failure_count}次失败")

        if failure_count >= self.max_failures:
            # 达到失败次数上限 - 熔断（熔断会移除监控，无需设置in_recovery）
            await self._execute_circuit_break(emulator_id)
        else:
            # 执行软重启
            try:
                await self._execute_soft_restart(emulator_id, failure_count)
            finally:
                # 软重启完成后重置恢复状态
                if emulator_id in self.monitored_emulators:
                    self.monitored_emulators[emulator_id]['in_recovery'] = False

    # ========================================================================
    # 🎯 软重启恢复策略实现
    # ========================================================================

    async def _execute_soft_restart(self, emulator_id: int, failure_count: int):
        """执行软重启 - 心跳异常处理"""
        try:
            log_emulator(f"开始软重启", emulator_id=emulator_id, component="TaskActivityHeartbeatManager")

            emulator_path = self.config_manager.get("emulator_path", "")
            if not emulator_path:
                self.heartbeat_failed.emit(emulator_id, "软重启失败: 模拟器路径未配置", failure_count)
                return

            ldconsole_path = Path(emulator_path) / "ldconsole.exe"
            cmd = [str(ldconsole_path), "restart", "--index", str(emulator_id)]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            _, stderr = await asyncio.wait_for(process.communicate(), timeout=10)

            if process.returncode == 0:
                log_emulator(f"软重启命令执行成功", emulator_id=emulator_id, component="TaskActivityHeartbeatManager")

                # 等待5秒后恢复任务
                await asyncio.sleep(5)
                await self._restore_task_after_restart(emulator_id, failure_count)
            else:
                error_msg = stderr.decode() if stderr else "软重启命令失败"
                self.heartbeat_failed.emit(emulator_id, f"软重启失败: {error_msg}", failure_count)

        except Exception as e:
            self.heartbeat_failed.emit(emulator_id, f"软重启异常: {e}", failure_count)

    async def _restore_task_after_restart(self, emulator_id: int, failure_count: int):
        """软重启后恢复任务执行"""
        try:
            if emulator_id in self.monitored_emulators:
                emulator_info = self.monitored_emulators[emulator_id]
                current_time = time.time()

                # 重置状态为正常，更新活动时间
                emulator_info['status'] = HeartbeatStatus.NORMAL
                emulator_info['last_activity_time'] = current_time
                emulator_info['observation_start_time'] = None

                # 软重启成功，但保持失败计数不变
                self.emulator_recovered.emit(emulator_id, "软重启成功，任务已恢复")

                log_emulator(f"软重启成功，任务已恢复", emulator_id=emulator_id, component="TaskActivityHeartbeatManager")
        except Exception as e:
            log_error(f"恢复任务失败: {e}", component="TaskActivityHeartbeatManager")
            self.heartbeat_failed.emit(emulator_id, f"任务恢复失败: {e}", failure_count)



    async def _execute_circuit_break(self, emulator_id: int):
        """执行熔断 - 达到失败次数上限处理"""
        try:
            log_emulator(f"开始熔断处理", emulator_id=emulator_id, component="TaskActivityHeartbeatManager")

            # 1. 强制关闭模拟器
            await self._force_close_emulator(emulator_id)

            # 2. 设置状态为异常
            await self._mark_emulator_abnormal(emulator_id)

            # 3. 从任务活动监控中移除
            self.remove_emulator_monitoring(emulator_id)

            # 4. 检查任务接力
            await self._check_task_relay(emulator_id)

            self.emulator_switched.emit(emulator_id, -1)

        except Exception as e:
            log_error(f"熔断处理异常: {e}", component="TaskActivityHeartbeatManager")

    # ========================================================================
    # 🎯 辅助方法
    # ========================================================================

    async def _force_close_emulator(self, emulator_id: int):
        """强制关闭模拟器"""
        try:
            emulator_path = self.config_manager.get("emulator_path", "")
            if emulator_path:
                ldconsole_path = Path(emulator_path) / "ldconsole.exe"
                cmd = [str(ldconsole_path), "quit", "--index", str(emulator_id)]

                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                await process.communicate()

        except Exception as e:
            log_error(f"强制关闭模拟器失败: {e}", component="TaskActivityHeartbeatManager")

    async def _mark_emulator_abnormal(self, emulator_id: int):
        """标记模拟器为异常状态 - 安全释放槽位"""
        try:
            from .unified_emulator_manager import get_emulator_manager
            from .status_converter import EmulatorStatus

            emulator_manager = get_emulator_manager()
            if hasattr(emulator_manager, 'startup_manager') and emulator_manager.startup_manager:
                startup_manager = emulator_manager.startup_manager

                # 🎯 安全清理所有相关状态
                removed_from_running = False
                removed_from_active = False

                # 🎯 关键修复：使用release_emulator_slot来正确释放槽位并触发任务接力
                if emulator_id in startup_manager.running_emulators:
                    startup_manager.release_emulator_slot(emulator_id)
                    removed_from_running = True

                if emulator_id in startup_manager.active_startups:
                    del startup_manager.active_startups[emulator_id]
                    removed_from_active = True

                # 🎯 更新状态为异常
                startup_manager._update_emulator_state(emulator_id, EmulatorStatus.ABNORMAL)

                log_emulator(f"模拟器已标记为异常并清理状态",
                            emulator_id=emulator_id,
                            removed_from_running=removed_from_running,
                            removed_from_active=removed_from_active,
                            total_running=len(startup_manager.running_emulators),
                            total_active=len(startup_manager.active_startups),
                            component="TaskActivityHeartbeatManager")

        except Exception as e:
            log_error(f"标记模拟器异常状态失败: {e}", component="TaskActivityHeartbeatManager")

    async def _check_task_relay(self, emulator_id: int):
        """检查任务接力 - 已通过release_emulator_slot自动触发"""
        try:
            from .unified_emulator_manager import get_emulator_manager
            emulator_manager = get_emulator_manager()

            if hasattr(emulator_manager, 'startup_manager') and emulator_manager.startup_manager:
                # 检查是否有排队的模拟器（仅用于日志记录）
                if emulator_manager.startup_manager.startup_queue:
                    next_emulator = emulator_manager.startup_manager.startup_queue[0]
                    log_info(f"发现排队模拟器 {next_emulator}，开始任务接力", component="TaskActivityHeartbeatManager")
                    # 注意：实际的任务接力已经通过release_emulator_slot自动触发
                else:
                    log_info(f"没有排队等待接力的模拟器", component="TaskActivityHeartbeatManager")

        except Exception as e:
            log_error(f"检查任务接力失败: {e}", component="TaskActivityHeartbeatManager")

    async def _capture_failure_screenshot(self, emulator_id: int, error_type: str):
        """捕获异常截图"""
        try:
            from .screenshot_manager import get_screenshot_manager
            screenshot_manager = get_screenshot_manager()

            # 获取当前模拟器的失败次数
            failure_count = 1
            if emulator_id in self.monitored_emulators:
                failure_count = self.monitored_emulators[emulator_id].get('failure_count', 1)

            await screenshot_manager.capture_failure_screenshot(emulator_id, error_type, failure_count)

        except Exception as e:
            log_error(f"捕获异常截图失败: {e}", component="TaskActivityHeartbeatManager")


# ========================================================================
# 🎯 全局实例管理
# ========================================================================

_task_activity_heartbeat_manager = None

def get_simple_heartbeat_manager() -> TaskActivityHeartbeatManager:
    """获取任务活动检测心跳管理器实例 - 保持函数名兼容性"""
    global _task_activity_heartbeat_manager
    if _task_activity_heartbeat_manager is None:
        _task_activity_heartbeat_manager = TaskActivityHeartbeatManager()
    return _task_activity_heartbeat_manager

# 🎯 新的函数名，更准确地反映功能
def get_task_activity_heartbeat_manager() -> TaskActivityHeartbeatManager:
    """获取任务活动检测心跳管理器实例"""
    return get_simple_heartbeat_manager()

# 🎯 全局心跳活动更新函数 - 在所有心跳操作中调用
def update_emulator_heartbeat_activity(emulator_id: int):
    """更新模拟器心跳活动时间 - 全局函数，在所有心跳操作中调用"""
    try:
        global _task_activity_heartbeat_manager
        if _task_activity_heartbeat_manager is not None:
            _task_activity_heartbeat_manager.update_heartbeat_activity(emulator_id)
        else:
            # 如果管理器还未初始化，先获取实例
            manager = get_simple_heartbeat_manager()
            manager.update_heartbeat_activity(emulator_id)
    except Exception as e:
        log_error(f"更新心跳活动时间失败: {e}", component="HeartbeatActivityUpdate")

# 🎯 保持向后兼容性的别名函数
def update_emulator_task_activity(emulator_id: int):
    """向后兼容性别名 - 请使用 update_emulator_heartbeat_activity"""
    update_emulator_heartbeat_activity(emulator_id)
