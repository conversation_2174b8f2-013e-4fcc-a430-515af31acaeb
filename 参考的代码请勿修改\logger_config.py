# _*_coding: utf_8*_
# logger_config.py
import logging

def setup_logger(name, level=logging.INFO, debug_mode=False):
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # 禁用日志传播，防止冲突
    logger.propagate = False

    # 检查是否已添加处理器，防止重复
    if not logger.handlers:
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level if not debug_mode else logging.DEBUG)
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # 文件处理器
        file_handler = logging.FileHandler(f'{name}.log')
        file_handler.setLevel(level if not debug_mode else logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger

# 使用示例
# main_logger = setup_logger("MainWindow", debug_mode=True)
# main_logger.info("通过自定义 logger 测试日志是否打印")